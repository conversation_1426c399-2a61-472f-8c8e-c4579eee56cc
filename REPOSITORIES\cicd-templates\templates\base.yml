# Base GitLab CI/CD Template - Simplified Version
# This template provides basic stages and configurations

# Default image
default:
  image: alpine:latest

# Global variables
variables:
  PIPELINE_VERSION: "1.0.0"

# Simple pipeline stages
stages:
  - validate
  - test

# Base job template
.base_job:
  before_script:
    - echo "Starting job $CI_JOB_NAME"
  after_script:
    - echo "Completed job $CI_JOB_NAME"

# Validation job template
validate_pipeline:
  stage: validate
  extends: .base_job
  script:
    - echo "Pipeline validation passed"
