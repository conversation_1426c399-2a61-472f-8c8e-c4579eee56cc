"""
Simple ExLog Dashboard API Client

This module provides a simple, synchronous API client that follows the exact
pattern from the working debug_api_format.py test file.
"""

import json
import logging
import time
import uuid
import socket
from datetime import datetime
from typing import Dict, List, Any, Optional
import threading
import queue

try:
    import requests
except ImportError:
    requests = None


class SimpleExLogAPIClient:
    """
    Simple synchronous API client for sending logs to ExLog dashboard.
    Uses the same pattern as the working test file.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the API client with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Check for required dependencies
        if not requests:
            raise ImportError("Required dependency missing. Please install: pip install requests")
        
        # API configuration
        self.endpoint = config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 5)
        
        # Batch configuration
        self.batch_size = config.get('batch_size', 10)
        self.max_batch_wait_time = config.get('max_batch_wait_time', 5)
        
        # Validation configuration
        validation_config = config.get('validation', {})
        self.fix_missing_fields = validation_config.get('fix_missing_fields', True)
        self.default_source = validation_config.get('default_source', 'System')
        self.default_source_type = validation_config.get('default_source_type', 'event')
        self.default_log_level = validation_config.get('default_log_level', 'info')
        
        # Internal state
        self._running = False
        self._batch_queue = queue.Queue()
        self._send_thread = None
        
        # Statistics
        self.stats = {
            'logs_sent': 0,
            'logs_failed': 0,
            'batches_sent': 0,
            'batches_failed': 0,
            'api_errors': 0,
            'last_successful_send': None,
            'last_error': None
        }
        
        self.logger.info(f"Simple API client configured: {self.endpoint}")
    
    def start(self) -> None:
        """Start the API client and background processing."""
        if self._running:
            return
            
        self._running = True
        
        # Start background thread for sending batches
        self._send_thread = threading.Thread(target=self._batch_sender_loop, daemon=True)
        self._send_thread.start()
        
        self.logger.info("Simple API client started")
        
        # Test connection
        self._test_connection()
    
    def stop(self) -> None:
        """Stop the API client and cleanup resources."""
        if not self._running:
            return
            
        self._running = False
        
        # Process remaining batches
        self._process_remaining_batches()
        
        self.logger.info("Simple API client stopped")
    
    def send_logs(self, logs: List[Dict[str, Any]]) -> None:
        """Queue logs for sending to the API."""
        if not logs:
            return
            
        # Validate and fix logs
        validated_logs = []
        for log in logs:
            validated_log = self._validate_and_fix_log(log)
            if validated_log:
                validated_logs.append(validated_log)
        
        if not validated_logs:
            return
        
        # Add to batch queue
        for log in validated_logs:
            try:
                self._batch_queue.put_nowait(log)
            except queue.Full:
                self.logger.warning("Batch queue is full, dropping log")
                self.stats['logs_failed'] += 1
    
    def _validate_and_fix_log(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate and fix a log entry to match ExLog API schema."""
        try:
            # Create a copy to avoid modifying original
            fixed_log = log.copy()
            
            # Ensure required fields exist
            if not fixed_log.get('log_id'):
                if self.fix_missing_fields:
                    fixed_log['log_id'] = str(uuid.uuid4())
                else:
                    self.logger.warning("Log missing required field 'log_id'")
                    return None
            
            if not fixed_log.get('timestamp'):
                if self.fix_missing_fields:
                    fixed_log['timestamp'] = datetime.now().isoformat()
                else:
                    self.logger.warning("Log missing required field 'timestamp'")
                    return None
            
            if not fixed_log.get('source'):
                if self.fix_missing_fields:
                    fixed_log['source'] = self.default_source
                else:
                    self.logger.warning("Log missing required field 'source'")
                    return None
            
            if not fixed_log.get('source_type'):
                if self.fix_missing_fields:
                    fixed_log['source_type'] = self.default_source_type
                else:
                    self.logger.warning("Log missing required field 'source_type'")
                    return None
            
            if not fixed_log.get('host'):
                if self.fix_missing_fields:
                    fixed_log['host'] = socket.gethostname()
                else:
                    self.logger.warning("Log missing required field 'host'")
                    return None
            
            if not fixed_log.get('log_level'):
                if self.fix_missing_fields:
                    fixed_log['log_level'] = self.default_log_level
                else:
                    self.logger.warning("Log missing required field 'log_level'")
                    return None
            
            if not fixed_log.get('message'):
                if self.fix_missing_fields:
                    fixed_log['message'] = "No message provided"
                else:
                    self.logger.warning("Log missing required field 'message'")
                    return None
            
            # Ensure additional_fields exists (can be empty)
            if 'additional_fields' not in fixed_log:
                fixed_log['additional_fields'] = {}

            # Ensure raw_data exists (can be None)
            if 'raw_data' not in fixed_log:
                fixed_log['raw_data'] = None

            # Additional validation checks for common issues
            # Check for very long strings that might cause validation errors
            if len(fixed_log.get('message', '')) > 10000:
                self.logger.warning(f"Message too long ({len(fixed_log['message'])} chars), truncating")
                fixed_log['message'] = fixed_log['message'][:10000] + "... [truncated]"

            # Check for invalid characters in log_id
            log_id = fixed_log.get('log_id', '')
            if not log_id.replace('-', '').replace('_', '').isalnum():
                self.logger.warning(f"Invalid log_id format: {log_id}, generating new one")
                fixed_log['log_id'] = str(uuid.uuid4())

            # Ensure timestamp is in proper format
            timestamp = fixed_log.get('timestamp', '')
            if timestamp and not timestamp.endswith('Z') and '+' not in timestamp:
                # Add Z if no timezone specified
                if 'T' in timestamp and len(timestamp) >= 19:
                    fixed_log['timestamp'] = timestamp + 'Z'

            # Validate log_level values
            valid_levels = ['critical', 'error', 'warning', 'info', 'debug']
            if fixed_log.get('log_level', '').lower() not in valid_levels:
                self.logger.warning(f"Invalid log_level: {fixed_log.get('log_level')}, using 'info'")
                fixed_log['log_level'] = 'info'

            # Validate source_type values
            valid_source_types = ['event', 'application', 'security', 'network', 'audit', 'performance']
            if fixed_log.get('source_type', '').lower() not in valid_source_types:
                self.logger.warning(f"Invalid source_type: {fixed_log.get('source_type')}, using 'event'")
                fixed_log['source_type'] = 'event'

            # Clean up source field - remove special characters and simplify
            source = fixed_log.get('source', '')
            if source:
                # Map complex source names to simple valid values
                if 'microsoft-windows' in source.lower() or 'operational' in source.lower():
                    fixed_log['source'] = 'System'
                elif 'application' in source.lower():
                    fixed_log['source'] = 'Application'
                elif 'security' in source.lower():
                    fixed_log['source'] = 'Security'
                elif 'network' in source.lower():
                    fixed_log['source'] = 'Network'
                elif source not in ['System', 'Application', 'Security', 'Network', 'Custom']:
                    # For any other complex source, use Application as default
                    self.logger.warning(f"Complex source name '{source}' simplified to 'Application'")
                    fixed_log['source'] = 'Application'

            # Clean up message field - remove problematic Unicode characters
            message = fixed_log.get('message', '')
            if message:
                # Replace common Unicode characters that might cause issues
                message = message.replace('\u00ae', '(R)')  # Registered trademark
                message = message.replace('\u2122', '(TM)')  # Trademark
                message = message.replace('\u00a9', '(C)')   # Copyright
                # Remove other non-ASCII characters that might cause issues
                message = message.encode('ascii', 'ignore').decode('ascii')
                fixed_log['message'] = message

            # Clean up additional_fields to remove problematic data
            if isinstance(fixed_log.get('additional_fields'), dict):
                additional_fields = fixed_log['additional_fields']

                # Clean string_inserts if they contain Unicode characters
                if 'string_inserts' in additional_fields and isinstance(additional_fields['string_inserts'], list):
                    cleaned_inserts = []
                    for insert in additional_fields['string_inserts']:
                        if isinstance(insert, str):
                            # Clean Unicode characters from string inserts
                            cleaned_insert = insert.replace('\u00ae', '(R)').replace('\u2122', '(TM)').replace('\u00a9', '(C)')
                            cleaned_insert = cleaned_insert.encode('ascii', 'ignore').decode('ascii')
                            cleaned_inserts.append(cleaned_insert)
                        else:
                            cleaned_inserts.append(insert)
                    additional_fields['string_inserts'] = cleaned_inserts

            return fixed_log
            
        except Exception as e:
            self.logger.error(f"Error validating log: {e}")
            return None
    
    def _batch_sender_loop(self) -> None:
        """Background thread loop for sending batches."""
        batch = []
        last_batch_time = time.time()
        
        while self._running:
            try:
                # Try to get a log from the queue
                try:
                    log = self._batch_queue.get(timeout=1.0)
                    batch.append(log)
                except queue.Empty:
                    pass
                
                current_time = time.time()
                
                # Send batch if it's full or max wait time exceeded
                should_send = (
                    len(batch) >= self.batch_size or
                    (batch and (current_time - last_batch_time) >= self.max_batch_wait_time)
                )
                
                if should_send and batch:
                    self._send_batch(batch)
                    batch = []
                    last_batch_time = current_time
                    
            except Exception as e:
                self.logger.error(f"Error in batch sender loop: {e}")
                time.sleep(1)
        
        # Send any remaining logs
        if batch:
            self._send_batch(batch)
    
    def _send_batch(self, logs: List[Dict[str, Any]]) -> bool:
        """Send a batch of logs to the API using the same method as the test file."""
        if not logs:
            return False
        
        # Debug: Log the first few log IDs being sent
        log_ids = [log.get('log_id', 'unknown') for log in logs[:3]]
        self.logger.debug(f"Sending batch of {len(logs)} logs, first few IDs: {log_ids}")
        
        # Prepare payload (exactly like the test file)
        payload = {"logs": logs}
        
        # Prepare headers (exactly like the test file)
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        
        for attempt in range(self.max_retries + 1):
            try:
                # Send request (exactly like the test file)
                response = requests.post(
                    self.endpoint, 
                    json=payload, 
                    headers=headers, 
                    timeout=self.timeout
                )
                
                self.logger.debug(f"Response Status: {response.status_code}")
                self.logger.debug(f"Response Body: {response.text}")
                
                if response.status_code in [200, 201]:
                    # Success
                    self.stats['logs_sent'] += len(logs)
                    self.stats['batches_sent'] += 1
                    self.stats['last_successful_send'] = datetime.now()
                    
                    self.logger.info(f"Successfully sent batch of {len(logs)} logs")
                    
                    # Parse response to check for failed logs
                    try:
                        response_data = response.json()
                        processed = response_data.get('data', {}).get('processed', 0)
                        failed = response_data.get('data', {}).get('failed', 0)
                        self.logger.info(f"API response: processed={processed}, failed={failed}")
                        
                        if failed > 0:
                            failed_logs = response_data.get('data', {}).get('results', {}).get('failed', [])
                            for failed_log in failed_logs[:3]:  # Show first 3 failures
                                self.logger.warning(f"Failed log: {failed_log.get('logId')} - {failed_log.get('error')}")
                    except:
                        pass
                    
                    return True
                
                elif response.status_code == 400:
                    # Bad request - don't retry, but log the problematic payload for debugging
                    self.logger.error(f"API request failed with status {response.status_code}: {response.text}")

                    # Log the first few logs that caused the validation error for debugging
                    self.logger.error("Problematic logs that caused validation error:")
                    for i, log in enumerate(logs[:3]):  # Show first 3 logs
                        self.logger.error(f"Log {i+1}: {json.dumps(log, indent=2)}")

                    self.stats['api_errors'] += 1
                    self.stats['logs_failed'] += len(logs)
                    self.stats['batches_failed'] += 1
                    self.stats['last_error'] = f"HTTP {response.status_code}: {response.text}"
                    return False
                
                else:
                    # Server error - retry
                    self.logger.warning(f"API request failed with status {response.status_code}: {response.text}")
                    
                    if attempt < self.max_retries:
                        delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                        self.logger.info(f"Retrying in {delay} seconds (attempt {attempt + 1}/{self.max_retries})")
                        time.sleep(delay)
                    else:
                        # Final attempt failed
                        self.stats['api_errors'] += 1
                        self.stats['logs_failed'] += len(logs)
                        self.stats['batches_failed'] += 1
                        self.stats['last_error'] = f"HTTP {response.status_code}: {response.text}"
                        return False
                        
            except Exception as e:
                self.logger.error(f"Error sending batch (attempt {attempt + 1}): {e}")
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.info(f"Retrying in {delay} seconds")
                    time.sleep(delay)
                else:
                    # Final attempt failed
                    self.stats['api_errors'] += 1
                    self.stats['logs_failed'] += len(logs)
                    self.stats['batches_failed'] += 1
                    self.stats['last_error'] = str(e)
                    return False
        
        return False
    
    def _test_connection(self) -> bool:
        """Test API connection using the same method as the test file."""
        try:
            test_log = {
                "log_id": "connection-test-001",
                "timestamp": datetime.now().isoformat(),
                "source": "System",
                "source_type": "event",
                "host": "test-host",
                "log_level": "info",
                "message": "API connection test",
                "raw_data": None,
                "additional_fields": {}
            }
            
            payload = {"logs": [test_log]}
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }
            
            response = requests.post(self.endpoint, json=payload, headers=headers, timeout=5)
            
            if response.status_code in [200, 201, 400]:  # 400 is OK for connection test
                self.logger.info("API connection test successful")
                return True
            else:
                self.logger.warning(f"API connection test failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"API connection test failed: {e}")
            return False
    
    def _process_remaining_batches(self) -> None:
        """Process any remaining batches in the queue."""
        remaining_logs = []
        
        # Collect all remaining logs
        while not self._batch_queue.empty():
            try:
                log = self._batch_queue.get_nowait()
                remaining_logs.append(log)
            except queue.Empty:
                break
        
        # Send in batches
        if remaining_logs:
            for i in range(0, len(remaining_logs), self.batch_size):
                batch = remaining_logs[i:i + self.batch_size]
                self._send_batch(batch)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get API client statistics."""
        stats = self.stats.copy()
        stats['queue_size'] = self._batch_queue.qsize()
        stats['running'] = self._running
        return stats


# For backward compatibility, create an alias
ExLogAPIClient = SimpleExLogAPIClient
