import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import {
  CheckCircle,
  Error,
  Warning,
  Schedule,
  Assessment,
  Download,
  Visibility,
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { fetchComplianceAnalytics } from '../../../store/slices/reportingSlice'

const ComplianceReporting = () => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const { complianceAnalytics, loading, error } = useSelector(state => state.reporting)
  const [selectedFramework, setSelectedFramework] = useState('')

  useEffect(() => {
    dispatch(fetchComplianceAnalytics())
  }, [dispatch])

  const handleFrameworkChange = (event) => {
    const framework = event.target.value
    setSelectedFramework(framework)
    dispatch(fetchComplianceAnalytics(framework))
  }

  const getComplianceColor = (score) => {
    if (score >= 90) return theme.palette.success.main
    if (score >= 75) return theme.palette.warning.main
    return theme.palette.error.main
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed':
        return <CheckCircle color="success" />
      case 'failed':
        return <Error color="error" />
      case 'warning':
        return <Warning color="warning" />
      default:
        return <Schedule color="info" />
    }
  }

  const renderFrameworkOverview = () => {
    if (loading.complianceAnalytics) {
      return (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    if (error.complianceAnalytics) {
      return (
        <Card>
          <CardContent>
            <Alert severity="error">{error.complianceAnalytics}</Alert>
          </CardContent>
        </Card>
      )
    }

    if (!complianceAnalytics) return null

    return (
      <Grid container spacing={3}>
        {complianceAnalytics.frameworks?.map((framework, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">{framework.name}</Typography>
                  <Chip
                    label={`v${framework.version}`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
                
                <Box sx={{ textAlign: 'center', mb: 2 }}>
                  <Typography
                    variant="h3"
                    sx={{ color: getComplianceColor(framework.complianceScore) }}
                  >
                    {framework.complianceScore}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Compliance Score
                  </Typography>
                </Box>
                
                <LinearProgress
                  variant="determinate"
                  value={framework.complianceScore}
                  sx={{
                    mb: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getComplianceColor(framework.complianceScore),
                    },
                  }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Controls Passed</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {framework.controlsPassed}/{framework.controlsTotal}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Last Assessment</Typography>
                  <Typography variant="body2">
                    {new Date(framework.lastAssessment).toLocaleDateString()}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => setSelectedFramework(framework.name.toLowerCase())}
                  >
                    View Details
                  </Button>
                  <Button
                    size="small"
                    startIcon={<Download />}
                    variant="outlined"
                  >
                    Export
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    )
  }

  const renderAuditReadiness = () => {
    if (!complianceAnalytics?.auditReadiness) return null

    const { auditReadiness } = complianceAnalytics

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Audit Readiness
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {auditReadiness.daysUntilNextAudit}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Days Until Next Audit
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info">
                  {auditReadiness.evidenceCollectionProgress}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Evidence Collection Progress
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={auditReadiness.evidenceCollectionProgress}
                  sx={{ mt: 1, height: 6, borderRadius: 3 }}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning">
                  {auditReadiness.outstandingItems}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Outstanding Items
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderComplianceDetails = () => {
    if (!selectedFramework) return null

    // Mock detailed compliance data
    const mockControls = [
      {
        id: 'PCI-1.1',
        name: 'Install and maintain firewall configuration',
        status: 'passed',
        lastTested: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        evidence: 'Firewall rules documented and reviewed',
      },
      {
        id: 'PCI-1.2',
        name: 'Build firewall and router configurations',
        status: 'passed',
        lastTested: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        evidence: 'Configuration templates maintained',
      },
      {
        id: 'PCI-2.1',
        name: 'Change vendor-supplied defaults',
        status: 'failed',
        lastTested: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        evidence: 'Default passwords found on 2 systems',
      },
      {
        id: 'PCI-2.2',
        name: 'Develop configuration standards',
        status: 'warning',
        lastTested: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        evidence: 'Standards exist but need updates',
      },
    ]

    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {selectedFramework.toUpperCase()} Control Details
            </Typography>
            <Button
              variant="outlined"
              onClick={() => setSelectedFramework('')}
            >
              Back to Overview
            </Button>
          </Box>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Control ID</TableCell>
                  <TableCell>Control Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Tested</TableCell>
                  <TableCell>Evidence/Notes</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockControls.map((control) => (
                  <TableRow key={control.id}>
                    <TableCell>{control.id}</TableCell>
                    <TableCell>{control.name}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getStatusIcon(control.status)}
                        <Chip
                          label={control.status}
                          size="small"
                          color={
                            control.status === 'passed' ? 'success' :
                            control.status === 'failed' ? 'error' : 'warning'
                          }
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      {control.lastTested.toLocaleDateString()}
                    </TableCell>
                    <TableCell>{control.evidence}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Compliance Reporting
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Monitor compliance status across multiple regulatory frameworks and generate audit-ready reports.
        </Typography>
        
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Select Framework</InputLabel>
          <Select
            value={selectedFramework}
            label="Select Framework"
            onChange={handleFrameworkChange}
          >
            <MenuItem value="">All Frameworks</MenuItem>
            <MenuItem value="pci">PCI DSS</MenuItem>
            <MenuItem value="hipaa">HIPAA</MenuItem>
            <MenuItem value="soc2">SOC2</MenuItem>
            <MenuItem value="gdpr">GDPR</MenuItem>
            <MenuItem value="iso27001">ISO 27001</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Grid container spacing={3}>
        {!selectedFramework && (
          <>
            <Grid item xs={12}>
              {renderFrameworkOverview()}
            </Grid>
            
            <Grid item xs={12}>
              {renderAuditReadiness()}
            </Grid>
          </>
        )}
        
        {selectedFramework && (
          <Grid item xs={12}>
            {renderComplianceDetails()}
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default ComplianceReporting
