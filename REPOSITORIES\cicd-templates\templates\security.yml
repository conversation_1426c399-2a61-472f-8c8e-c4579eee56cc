# Security-specific GitLab CI/CD Template
# This template provides comprehensive security scanning jobs

# Security-specific variables
variables:
  SAST_EXCLUDED_PATHS: "spec, test, tests, tmp, node_modules, .cache"
  SECRET_DETECTION_EXCLUDED_PATHS: "spec, test, tests, tmp, node_modules, .cache"
  DEPENDENCY_SCANNING_EXCLUDED_PATHS: "spec, test, tests, tmp"

  # Security scanning configuration
  SECURITY_SCAN_ENABLED: "true"
  GITLEAKS_VERSION: "8.18.0"
  TRIVY_VERSION: "0.48.0"

# Note: GitLab's built-in security templates are included conditionally
# They may not be available in all GitLab instances (CE vs EE)
# We provide custom implementations as fallbacks

# GitLab Security Templates (conditionally included)
# These templates are available in GitLab EE and some GitLab.com instances
# If they fail to load, the custom implementations below will be used

# Uncomment these lines if your GitLab instance supports them:
# include:
#   - template: Security/SAST.gitlab-ci.yml
#   - template: Security/Secret-Detection.gitlab-ci.yml
#   - template: Security/Dependency-Scanning.gitlab-ci.yml
#   - template: Security/License-Scanning.gitlab-ci.yml

# Custom security scanning jobs
.security_base:
  stage: security
  extends:
    - .base_job
    - .retry_policy
  artifacts:
    paths:
      - reports/
    expire_in: 1 week
  allow_failure: true
  rules:
    - if: $SECURITY_SCAN_ENABLED == "true"

# OWASP ZAP security scanning
security_zap_baseline:
  extends: .security_base
  image: owasp/zap2docker-stable:latest
  script:
    - echo "Running OWASP ZAP baseline scan..."
    - mkdir -p reports
    - |
      if [ ! -z "$ZAP_TARGET_URL" ]; then
        zap-baseline.py -t $ZAP_TARGET_URL -J reports/zap-baseline.json -r reports/zap-baseline.html || true
      else
        echo "ZAP_TARGET_URL not set, skipping ZAP scan"
      fi
  artifacts:
    paths:
      - reports/zap-baseline.json
      - reports/zap-baseline.html
    expire_in: 1 week
  rules:
    - if: $ZAP_TARGET_URL

# Custom SAST (Static Application Security Testing)
security_sast_semgrep:
  extends: .security_base
  image: returntocorp/semgrep:latest
  script:
    - echo "Running Semgrep SAST scan..."
    - mkdir -p reports
    - semgrep --config=auto --json --output=reports/semgrep-sast.json . || true
    - semgrep --config=auto . || true
  artifacts:
    reports:
      sast: reports/semgrep-sast.json
    paths:
      - reports/semgrep-sast.json
    expire_in: 1 week

# Custom secret detection
security_secrets_gitleaks:
  extends: .security_base
  image: alpine:latest
  script:
    - echo "Running Gitleaks secret detection..."
    - apk add --no-cache git curl
    - curl -sSfL https://github.com/zricethezav/gitleaks/releases/download/v${GITLEAKS_VERSION}/gitleaks_${GITLEAKS_VERSION}_linux_x64.tar.gz | tar -xz
    - chmod +x gitleaks
    - mkdir -p reports
    - ./gitleaks detect --source . --report-format json --report-path reports/gitleaks.json --verbose || true
    - echo "Secret detection completed"
  artifacts:
    reports:
      secret_detection: reports/gitleaks.json
    paths:
      - reports/gitleaks.json
    expire_in: 1 week

# Custom dependency scanning
security_dependency_safety:
  extends: .security_base
  image: python:3.9
  script:
    - echo "Running Python dependency security scan..."
    - pip install safety
    - mkdir -p reports
    - |
      if [ -f requirements.txt ]; then
        safety check --json --output reports/safety.json || true
        safety check || true
      else
        echo "No requirements.txt found, skipping Python dependency scan"
      fi
  artifacts:
    reports:
      dependency_scanning: reports/safety.json
    paths:
      - reports/safety.json
    expire_in: 1 week
  rules:
    - exists:
        - requirements.txt

security_dependency_npm_audit:
  extends: .security_base
  image: node:18
  script:
    - echo "Running Node.js dependency security scan..."
    - mkdir -p reports
    - |
      if [ -f package.json ]; then
        npm audit --audit-level=moderate --json > reports/npm-audit.json || true
        npm audit --audit-level=moderate || true
      else
        echo "No package.json found, skipping Node.js dependency scan"
      fi
  artifacts:
    reports:
      dependency_scanning: reports/npm-audit.json
    paths:
      - reports/npm-audit.json
    expire_in: 1 week
  rules:
    - exists:
        - package.json

# License compliance scanning
security_license_check:
  extends: .security_base
  image: python:3.9
  script:
    - echo "Running license compliance check..."
    - pip install pip-licenses
    - mkdir -p reports
    - |
      if [ -f requirements.txt ]; then
        pip install -r requirements.txt
        pip-licenses --format=json --output-file=reports/python-licenses.json
        pip-licenses --format=csv --output-file=reports/python-licenses.csv
        pip-licenses
      fi
  artifacts:
    paths:
      - reports/python-licenses.json
      - reports/python-licenses.csv
    expire_in: 1 week
  rules:
    - exists:
        - requirements.txt

security_license_check_nodejs:
  extends: .security_base
  image: node:18
  script:
    - echo "Running Node.js license compliance check..."
    - npm install -g license-checker
    - mkdir -p reports
    - |
      if [ -f package.json ]; then
        npm ci
        license-checker --json --out reports/nodejs-licenses.json
        license-checker --csv --out reports/nodejs-licenses.csv
        license-checker
      fi
  artifacts:
    paths:
      - reports/nodejs-licenses.json
      - reports/nodejs-licenses.csv
    expire_in: 1 week
  rules:
    - exists:
        - package.json

# Infrastructure as Code security scanning
security_iac_checkov:
  extends: .security_base
  image: bridgecrew/checkov:latest
  script:
    - echo "Running Checkov IaC security scan..."
    - mkdir -p reports
    - checkov -d . --framework dockerfile docker_compose terraform --output json --output-file reports/checkov.json || true
    - checkov -d . --framework dockerfile docker_compose terraform
  artifacts:
    paths:
      - reports/checkov.json
    expire_in: 1 week
  rules:
    - exists:
        - Dockerfile
        - docker-compose.yml
        - "*.tf"

# Container image vulnerability scanning
security_container_grype:
  extends: .security_base
  image: alpine:latest
  script:
    - echo "Running Grype container vulnerability scan..."
    - apk add --no-cache curl
    - curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
    - mkdir -p reports
    - |
      if [ -f Dockerfile ]; then
        grype dir:. -o json > reports/grype.json || true
        grype dir:.
      else
        echo "No Dockerfile found, skipping container scan"
      fi
  artifacts:
    paths:
      - reports/grype.json
    expire_in: 1 week
  rules:
    - exists:
        - Dockerfile

# Security report aggregation
security_report_summary:
  stage: security
  extends:
    - .base_job
    - .retry_policy
  image: python:3.9
  needs:
    - job: security_secrets_gitleaks
      artifacts: true
      optional: true
    - job: security_license_check
      artifacts: true
      optional: true
    - job: security_license_check_nodejs
      artifacts: true
      optional: true
    - job: security_iac_checkov
      artifacts: true
      optional: true
    - job: security_container_grype
      artifacts: true
      optional: true
  script:
    - echo "Generating security report summary..."
    - mkdir -p reports
    - |
      cat > reports/security-summary.md << 'EOF'
      # Security Scan Summary
      
      ## Scan Results
      
      This report summarizes the security scanning results for the pipeline.
      
      ### Files Scanned
      EOF
    - find reports -name "*.json" -type f | while read file; do echo "- $file" >> reports/security-summary.md; done
    - echo "Security report summary generated"
  artifacts:
    paths:
      - reports/security-summary.md
    expire_in: 1 week
  when: always
