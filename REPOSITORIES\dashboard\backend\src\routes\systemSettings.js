const express = require('express');
const { body, validationResult } = require('express-validator');
const SystemSettings = require('../models/SystemSettings');
const { authenticateToken, authorize } = require('../middleware/auth');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/settings/system
 * @desc    Get system settings (admin only)
 * @access  Private (Admin)
 */
router.get('/system', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const settings = await SystemSettings.getCurrentSettings();

  res.json({
    status: 'success',
    data: {
      settings,
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/system/log-retention
 * @desc    Update log retention settings
 * @access  Private (Admin)
 */
router.put('/system/log-retention', authenticateToken, authorize(['system_admin']), [
  body('defaultRetentionDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Default retention days must be between 1 and 3650'),
  body('autoArchiveEnabled')
    .optional()
    .isBoolean()
    .withMessage('Auto archive enabled must be a boolean'),
  body('archiveCompressionEnabled')
    .optional()
    .isBoolean()
    .withMessage('Archive compression enabled must be a boolean'),
  body('archiveLocation')
    .optional()
    .isIn(['local', 's3', 'azure', 'gcp'])
    .withMessage('Archive location must be local, s3, azure, or gcp'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { defaultRetentionDays, autoArchiveEnabled, archiveCompressionEnabled, archiveLocation } = req.body;

  // Update log retention settings
  if (defaultRetentionDays !== undefined) {
    settings.logRetention.defaultRetentionDays = defaultRetentionDays;
  }
  if (autoArchiveEnabled !== undefined) {
    settings.logRetention.autoArchiveEnabled = autoArchiveEnabled;
  }
  if (archiveCompressionEnabled !== undefined) {
    settings.logRetention.archiveCompressionEnabled = archiveCompressionEnabled;
  }
  if (archiveLocation !== undefined) {
    settings.logRetention.archiveLocation = archiveLocation;
  }

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info('Log retention settings updated', { userId: req.userId, changes: req.body });

  res.json({
    status: 'success',
    message: 'Log retention settings updated successfully',
    data: {
      logRetention: settings.logRetention,
    },
  });
}));

/**
 * @route   POST /api/v1/settings/system/retention-policies
 * @desc    Create new retention policy
 * @access  Private (Admin)
 */
router.post('/system/retention-policies', authenticateToken, authorize(['system_admin']), [
  body('name')
    .notEmpty()
    .trim()
    .withMessage('Policy name is required'),
  body('description')
    .optional()
    .trim()
    .isString()
    .withMessage('Description must be a string'),
  body('retentionDays')
    .isInt({ min: 1, max: 3650 })
    .withMessage('Retention days must be between 1 and 3650'),
  body('logSources')
    .optional()
    .isArray()
    .withMessage('Log sources must be an array'),
  body('logLevels')
    .optional()
    .isArray()
    .withMessage('Log levels must be an array'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { name, description, retentionDays, logSources, logLevels, isDefault } = req.body;

  // Check if policy name already exists
  const existingPolicy = settings.logRetention.retentionPolicies.find(p => p.name === name);
  if (existingPolicy) {
    throw new AppError('A retention policy with this name already exists', 400);
  }

  // If this is set as default, unset other default policies
  if (isDefault) {
    settings.logRetention.retentionPolicies.forEach(policy => {
      policy.isDefault = false;
    });
  }

  const newPolicy = {
    name,
    description: description || '',
    retentionDays,
    logSources: logSources || [],
    logLevels: logLevels || [],
    isDefault: isDefault || false,
  };

  settings.logRetention.retentionPolicies.push(newPolicy);
  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`New retention policy created: ${name}`, { userId: req.userId });

  res.status(201).json({
    status: 'success',
    message: 'Retention policy created successfully',
    data: {
      policy: settings.logRetention.retentionPolicies[settings.logRetention.retentionPolicies.length - 1],
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/system/retention-policies/:policyId
 * @desc    Update retention policy
 * @access  Private (Admin)
 */
router.put('/system/retention-policies/:policyId', authenticateToken, authorize(['system_admin']), [
  body('name')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Policy name cannot be empty'),
  body('description')
    .optional()
    .trim()
    .isString()
    .withMessage('Description must be a string'),
  body('retentionDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Retention days must be between 1 and 3650'),
  body('logSources')
    .optional()
    .isArray()
    .withMessage('Log sources must be an array'),
  body('logLevels')
    .optional()
    .isArray()
    .withMessage('Log levels must be an array'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { policyId } = req.params;
  const settings = await SystemSettings.getCurrentSettings();
  
  const policy = settings.logRetention.retentionPolicies.id(policyId);
  if (!policy) {
    throw new AppError('Retention policy not found', 404);
  }

  const { name, description, retentionDays, logSources, logLevels, isDefault } = req.body;

  // Check if new name conflicts with existing policies
  if (name && name !== policy.name) {
    const existingPolicy = settings.logRetention.retentionPolicies.find(p => p.name === name && p._id.toString() !== policyId);
    if (existingPolicy) {
      throw new AppError('A retention policy with this name already exists', 400);
    }
  }

  // If this is set as default, unset other default policies
  if (isDefault) {
    settings.logRetention.retentionPolicies.forEach(p => {
      if (p._id.toString() !== policyId) {
        p.isDefault = false;
      }
    });
  }

  // Update fields
  if (name !== undefined) policy.name = name;
  if (description !== undefined) policy.description = description;
  if (retentionDays !== undefined) policy.retentionDays = retentionDays;
  if (logSources !== undefined) policy.logSources = logSources;
  if (logLevels !== undefined) policy.logLevels = logLevels;
  if (isDefault !== undefined) policy.isDefault = isDefault;

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`Retention policy updated: ${policy.name}`, { userId: req.userId, policyId });

  res.json({
    status: 'success',
    message: 'Retention policy updated successfully',
    data: {
      policy,
    },
  });
}));

/**
 * @route   DELETE /api/v1/settings/system/retention-policies/:policyId
 * @desc    Delete retention policy
 * @access  Private (Admin)
 */
router.delete('/system/retention-policies/:policyId', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const { policyId } = req.params;
  const settings = await SystemSettings.getCurrentSettings();
  
  const policy = settings.logRetention.retentionPolicies.id(policyId);
  if (!policy) {
    throw new AppError('Retention policy not found', 404);
  }

  // Prevent deletion if it's the only policy
  if (settings.logRetention.retentionPolicies.length === 1) {
    throw new AppError('Cannot delete the last retention policy', 400);
  }

  const policyName = policy.name;
  settings.logRetention.retentionPolicies.pull(policyId);
  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`Retention policy deleted: ${policyName}`, { userId: req.userId, policyId });

  res.json({
    status: 'success',
    message: 'Retention policy deleted successfully',
  });
}));

/**
 * @route   PUT /api/v1/settings/system/notifications
 * @desc    Update system notification settings
 * @access  Private (Admin)
 */
router.put('/system/notifications', authenticateToken, authorize(['system_admin']), [
  body('emailSettings.enabled')
    .optional()
    .isBoolean()
    .withMessage('Email enabled must be a boolean'),
  body('emailSettings.smtpHost')
    .optional()
    .trim()
    .isString()
    .withMessage('SMTP host must be a string'),
  body('emailSettings.smtpPort')
    .optional()
    .isInt({ min: 1, max: 65535 })
    .withMessage('SMTP port must be between 1 and 65535'),
  body('emailSettings.fromAddress')
    .optional()
    .isEmail()
    .withMessage('From address must be a valid email'),
  body('webhookSettings.enabled')
    .optional()
    .isBoolean()
    .withMessage('Webhook enabled must be a boolean'),
  body('webhookSettings.defaultWebhookUrl')
    .optional()
    .isURL()
    .withMessage('Webhook URL must be a valid URL'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { emailSettings, webhookSettings, slackIntegration } = req.body;

  // Update email settings
  if (emailSettings) {
    Object.keys(emailSettings).forEach(key => {
      if (settings.systemNotifications.emailSettings[key] !== undefined) {
        settings.systemNotifications.emailSettings[key] = emailSettings[key];
      }
    });
  }

  // Update webhook settings
  if (webhookSettings) {
    Object.keys(webhookSettings).forEach(key => {
      if (settings.systemNotifications.webhookSettings[key] !== undefined) {
        settings.systemNotifications.webhookSettings[key] = webhookSettings[key];
      }
    });
  }

  // Update Slack integration
  if (slackIntegration) {
    Object.keys(slackIntegration).forEach(key => {
      if (settings.systemNotifications.slackIntegration[key] !== undefined) {
        settings.systemNotifications.slackIntegration[key] = slackIntegration[key];
      }
    });
  }

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info('System notification settings updated', { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Notification settings updated successfully',
    data: {
      systemNotifications: settings.systemNotifications,
    },
  });
}));

module.exports = router;
