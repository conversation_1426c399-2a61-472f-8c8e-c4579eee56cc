import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
} from '@mui/material'
import { Add, People, Assessment } from '@mui/icons-material'
import {
  fetchUsers,
  fetchUserStats,
  fetchRoles,
  fetchAllRoles,
  fetchPermissions,
  clearError,
} from '../../store/slices/usersSlice'
import UsersList from './components/UsersList'
import UsersStatistics from './components/UsersStatistics'
import CreateUserDialog from './components/CreateUserDialog'
import EditUserDialog from './components/EditUserDialog'

const Users = () => {
  const dispatch = useDispatch()
  const {
    users,
    stats,
    roles,
    permissions,
    isLoading,
    error,
    pagination,
    filters,
  } = useSelector((state) => state.users)
  const { user: currentUser } = useSelector((state) => state.auth)

  const [activeTab, setActiveTab] = useState(0)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' })

  // Check if user has permission to manage users
  const canManageUsers = currentUser?.permissions?.includes('manage_users') || currentUser?.role === 'admin'
  const canViewUsers = currentUser?.permissions?.includes('view_users') || canManageUsers

  useEffect(() => {
    if (canViewUsers) {
      dispatch(fetchUsers())
      dispatch(fetchUserStats())
      dispatch(fetchAllRoles()) // Use fetchAllRoles for admin users
      dispatch(fetchPermissions())
    }
  }, [dispatch, canViewUsers])

  useEffect(() => {
    if (error) {
      setSnackbar({
        open: true,
        message: error,
        severity: 'error',
      })
      dispatch(clearError())
    }
  }, [error, dispatch])

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const handleCreateUser = () => {
    setCreateDialogOpen(true)
  }

  const handleEditUser = (user) => {
    setSelectedUser(user)
    setEditDialogOpen(true)
  }

  const handleCloseDialogs = () => {
    setCreateDialogOpen(false)
    setEditDialogOpen(false)
    setSelectedUser(null)
  }

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false })
  }

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity,
    })
  }

  if (!canViewUsers) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          User Management
        </Typography>
        <Alert severity="error">
          You don't have permission to view user management. Please contact your administrator.
        </Alert>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage user accounts, roles, and permissions for the ExLog system.
          </Typography>
        </Box>
        {canManageUsers && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateUser}
            disabled={isLoading}
          >
            Add User
          </Button>
        )}
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab
            label="Users"
            icon={<People />}
            iconPosition="start"
          />
          <Tab
            label="Statistics"
            icon={<Assessment />}
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {activeTab === 0 && (
        <UsersList
          users={users}
          pagination={pagination}
          filters={filters}
          isLoading={isLoading}
          canManageUsers={canManageUsers}
          onEditUser={handleEditUser}
          onRefresh={() => dispatch(fetchUsers())}
          showSnackbar={showSnackbar}
        />
      )}

      {activeTab === 1 && (
        <UsersStatistics
          stats={stats}
          isLoading={isLoading}
          onRefresh={() => dispatch(fetchUserStats())}
        />
      )}

      {/* Create User Dialog */}
      <CreateUserDialog
        open={createDialogOpen}
        onClose={handleCloseDialogs}
        roles={roles}
        permissions={permissions}
        showSnackbar={showSnackbar}
      />

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editDialogOpen}
        onClose={handleCloseDialogs}
        user={selectedUser}
        roles={roles}
        permissions={permissions}
        showSnackbar={showSnackbar}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default Users
