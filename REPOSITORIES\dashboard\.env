# Environment Configuration Example
# Copy this file to .env and update the values for your environment

# Node Environment
NODE_ENV=development

# Server Configuration
PORT=5000
WEBSOCKET_PORT=5001

# Database Configuration
MONGODB_URI=***************************************************************
TIMESCALEDB_URI=postgresql://postgres:password@localhost:5432/exlog
ELASTICSEARCH_URL=http://localhost:9200
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-please
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=10000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_WS_URL=ws://localhost:5001

# Development Tools
VITE_API_URL=http://localhost:5000/api/v1
