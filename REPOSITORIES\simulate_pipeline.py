#!/usr/bin/env python3
"""
GitLab CI/CD Pipeline Simulator
This script simulates running the GitLab CI/CD pipeline locally to test the configuration.
"""

import os
import sys
import yaml
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional

class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def log_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")

def log_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")

def log_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")

def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.END} {message}")

def log_stage(stage: str):
    print(f"\n{Colors.MAGENTA}{Colors.BOLD}=== STAGE: {stage.upper()} ==={Colors.END}")

def log_job(job: str):
    print(f"\n{Colors.CYAN}--- Job: {job} ---{Colors.END}")

class PipelineSimulator:
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.gitlab_ci_path = project_path / '.gitlab-ci.yml'
        self.config = None
        self.stages = []
        self.jobs = {}
        
    def load_config(self) -> bool:
        """Load GitLab CI configuration"""
        if not self.gitlab_ci_path.exists():
            log_error(f"GitLab CI config not found: {self.gitlab_ci_path}")
            return False
        
        try:
            with open(self.gitlab_ci_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # Extract stages
            if 'stages' in self.config:
                self.stages = self.config['stages']
            else:
                # Default GitLab stages
                self.stages = ['build', 'test', 'deploy']
            
            # Extract jobs
            for key, value in self.config.items():
                if isinstance(value, dict) and not key.startswith('.') and key not in ['stages', 'variables', 'include', 'cache', 'default']:
                    self.jobs[key] = value
            
            log_success(f"Loaded GitLab CI config with {len(self.jobs)} jobs")
            return True
            
        except Exception as e:
            log_error(f"Error loading GitLab CI config: {e}")
            return False
    
    def simulate_job(self, job_name: str, job_config: Dict[str, Any]) -> bool:
        """Simulate running a single job"""
        log_job(job_name)
        
        # Check if job has script
        if 'script' not in job_config and 'extends' not in job_config:
            log_warning(f"Job {job_name} has no script or extends")
            return True
        
        # Get job stage
        stage = job_config.get('stage', 'test')
        log_info(f"Stage: {stage}")
        
        # Get job image
        image = job_config.get('image', 'default')
        log_info(f"Image: {image}")
        
        # Simulate script execution
        if 'script' in job_config:
            scripts = job_config['script']
            if isinstance(scripts, str):
                scripts = [scripts]
            
            log_info("Executing scripts:")
            for i, script in enumerate(scripts, 1):
                print(f"  {i}. {script}")
                
                # Simulate some common commands
                if script.startswith('echo'):
                    log_success(f"✓ Echo command executed")
                elif 'python' in script and '--version' in script:
                    log_success(f"✓ Python version check")
                elif 'npm' in script and '--version' in script:
                    log_success(f"✓ npm version check")
                elif 'pip install' in script:
                    log_success(f"✓ Package installation simulated")
                elif 'npm install' in script or 'npm ci' in script:
                    log_success(f"✓ npm installation simulated")
                elif 'pytest' in script:
                    log_success(f"✓ Python tests simulated")
                elif 'npm test' in script:
                    log_success(f"✓ Node.js tests simulated")
                elif 'flake8' in script:
                    log_success(f"✓ Python linting simulated")
                elif 'eslint' in script:
                    log_success(f"✓ JavaScript linting simulated")
                elif 'docker build' in script:
                    log_success(f"✓ Docker build simulated")
                elif 'bandit' in script or 'safety' in script:
                    log_success(f"✓ Security scan simulated")
                else:
                    log_info(f"✓ Command simulated")
        
        # Check artifacts
        if 'artifacts' in job_config:
            artifacts = job_config['artifacts']
            if 'paths' in artifacts:
                log_info(f"Artifacts: {artifacts['paths']}")
            if 'reports' in artifacts:
                log_info(f"Reports: {list(artifacts['reports'].keys())}")
        
        log_success(f"Job {job_name} completed successfully")
        return True
    
    def simulate_stage(self, stage_name: str) -> bool:
        """Simulate running all jobs in a stage"""
        log_stage(stage_name)
        
        # Find jobs for this stage
        stage_jobs = []
        for job_name, job_config in self.jobs.items():
            job_stage = job_config.get('stage', 'test')
            if job_stage == stage_name:
                stage_jobs.append((job_name, job_config))
        
        if not stage_jobs:
            log_info(f"No jobs found for stage: {stage_name}")
            return True
        
        log_info(f"Running {len(stage_jobs)} job(s) in stage: {stage_name}")
        
        # Run jobs in parallel (simulated)
        all_success = True
        for job_name, job_config in stage_jobs:
            success = self.simulate_job(job_name, job_config)
            if not success:
                all_success = False
        
        if all_success:
            log_success(f"Stage {stage_name} completed successfully")
        else:
            log_error(f"Stage {stage_name} failed")
        
        return all_success
    
    def simulate_pipeline(self) -> bool:
        """Simulate running the entire pipeline"""
        log_info(f"Starting pipeline simulation for: {self.project_path.name}")
        log_info("=" * 60)
        
        if not self.load_config():
            return False
        
        # Show pipeline overview
        log_info(f"Pipeline stages: {self.stages}")
        log_info(f"Total jobs: {len(self.jobs)}")
        
        # Show variables
        if 'variables' in self.config:
            log_info("Pipeline variables:")
            for key, value in self.config['variables'].items():
                print(f"  {key}: {value}")
        
        # Show includes
        if 'include' in self.config:
            log_info("Template includes:")
            includes = self.config['include']
            if isinstance(includes, list):
                for include in includes:
                    if isinstance(include, dict) and 'file' in include:
                        files = include['file']
                        if isinstance(files, list):
                            for file in files:
                                print(f"  - {file}")
        
        # Run stages
        all_success = True
        for stage in self.stages:
            success = self.simulate_stage(stage)
            if not success:
                all_success = False
                break  # Stop on first failure
        
        # Summary
        print("\n" + "=" * 60)
        if all_success:
            log_success("🎉 Pipeline simulation completed successfully!")
        else:
            log_error("❌ Pipeline simulation failed")
        
        return all_success

def main():
    """Main function"""
    log_info("GitLab CI/CD Pipeline Simulator")
    log_info("=" * 60)
    
    # Test projects
    projects = [
        ('backend', 'Python Backend Agent'),
        ('dashboard', 'Full-stack Dashboard'),
        ('linux-agent', 'Linux Agent')
    ]
    
    results = []
    
    for project_dir, description in projects:
        project_path = Path(project_dir)
        
        if not project_path.exists():
            log_error(f"Project directory not found: {project_path}")
            results.append((description, False))
            continue
        
        log_info(f"\nTesting project: {description}")
        log_info("-" * 40)
        
        simulator = PipelineSimulator(project_path)
        success = simulator.simulate_pipeline()
        results.append((description, success))
    
    # Final summary
    print("\n" + "=" * 60)
    log_info("PIPELINE SIMULATION SUMMARY")
    log_info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for project, success in results:
        if success:
            log_success(f"✓ {project}")
            passed += 1
        else:
            log_error(f"✗ {project}")
    
    log_info(f"\nProjects passed: {passed}/{total}")
    
    if passed == total:
        log_success("\n🎉 All pipeline simulations passed!")
        log_info("Your GitLab CI/CD configuration is ready for production!")
        return 0
    else:
        log_error(f"\n❌ {total - passed} project(s) failed simulation")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        log_info("\nSimulation interrupted by user")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)
