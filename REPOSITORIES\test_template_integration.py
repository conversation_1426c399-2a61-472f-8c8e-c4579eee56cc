#!/usr/bin/env python3
"""
Template Integration Tester
This script tests that the centralized templates integrate correctly with project configurations.
"""

import os
import sys
import yaml
from pathlib import Path
from typing import Dict, List, Any, Set

class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def log_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")

def log_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")

def log_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")

def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.END} {message}")

def load_yaml_file(file_path: Path) -> Dict[str, Any]:
    """Load YAML file and return parsed content"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        log_error(f"Error loading {file_path}: {e}")
        return {}

def extract_job_templates(config: Dict[str, Any]) -> Set[str]:
    """Extract job template names from configuration"""
    templates = set()
    
    for key, value in config.items():
        if key.startswith('.') and isinstance(value, dict):
            templates.add(key)
    
    return templates

def extract_job_names(config: Dict[str, Any]) -> Set[str]:
    """Extract job names from configuration"""
    jobs = set()
    
    for key, value in config.items():
        if not key.startswith('.') and isinstance(value, dict) and key not in ['stages', 'variables', 'include', 'cache', 'default']:
            jobs.add(key)
    
    return jobs

def test_template_structure():
    """Test that all template files have proper structure"""
    log_info("Testing template structure...")
    
    template_files = [
        'cicd-templates/templates/base.yml',
        'cicd-templates/templates/python.yml',
        'cicd-templates/templates/nodejs.yml',
        'cicd-templates/templates/docker.yml',
        'cicd-templates/templates/security.yml'
    ]
    
    all_templates = set()
    all_jobs = set()
    
    for template_file in template_files:
        template_path = Path(template_file)
        if not template_path.exists():
            log_error(f"Template file not found: {template_file}")
            continue
        
        config = load_yaml_file(template_path)
        if not config:
            log_error(f"Failed to load template: {template_file}")
            continue
        
        templates = extract_job_templates(config)
        jobs = extract_job_names(config)
        
        log_success(f"✓ {template_file}: {len(templates)} templates, {len(jobs)} jobs")
        
        all_templates.update(templates)
        all_jobs.update(jobs)
    
    log_info(f"Total templates found: {len(all_templates)}")
    log_info(f"Total jobs found: {len(all_jobs)}")
    
    # Check for essential templates
    essential_templates = {
        '.base_job',
        '.retry_policy',
        '.python_base',
        '.nodejs_base',
        '.docker_base',
        '.security_base'
    }
    
    missing_templates = essential_templates - all_templates
    if missing_templates:
        log_warning(f"Missing essential templates: {missing_templates}")
    else:
        log_success("✓ All essential templates found")
    
    return len(missing_templates) == 0

def test_project_template_references():
    """Test that projects correctly reference templates"""
    log_info("Testing project template references...")
    
    projects = [
        ('backend/.gitlab-ci.yml', 'Python Backend'),
        ('dashboard/.gitlab-ci.yml', 'Dashboard'),
        ('linux-agent/.gitlab-ci.yml', 'Linux Agent')
    ]
    
    all_good = True
    
    for project_file, project_name in projects:
        project_path = Path(project_file)
        if not project_path.exists():
            log_error(f"Project file not found: {project_file}")
            all_good = False
            continue
        
        config = load_yaml_file(project_path)
        if not config:
            log_error(f"Failed to load project config: {project_file}")
            all_good = False
            continue
        
        # Check includes
        if 'include' not in config:
            log_error(f"{project_name}: No includes found")
            all_good = False
            continue
        
        includes = config['include']
        if not isinstance(includes, list):
            log_error(f"{project_name}: Includes should be a list")
            all_good = False
            continue
        
        template_files = []
        for include in includes:
            if isinstance(include, dict) and 'file' in include:
                files = include['file']
                if isinstance(files, list):
                    template_files.extend(files)
                else:
                    template_files.append(files)
        
        log_success(f"✓ {project_name}: References {len(template_files)} template files")
        
        # Check if templates exist
        for template_file in template_files:
            template_path = Path(f'cicd-templates/{template_file}')
            if template_path.exists():
                log_success(f"  ✓ {template_file}")
            else:
                log_error(f"  ✗ {template_file} not found")
                all_good = False
    
    return all_good

def test_job_inheritance():
    """Test that jobs properly inherit from templates"""
    log_info("Testing job inheritance...")
    
    # Load base template to get available templates
    base_config = load_yaml_file(Path('cicd-templates/templates/base.yml'))
    python_config = load_yaml_file(Path('cicd-templates/templates/python.yml'))
    nodejs_config = load_yaml_file(Path('cicd-templates/templates/nodejs.yml'))
    
    available_templates = set()
    available_templates.update(extract_job_templates(base_config))
    available_templates.update(extract_job_templates(python_config))
    available_templates.update(extract_job_templates(nodejs_config))
    
    log_info(f"Available templates: {sorted(available_templates)}")
    
    # Test project jobs
    projects = [
        ('backend/.gitlab-ci.yml', 'Backend'),
        ('dashboard/.gitlab-ci.yml', 'Dashboard'),
        ('linux-agent/.gitlab-ci.yml', 'Linux Agent')
    ]
    
    all_good = True
    
    for project_file, project_name in projects:
        project_path = Path(project_file)
        if not project_path.exists():
            continue
        
        config = load_yaml_file(project_path)
        if not config:
            continue
        
        jobs = extract_job_names(config)
        log_info(f"{project_name}: {len(jobs)} jobs")
        
        for job_name in jobs:
            job_config = config[job_name]
            
            # Check if job extends templates
            if 'extends' in job_config:
                extends = job_config['extends']
                if isinstance(extends, str):
                    extends = [extends]
                
                for template in extends:
                    if template in available_templates:
                        log_success(f"  ✓ {job_name} extends {template}")
                    else:
                        log_warning(f"  ⚠ {job_name} extends unknown template {template}")
            
            # Check if job has script or extends
            if 'script' not in job_config and 'extends' not in job_config:
                log_warning(f"  ⚠ {job_name} has no script or extends")
    
    return all_good

def test_configuration_consistency():
    """Test configuration consistency across projects"""
    log_info("Testing configuration consistency...")
    
    projects = [
        ('backend/.gitlab-ci.yml', 'Backend', 'python'),
        ('dashboard/.gitlab-ci.yml', 'Dashboard', 'nodejs'),
        ('linux-agent/.gitlab-ci.yml', 'Linux Agent', 'python')
    ]
    
    all_good = True
    
    for project_file, project_name, project_type in projects:
        project_path = Path(project_file)
        if not project_path.exists():
            continue
        
        config = load_yaml_file(project_path)
        if not config:
            continue
        
        # Check variables
        if 'variables' in config:
            variables = config['variables']
            
            # Check for required variables based on project type
            if project_type == 'python':
                required_vars = ['PYTHON_VERSION', 'PROJECT_NAME']
                for var in required_vars:
                    if var in variables:
                        log_success(f"  ✓ {project_name} has {var}")
                    else:
                        log_warning(f"  ⚠ {project_name} missing {var}")
            
            elif project_type == 'nodejs':
                required_vars = ['NODE_VERSION', 'PROJECT_NAME']
                for var in required_vars:
                    if var in variables:
                        log_success(f"  ✓ {project_name} has {var}")
                    else:
                        log_warning(f"  ⚠ {project_name} missing {var}")
        
        # Check cache configuration
        if 'cache' in config:
            log_success(f"  ✓ {project_name} has cache configuration")
        else:
            log_info(f"  ℹ {project_name} no cache configuration")
    
    return all_good

def main():
    """Main test function"""
    log_info("GitLab CI/CD Template Integration Testing")
    log_info("=" * 60)
    
    test_results = []
    
    # Test template structure
    test_results.append(("Template Structure", test_template_structure()))
    
    # Test project template references
    test_results.append(("Template References", test_project_template_references()))
    
    # Test job inheritance
    test_results.append(("Job Inheritance", test_job_inheritance()))
    
    # Test configuration consistency
    test_results.append(("Configuration Consistency", test_configuration_consistency()))
    
    # Summary
    log_info("\n" + "=" * 60)
    log_info("TEMPLATE INTEGRATION TEST SUMMARY")
    log_info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        if result:
            log_success(f"✓ {test_name}")
            passed_tests += 1
        else:
            log_error(f"✗ {test_name}")
    
    log_info(f"\nTests passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        log_success("\n🎉 All template integration tests passed!")
        log_info("Your centralized CI/CD templates are properly integrated!")
        return 0
    else:
        log_error(f"\n❌ {total_tests - passed_tests} test(s) failed.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        log_info("\nTesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)
