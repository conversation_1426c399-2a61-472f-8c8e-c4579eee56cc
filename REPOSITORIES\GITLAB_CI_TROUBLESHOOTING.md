# GitLab CI/CD Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Issue 1: "Included file `Security/License-Scanning.gitlab-ci.yml` is empty or does not exist!"

**Problem:** GitLab's built-in security templates are not available in your GitLab instance.

**Root Cause:** 
- GitLab's security templates (`Security/SAST.gitlab-ci.yml`, `Security/Secret-Detection.gitlab-ci.yml`, etc.) are primarily available in GitLab EE (Enterprise Edition) or GitLab.com
- GitLab CE (Community Edition) or self-hosted instances may not have these templates
- Some GitLab instances may have these features disabled

**Solution:** Use the simplified security template instead.

#### ✅ Quick Fix

Replace `security.yml` with `security-simple.yml` in your project's `.gitlab-ci.yml`:

**Before:**
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/python.yml'
      - 'templates/security.yml'  # ❌ This causes the error
```

**After:**
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/python.yml'
      - 'templates/security-simple.yml'  # ✅ This works everywhere
```

#### 🔧 What's Different?

The `security-simple.yml` template provides:
- ✅ **Custom implementations** of security scanning tools
- ✅ **No dependency** on GitLab's built-in security templates
- ✅ **Works on all GitLab instances** (CE, EE, GitLab.com, self-hosted)
- ✅ **Same security coverage** with different tools

**Security Tools Included:**
- **Secret Detection:** Gitleaks
- **SAST:** Semgrep (commented out in simple version)
- **Dependency Scanning:** Safety (Python), npm audit (Node.js)
- **Container Scanning:** Trivy
- **License Compliance:** pip-licenses, license-checker
- **IaC Security:** Checkov

### Issue 2: "Project not found" Error

**Problem:** GitLab can't find the `cicd-templates` project.

**Solutions:**

1. **Check Project Path:**
   ```yaml
   include:
     - project: 'spr888/cicd-templates'  # Verify this path
   ```

2. **Verify Project Visibility:**
   - Go to `cicd-templates` → Settings → General → Visibility
   - Set to `Internal` or `Public` (not `Private`)

3. **Check Permissions:**
   - Ensure your user has at least `Reporter` access to `cicd-templates`

### Issue 3: "File not found" Error

**Problem:** GitLab can't find specific template files.

**Solutions:**

1. **Verify File Paths:**
   ```yaml
   file: 
     - 'templates/base.yml'           # ✅ Correct
     - 'templates/security-simple.yml' # ✅ Correct
     # NOT: 'base.yml'                # ❌ Missing templates/ prefix
   ```

2. **Check Files Exist:**
   - Go to `cicd-templates` repository
   - Verify files exist in the `templates/` directory

### Issue 4: Template Jobs Not Appearing

**Problem:** Jobs from templates don't show up in your pipeline.

**Solutions:**

1. **Check Job Extensions:**
   ```yaml
   my_custom_job:
     extends: .python_base  # ✅ Extends template job
     script:
       - echo "Custom script"
   ```

2. **Verify Template Loading:**
   - Check pipeline logs for template inclusion messages
   - Look for any YAML syntax errors

### Issue 5: Permission Denied for Cross-Project Access

**Problem:** Projects can't access the `cicd-templates` repository.

**Solutions:**

1. **Group-Level Access (Recommended):**
   - Ensure all projects are in the same GitLab group
   - Set appropriate group permissions

2. **Project Access Token:**
   ```yaml
   # In cicd-templates project
   # Settings → Access Tokens → Create token with 'read_repository' scope
   
   # In consuming projects
   # Settings → CI/CD → Variables
   variables:
     CICD_TEMPLATES_TOKEN: "your-token-here"  # Masked variable
   ```

3. **Update Include with Token:**
   ```yaml
   include:
     - project: 'spr888/cicd-templates'
       ref: main
       file: 'templates/base.yml'
   # Token will be used automatically if set as CI/CD variable
   ```

## 🔍 Debugging Steps

### Step 1: Validate YAML Syntax

```bash
# Install yamllint
pip install yamllint

# Check your .gitlab-ci.yml
yamllint .gitlab-ci.yml
```

### Step 2: Test Template Access

Create a minimal `.gitlab-ci.yml` to test:

```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 'templates/base.yml'

test_template_access:
  stage: test
  script:
    - echo "Template access works!"
```

### Step 3: Check Pipeline Logs

1. Go to `CI/CD` → `Pipelines`
2. Click on the failed pipeline
3. Look for specific error messages in the logs

### Step 4: Verify Template Content

```bash
# Clone the templates repository
git clone https://gitlab.com/spr888/cicd-templates.git
cd cicd-templates

# Check file structure
ls -la templates/

# Validate template YAML
yamllint templates/*.yml
```

## 🛠️ Advanced Troubleshooting

### Enable Debug Mode

Add to your project's `.gitlab-ci.yml`:

```yaml
variables:
  CI_DEBUG_TRACE: "true"  # Enable detailed logging
```

### Test Individual Templates

Test each template separately:

```yaml
# Test 1: Base template only
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 'templates/base.yml'

# Test 2: Add Python template
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/python.yml'

# Test 3: Add security template
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/python.yml'
      - 'templates/security-simple.yml'
```

### Check GitLab Version Compatibility

Different GitLab versions support different features:

- **GitLab CE:** Basic CI/CD, no built-in security templates
- **GitLab EE:** Advanced security features, built-in templates
- **GitLab.com:** Most features available

## 📞 Getting Help

If you're still experiencing issues:

1. **Check GitLab Documentation:** [GitLab CI/CD](https://docs.gitlab.com/ee/ci/)
2. **Review Template Documentation:** `cicd-templates/docs/README.md`
3. **Create an Issue:** In the `cicd-templates` project
4. **Contact DevOps Team:** For organization-specific help

## ✅ Success Checklist

After fixing issues, verify:

- [ ] Pipeline starts without errors
- [ ] Template jobs appear in pipeline
- [ ] Security scans run (if enabled)
- [ ] All stages complete successfully
- [ ] Artifacts are generated correctly

---

**Remember:** The `security-simple.yml` template provides the same security coverage as GitLab's built-in templates but works on all GitLab instances!
