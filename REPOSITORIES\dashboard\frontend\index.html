<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ExLog - Cybersecurity Log Management</title>
    <meta name="description" content="ExLog - Advanced cybersecurity log management and analysis platform" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Material-UI Roboto font -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap"
    />

    <!-- Material-UI Icons -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />

    <!-- Favicon -->
    <link rel="icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#1976d2" />

    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="ExLog - Cybersecurity Log Management" />
    <meta property="og:description" content="Advanced cybersecurity log management and analysis platform" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://exlog.local" />

    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

    <!-- CSP will be handled by the server -->

    <style>
      /* Loading spinner styles */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #e0e0e0;
        border-top: 4px solid #1976d2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        margin-top: 20px;
        color: #666;
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
      }

      /* Hide loading when app is ready */
      .app-ready .loading-container {
        display: none;
      }

      /* Basic reset and body styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
        color: #333;
      }

      #root {
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div class="loading-container" id="loading">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading ExLog...</div>
      </div>
    </div>

    <!-- React app root -->
    <div id="root"></div>

    <!-- Main application script -->
    <script type="module" src="/src/main.jsx"></script>

    <!-- Hide loading screen when app loads -->
    <script>
      // Hide loading screen when React app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
          document.body.classList.add('app-ready');
        }, 500);
      });

      // Fallback: hide loading after 10 seconds
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.display = 'none';
        }
        document.body.classList.add('app-ready');
      }, 10000);
    </script>
  </body>
</html>
