import axios from 'axios'
import { getToken, clearAllAuthData } from './authService'

// Determine the correct API base URL
function getApiBaseUrl() {
  // First, try environment variables
  const envApiUrl = import.meta.env.VITE_API_URL || import.meta.env.REACT_APP_API_URL;
  if (envApiUrl && envApiUrl !== 'undefined') {
    return envApiUrl;
  }

  // If we're running in production (built app), use relative URL
  if (import.meta.env.PROD) {
    return '/api/v1';
  }

  // For development, check if we're accessing via IP or localhost
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;

  // If accessing via IP address, use the same host for API
  if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
    // Check if we're on port 8080 (nginx proxy) - use relative URL
    if (currentPort === '8080' || currentPort === '80') {
      return '/api/v1';
    }
    // If on port 3000 (direct frontend), use backend port
    return `http://${currentHost}:5000/api/v1`;
  }

  // Default fallback for localhost
  return 'http://localhost:5000/api/v1';
}

// Get the API base URL and log it for debugging
const apiBaseUrl = getApiBaseUrl();
console.log('ExLog API Base URL:', apiBaseUrl);
console.log('Environment:', {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  REACT_APP_API_URL: import.meta.env.REACT_APP_API_URL,
  PROD: import.meta.env.PROD,
  hostname: window.location.hostname,
  port: window.location.port
});

// Create axios instance
const api = axios.create({
  baseURL: apiBaseUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor with rate limiting and auth handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    // Handle rate limiting (429 status)
    if (error.response?.status === 429) {
      const message = error.response?.data?.message || 'Too many requests'
      console.warn('Rate limit exceeded:', message)

      // You could implement retry logic here or show a user-friendly message
      // For now, we'll just log it and let the error propagate
      return Promise.reject(new Error('Rate limit exceeded. Please slow down your requests.'))
    }

    // If 401, clear all auth data and redirect to login
    if (error.response?.status === 401) {
      const message = error.response?.data?.message || 'Session expired'

      // Show different messages for different types of session issues
      if (message.includes('Session has been terminated')) {
        // Session was terminated by another device/user
        console.log('Session terminated remotely')
        // You could show a toast notification here if needed
      } else if (message.includes('Session not found')) {
        console.log('Session not found')
      } else {
        console.log('Authentication failed:', message)
      }

      clearAllAuthData()
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default api
