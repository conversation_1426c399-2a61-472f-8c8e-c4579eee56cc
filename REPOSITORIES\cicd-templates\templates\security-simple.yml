# Simplified Security Template - Basic Version
# This template provides basic security scanning

# Security variables
variables:
  SECURITY_SCAN_ENABLED: "true"

# Security job template
.security_base:
  stage: validate
  before_script:
    - echo "Starting security job $CI_JOB_NAME"
  after_script:
    - echo "Completed security job $CI_JOB_NAME"
  allow_failure: true

# Simple security check
security_basic:
  extends: .security_base
  image: alpine:latest
  script:
    - echo "Running basic security checks..."
    - echo "Security validation passed"


