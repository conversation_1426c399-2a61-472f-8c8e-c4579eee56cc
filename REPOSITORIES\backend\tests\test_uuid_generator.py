"""
Tests for UUID Generator

This module contains unit tests for the UUID generation functionality.
"""

import unittest
import uuid
import time
from unittest.mock import patch

from utils.uuid_generator import UUIDGenerator, UUIDFormat, generate_log_id


class TestUUIDGenerator(unittest.TestCase):
    """Test cases for UUIDGenerator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.generator = UUIDGenerator()
        self.sample_log = {
            'timestamp': '2024-01-01T12:00:00.000Z',
            'source': 'test_source',
            'host': 'test_host',
            'message': 'Test log message'
        }

    def test_uuid4_generation(self):
        """Test UUID4 generation (default)."""
        generator = UUIDGenerator(UUIDFormat.UUID4)
        log_id = generator.generate_log_id()
        
        # Should be a valid UUID4
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))
        
        # Should be different each time
        log_id2 = generator.generate_log_id()
        self.assertNotEqual(log_id, log_id2)

    def test_uuid1_generation(self):
        """Test UUID1 generation (time-based)."""
        generator = UUIDGenerator(UUIDFormat.UUID1)
        log_id = generator.generate_log_id()
        
        # Should be a valid UUID
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))
        
        # UUID1 should contain timestamp information
        uuid_obj = uuid.UUID(log_id)
        self.assertEqual(uuid_obj.version, 1)

    def test_uuid3_generation(self):
        """Test UUID3 generation (name-based with MD5)."""
        generator = UUIDGenerator(UUIDFormat.UUID3, namespace="test.example.com")
        log_id = generator.generate_log_id(self.sample_log)
        
        # Should be a valid UUID
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))
        
        # UUID3 should be deterministic for same input
        log_id2 = generator.generate_log_id(self.sample_log)
        self.assertEqual(log_id, log_id2)

    def test_uuid5_generation(self):
        """Test UUID5 generation (name-based with SHA-1)."""
        generator = UUIDGenerator(UUIDFormat.UUID5, namespace="test.example.com")
        log_id = generator.generate_log_id(self.sample_log)
        
        # Should be a valid UUID
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))
        
        # UUID5 should be deterministic for same input
        log_id2 = generator.generate_log_id(self.sample_log)
        self.assertEqual(log_id, log_id2)

    def test_custom_format_generation(self):
        """Test custom format generation."""
        generator = UUIDGenerator(UUIDFormat.CUSTOM)
        log_id = generator.generate_log_id(self.sample_log)
        
        # Should contain timestamp, random, and hash components
        parts = log_id.split('_')
        self.assertEqual(len(parts), 3)
        
        # First part should be timestamp (numeric)
        self.assertTrue(parts[0].isdigit())
        
        # Second and third parts should be hex strings
        self.assertTrue(all(c in '0123456789abcdef' for c in parts[1]))
        self.assertTrue(all(c in '0123456789abcdef' for c in parts[2]))

    def test_fallback_without_namespace(self):
        """Test fallback to UUID4 when namespace is missing for name-based UUIDs."""
        generator = UUIDGenerator(UUIDFormat.UUID3)  # No namespace provided
        log_id = generator.generate_log_id()
        
        # Should still generate a valid UUID (fallback to UUID4)
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))

    def test_create_generator_from_config(self):
        """Test creating generator from configuration."""
        config = {
            'format': 'uuid4',
            'namespace': 'test.example.com'
        }
        
        generator = UUIDGenerator.create_generator_from_config(config)
        self.assertEqual(generator.uuid_format, UUIDFormat.UUID4)
        self.assertEqual(generator.namespace, 'test.example.com')

    def test_create_generator_from_config_invalid_format(self):
        """Test creating generator with invalid format falls back to UUID4."""
        config = {
            'format': 'invalid_format'
        }
        
        generator = UUIDGenerator.create_generator_from_config(config)
        self.assertEqual(generator.uuid_format, UUIDFormat.UUID4)

    def test_is_valid_uuid(self):
        """Test UUID validation."""
        # Valid UUIDs
        valid_uuids = [
            str(uuid.uuid4()),
            str(uuid.uuid1()),
            '550e8400-e29b-41d4-a716-************'
        ]
        
        for valid_uuid in valid_uuids:
            self.assertTrue(UUIDGenerator.is_valid_uuid(valid_uuid))
        
        # Invalid UUIDs
        invalid_uuids = [
            'not-a-uuid',
            '123',
            '',
            None,
            '550e8400-e29b-41d4-a716-44665544000'  # Missing digit
        ]
        
        for invalid_uuid in invalid_uuids:
            self.assertFalse(UUIDGenerator.is_valid_uuid(invalid_uuid))

    def test_convenience_function(self):
        """Test the convenience generate_log_id function."""
        log_id = generate_log_id()
        self.assertTrue(UUIDGenerator.is_valid_uuid(log_id))

    def test_name_creation_from_log(self):
        """Test name creation from log entry for name-based UUIDs."""
        generator = UUIDGenerator(UUIDFormat.UUID5, namespace="test.example.com")
        
        # Test with complete log entry
        name1 = generator._create_name_from_log(self.sample_log)
        self.assertIn('test_source', name1)
        self.assertIn('test_host', name1)
        
        # Test with None log entry
        name2 = generator._create_name_from_log(None)
        self.assertIsInstance(name2, str)
        self.assertTrue(len(name2) > 0)

    def test_custom_id_format(self):
        """Test custom ID format structure."""
        generator = UUIDGenerator(UUIDFormat.CUSTOM)
        
        with patch('time.time', return_value=1640995200.123):  # Fixed timestamp
            log_id = generator._generate_custom_id(self.sample_log)
            
            parts = log_id.split('_')
            self.assertEqual(len(parts), 3)
            
            # Check timestamp part
            self.assertEqual(parts[0], '1640995200123')
            
            # Check random and hash parts are hex strings of correct length
            self.assertEqual(len(parts[1]), 8)
            self.assertEqual(len(parts[2]), 8)


class TestUUIDGeneratorIntegration(unittest.TestCase):
    """Integration tests for UUID generator with different configurations."""

    def test_different_formats_produce_different_results(self):
        """Test that different UUID formats produce different results."""
        log_entry = {
            'timestamp': '2024-01-01T12:00:00.000Z',
            'source': 'test',
            'message': 'test message'
        }
        
        generators = {
            'uuid4': UUIDGenerator(UUIDFormat.UUID4),
            'uuid1': UUIDGenerator(UUIDFormat.UUID1),
            'uuid5': UUIDGenerator(UUIDFormat.UUID5, namespace="test.com"),
            'custom': UUIDGenerator(UUIDFormat.CUSTOM)
        }
        
        results = {}
        for name, generator in generators.items():
            results[name] = generator.generate_log_id(log_entry)
        
        # All should be different (except potentially UUID5 if run multiple times)
        unique_results = set(results.values())
        self.assertGreaterEqual(len(unique_results), 3)  # At least 3 should be unique

    def test_performance(self):
        """Test UUID generation performance."""
        generator = UUIDGenerator(UUIDFormat.UUID4)
        
        start_time = time.time()
        for _ in range(1000):
            generator.generate_log_id()
        end_time = time.time()
        
        # Should be able to generate 1000 UUIDs in reasonable time (< 1 second)
        self.assertLess(end_time - start_time, 1.0)


if __name__ == '__main__':
    unittest.main()
