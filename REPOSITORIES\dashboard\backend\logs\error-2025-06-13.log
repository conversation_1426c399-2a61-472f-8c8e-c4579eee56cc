{"code": 11000, "errorLabelSet": {}, "errorResponse": {"code": 11000, "errmsg": "E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }", "index": 0, "keyPattern": {"apiKeys.key": 1}, "keyValue": {"apiKeys.key": null}}, "index": 0, "keyPattern": {"apiKeys.key": 1}, "keyValue": {"apiKeys.key": null}, "level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mFailed to initialize alert system: E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }\u001b[39m", "stack": "MongoServerError: E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)", "timestamp": "2025-06-13 21:08:53:853"}