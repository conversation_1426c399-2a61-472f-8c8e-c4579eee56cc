const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

async function debugCorrelation() {
  try {
    console.log('Debugging Correlation Engine...');
    
    // Get a recent agent log that should have triggered the rule
    // Note: We can't use the logs GET endpoint with API key, so let's check the database directly
    console.log('Checking database for recent agent logs...');
    
    if (response.data.data.logs.length > 0) {
      const agentLog = response.data.data.logs[0];
      console.log('Recent agent log:', {
        logId: agentLog.logId,
        host: agentLog.host,
        message: agentLog.message.substring(0, 50),
        alertTriggered: agentLog.alertTriggered,
        timestamp: agentLog.timestamp
      });
      
      // Now send the same log structure through our test endpoint to see if it triggers
      console.log('\nRe-sending agent log structure through test...');
      const testLog = {
        timestamp: new Date().toISOString(),
        source: agentLog.source,
        sourceType: agentLog.sourceType,
        host: agentLog.host,
        logLevel: agentLog.logLevel,
        message: `TEST: ${agentLog.message}`,
        logId: `debug-${Date.now()}`,
        rawData: agentLog.rawData,
        additionalFields: agentLog.additionalFields
      };
      
      const initialCount = (await axios.get(`${API_BASE}/alerts/statistics`, {
        headers: { 'X-API-Key': 'test-api-key-12345' }
      })).data.data.statistics.total;
      
      await axios.post(`${API_BASE}/logs`, { logs: [testLog] }, {
        headers: { 'X-API-Key': 'test-api-key-12345' }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const finalCount = (await axios.get(`${API_BASE}/alerts/statistics`, {
        headers: { 'X-API-Key': 'test-api-key-12345' }
      })).data.data.statistics.total;
      
      console.log('Test results:');
      console.log('- Initial alerts:', initialCount);
      console.log('- Final alerts:', finalCount);
      console.log('- Alert triggered:', finalCount > initialCount ? 'YES' : 'NO');
      
      if (finalCount > initialCount) {
        console.log('✅ The rule works with agent log structure');
        console.log('❌ Issue: Agent logs are not being processed by correlation engine');
      } else {
        console.log('❌ The rule does not work with agent log structure');
        console.log('❌ Issue: Rule configuration or log structure mismatch');
      }
    } else {
      console.log('No agent logs found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

debugCorrelation();
