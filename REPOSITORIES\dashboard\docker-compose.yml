services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: /api/v1
        REACT_APP_API_URL: /api/v1
        REACT_APP_WS_URL: /ws
    ports:
      - "3000:3000"
    environment:
      # Use dynamic API URLs that work with both localhost and IP access
      - VITE_API_URL=/api/v1
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_WS_URL=/ws
      # Fallback URLs for direct access (when not using nginx proxy)
      - REACT_APP_API_URL_FALLBACK=http://localhost:5000/api/v1
      - REACT_APP_WS_URL_FALLBACK=ws://localhost:5001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - exlog-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    user: "1000:1000"  # Add user to fix permission issues
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/exlog
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRES_IN=24h
      # CORS configuration to allow network access
      - CORS_ORIGIN=*
      # Alternative: specify multiple origins
      # - CORS_ORIGIN=http://localhost:3000,http://localhost,http://127.0.0.1:3000,http://127.0.0.1
    depends_on:
      - mongodb
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - exlog-network

  # WebSocket Service
  websocket:
    build:
      context: ./backend
      dockerfile: Dockerfile.websocket
    ports:
      - "5001:5001"
    user: "1000:1000"  # Add user to fix permission issues
    environment:
      - NODE_ENV=development
      - PORT=5001
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - exlog-network

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=exlog
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - exlog-network



  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80" # Use port 8080 to avoid conflict with existing web server
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
      - websocket
    networks:
      - exlog-network

volumes:
  mongodb_data:


networks:
  exlog-network:
    driver: bridge
