const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const config = require('./config');
const logger = require('./utils/logger');
const databaseManager = require('./config/database');
const { errorHandler } = require('./middleware/errorHandler');
const { authenticateToken } = require('./middleware/auth');

// Optional Swagger imports - gracefully handle if not available
let swaggerUi, swaggerSpecs;
try {
  swaggerUi = require('swagger-ui-express');
  swaggerSpecs = require('./config/swagger');
} catch (error) {
  logger.warn('Swagger dependencies not available. API documentation will be disabled.');
}

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const rolesRoutes = require('./routes/roles');
const logRoutes = require('./routes/logs');
const alertRoutes = require('./routes/alerts');
const agentRoutes = require('./routes/agents');
const reportRoutes = require('./routes/reports');
const analyticsRoutes = require('./routes/analytics');
const dashboardRoutes = require('./routes/dashboards');
const settingsRoutes = require('./routes/settings');
const systemSettingsRoutes = require('./routes/systemSettings');

class ExLogServer {
  constructor() {
    this.app = express();
    this.server = null;
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());

    // CORS middleware
    this.app.use(cors(config.cors));

    // Compression middleware
    this.app.use(compression());

    // Rate limiting - Different limits for different endpoints

    // Strict rate limiting for authentication endpoints
    const authLimiter = rateLimit({
      windowMs: config.security.rateLimitWindowMs, // 15 minutes
      max: config.security.authRateLimitMax, // Limit auth requests per IP
      message: 'Too many authentication attempts from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/v1/auth/login', authLimiter);
    this.app.use('/api/v1/auth/register', authLimiter);

    // Moderate rate limiting for general API endpoints
    const apiLimiter = rateLimit({
      windowMs: config.security.rateLimitWindowMs, // 15 minutes
      max: config.security.rateLimitMax, // General API limit
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
      // Skip rate limiting for authenticated users with valid tokens
      skip: (req) => {
        // Check if request has valid authorization header
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        return !!token; // Skip rate limiting if token is present (will be validated by auth middleware)
      }
    });
    this.app.use('/api/', apiLimiter);

    // Very lenient rate limiting for authenticated API calls
    const authenticatedLimiter = rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: config.security.authenticatedRateLimitMax, // Per minute for authenticated users
      message: 'Too many requests, please slow down.',
      standardHeaders: true,
      legacyHeaders: false,
    });

    // Logging middleware
    this.app.use(morgan('combined', { stream: logger.stream }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Store the authenticated limiter for use in routes
    this.authenticatedLimiter = authenticatedLimiter;
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
      });
    });

    // API Documentation (if Swagger is available)
    if (swaggerUi && swaggerSpecs) {
      this.app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'ExLog API Documentation',
        swaggerOptions: {
          url: '/api/docs.json',
          supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
          tryItOutEnabled: true,
        },
      }));

      // API JSON spec endpoint
      this.app.get('/api/docs.json', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpecs);
      });

      logger.info('API documentation enabled at /api/docs');
    } else {
      // Provide a simple message when Swagger is not available
      this.app.get('/api/docs', (req, res) => {
        res.json({
          message: 'API documentation is not available. Install swagger dependencies to enable.',
          endpoints: {
            health: 'GET /health',
            auth: 'POST /api/v1/auth/login, GET /api/v1/auth/me',
            logs: 'POST /api/v1/logs, GET /api/v1/logs'
          }
        });
      });

      this.app.get('/api/docs.json', (req, res) => {
        res.status(503).json({
          error: 'API documentation not available',
          message: 'Swagger dependencies not installed'
        });
      });
    }

    // API routes
    this.app.use('/api/v1/auth', authRoutes);
    this.app.use('/api/v1/users', this.authenticatedLimiter, authenticateToken, userRoutes);
    this.app.use('/api/v1/roles', this.authenticatedLimiter, authenticateToken, rolesRoutes);
    this.app.use('/api/v1/logs', this.authenticatedLimiter, logRoutes);
    this.app.use('/api/v1/alerts', this.authenticatedLimiter, alertRoutes);
    this.app.use('/api/v1/agents', agentRoutes); // Agents use API key auth, keep separate
    this.app.use('/api/v1/reports', this.authenticatedLimiter, authenticateToken, reportRoutes);
    this.app.use('/api/v1/analytics', this.authenticatedLimiter, authenticateToken, analyticsRoutes);
    this.app.use('/api/v1/dashboards', this.authenticatedLimiter, authenticateToken, dashboardRoutes);
    this.app.use('/api/v1/settings', this.authenticatedLimiter, authenticateToken, settingsRoutes);
    this.app.use('/api/v1/settings', this.authenticatedLimiter, authenticateToken, systemSettingsRoutes);

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested resource was not found',
        path: req.originalUrl,
      });
    });

    // Error handling middleware (must be last)
    this.app.use(errorHandler);
  }

  async connectDatabases() {
    try {
      await databaseManager.connectAll();
      logger.info('All database connections established');
    } catch (error) {
      logger.error('Failed to connect to databases:', error);
      throw error;
    }
  }

  async start() {
    try {
      // Connect to databases
      await this.connectDatabases();

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Initialize correlation engine and default rules
      await this.initializeAlertSystem();

      // Start server - bind to all interfaces for network access
      this.server = this.app.listen(config.port, '0.0.0.0', () => {
        logger.info(`ExLog API server running on port ${config.port}`);
        logger.info(`Environment: ${config.nodeEnv}`);
        logger.info(`Server accessible on all network interfaces`);
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async initializeAlertSystem() {
    try {
      logger.info('Initializing alert system...');

      // Initialize correlation engine
      const { getCorrelationEngine } = require('./services/correlationEngine');
      const correlationEngine = getCorrelationEngine();

      // Wait for correlation engine to initialize
      if (!correlationEngine.isInitialized) {
        await new Promise((resolve) => {
          correlationEngine.once('initialized', resolve);
        });
      }

      // Initialize default rules if needed
      const { getDefaultRulesService } = require('./services/defaultRules');
      const defaultRulesService = getDefaultRulesService();

      // Check if we need to create a system user for default rules
      const User = require('./models/User');
      let systemUser = await User.findOne({ username: 'system' });

      if (!systemUser) {
        systemUser = new User({
          username: 'system',
          email: '<EMAIL>',
          password: 'system-generated-password',
          firstName: 'System',
          lastName: 'User',
          role: 'admin',
          isActive: false, // System user should not be able to login
          apiKeys: [], // Explicitly set empty array to avoid unique constraint issues
        });
        await systemUser.save();
        logger.info('Created system user for default rules');
      }

      // Initialize default rules
      const AlertRule = require('./models/AlertRule');
      const existingDefaultRules = await AlertRule.countDocuments({ isDefault: true });

      if (existingDefaultRules === 0) {
        await defaultRulesService.initializeDefaultRules(systemUser._id);
        logger.info('Default alert rules initialized');
      } else {
        logger.info(`Found ${existingDefaultRules} existing default rules`);
      }

      logger.info('Alert system initialization completed');

    } catch (error) {
      logger.error('Failed to initialize alert system:', error);
      // Don't fail the entire server startup for alert system issues
    }
  }

  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      if (this.server) {
        this.server.close(async () => {
          logger.info('HTTP server closed');

          try {
            await databaseManager.disconnect();
            logger.info('Database connections closed');
            process.exit(0);
          } catch (error) {
            logger.error('Error during graceful shutdown:', error);
            process.exit(1);
          }
        });
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  const server = new ExLogServer();
  server.start();
}

module.exports = ExLogServer;
