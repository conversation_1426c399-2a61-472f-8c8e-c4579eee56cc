const express = require('express');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');
const Report = require('../models/Report');
const Schedule = require('../models/Schedule');
const analyticsService = require('../services/analyticsService');

const router = express.Router();

/**
 * @route   GET /api/v1/reports
 * @desc    Get all reports for the user
 * @access  Private
 */
router.get('/', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { page = 1, limit = 20, type, folder, search, status = 'published' } = req.query;
  const userId = req.user.id;

  // Build query
  const query = {
    $or: [
      { owner: userId },
      { 'sharing.isPublic': true, status: 'published' },
      { 'sharing.sharedWith.user': userId },
    ],
  };

  if (type) query.type = type;
  if (folder) query.folder = folder;
  if (status) query.status = status;
  if (search) {
    query.$text = { $search: search };
  }

  const reports = await Report.find(query)
    .populate('owner', 'username firstName lastName')
    .sort({ updatedAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .lean();

  const total = await Report.countDocuments(query);

  res.json({
    status: 'success',
    data: {
      reports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * @route   GET /api/v1/reports/:id
 * @desc    Get specific report
 * @access  Private
 */
router.get('/:id', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id)
    .populate('owner', 'username firstName lastName')
    .populate('sharing.sharedWith.user', 'username firstName lastName');

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Check access permissions
  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  res.json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   POST /api/v1/reports
 * @desc    Create new report
 * @access  Private
 */
router.post('/', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const { name, description, type, content, tags, folder, sharing } = req.body;

  const report = new Report({
    reportId: uuidv4(),
    name,
    description,
    type,
    owner: req.user.id,
    content: content || {},
    tags: tags || [],
    folder: folder || 'My Reports',
    sharing: sharing || { isPublic: false, sharedWith: [] },
  });

  await report.save();

  res.status(201).json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   PUT /api/v1/reports/:id
 * @desc    Update existing report
 * @access  Private
 */
router.put('/:id', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Check edit permissions
  if (!report.canEdit(req.user.id)) {
    throw new AppError('Edit access denied', 403);
  }

  const { name, description, content, tags, folder, sharing, status } = req.body;

  if (name) report.name = name;
  if (description !== undefined) report.description = description;
  if (content) report.content = content;
  if (tags) report.tags = tags;
  if (folder) report.folder = folder;
  if (sharing) report.sharing = sharing;
  if (status) report.status = status;

  await report.save();

  res.json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   DELETE /api/v1/reports/:id
 * @desc    Delete report
 * @access  Private
 */
router.delete('/:id', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Only owner can delete
  if (report.owner.toString() !== req.user.id) {
    throw new AppError('Only the owner can delete this report', 403);
  }

  await Report.findByIdAndDelete(req.params.id);

  res.json({
    status: 'success',
    message: 'Report deleted successfully',
  });
}));

/**
 * @route   POST /api/v1/reports/:id/execute
 * @desc    Execute report and return data
 * @access  Private
 */
router.post('/:id/execute', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  const { parameters = {} } = req.body;
  const startTime = Date.now();

  try {
    let data;

    // Execute based on report type
    switch (report.type) {
      case 'analytics':
        data = await executeAnalyticsReport(report, parameters);
        break;
      case 'compliance':
        data = await executeComplianceReport(report, parameters);
        break;
      case 'custom':
        data = await executeCustomReport(report, parameters);
        break;
      default:
        throw new AppError('Unknown report type', 400);
    }

    const executionTime = Date.now() - startTime;

    // Update report metadata
    report.lastRunAt = new Date();
    report.metadata.executionCount += 1;
    report.metadata.avgExecutionTime =
      (report.metadata.avgExecutionTime * (report.metadata.executionCount - 1) + executionTime) /
      report.metadata.executionCount;

    await report.save();

    res.json({
      status: 'success',
      data: {
        reportData: data,
        executionTime,
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    report.metadata.lastError = error.message;
    await report.save();
    throw error;
  }
}));

// Helper functions for report execution
async function executeAnalyticsReport(report, parameters) {
  const timeRange = parameters.timeRange || '30d';

  switch (report.content.analyticsType) {
    case 'security_posture':
      return await analyticsService.getSecurityPosture(timeRange);
    case 'incident_analytics':
      return await analyticsService.getIncidentAnalytics(timeRange);
    case 'operational_metrics':
      return await analyticsService.getOperationalMetrics(timeRange);
    default:
      return await analyticsService.getSecurityPosture(timeRange);
  }
}

async function executeComplianceReport(report, parameters) {
  // TODO: Implement compliance report execution
  return {
    framework: report.content.framework || 'PCI DSS',
    complianceScore: 85,
    controlsAssessed: 12,
    controlsPassed: 10,
    controlsFailed: 2,
    lastAssessment: new Date(),
  };
}

async function executeCustomReport(report, parameters) {
  // TODO: Implement custom report execution based on report.content configuration
  return {
    message: 'Custom report execution not yet implemented',
    reportId: report.reportId,
    parameters,
  };
}

module.exports = router;
