# ExLog CI/CD Templates

[![Pipeline Status](https://gitlab.com/spr888/cicd-templates/badges/main/pipeline.svg)](https://gitlab.com/spr888/cicd-templates/-/pipelines)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Version](https://img.shields.io/badge/Version-2.0.0-blue.svg)](CHANGELOG.md)

Centralized GitLab CI/CD templates and configurations for the ExLog project ecosystem. This repository provides **simplified, robust** pipeline templates that prioritize reliability and ease of use over complexity.

## 🎯 Purpose

This repository implements a centralized CI/CD architecture where:
- **Pipeline templates** are stored in one central location
- **Individual projects** reference these templates in their `.gitlab-ci.yml` files
- **Robust error handling** ensures pipelines don't fail due to dependency issues
- **Platform compatibility** handles Windows/Linux dependency differences
- **Graceful degradation** continues testing even with missing dependencies

## 🚨 Recent Major Update (v2.0.0)

**We've completely simplified the CI/CD templates** to focus on reliability and robustness:
- ✅ **Fixed dependency installation issues** across all platforms
- ✅ **Added graceful error handling** to prevent pipeline failures
- ✅ **Simplified template structure** for easier maintenance
- ✅ **Enhanced platform compatibility** for Windows/Linux projects
- ✅ **Improved workspace support** for Node.js monorepos

See [CHANGELOG.md](CHANGELOG.md) for complete details.

## 🏗️ Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   cicd-templates    │    │     backend         │    │     dashboard       │
│   (this repo)       │    │   (Python agent)   │    │   (Node.js app)     │
│                     │    │                     │    │                     │
│ ├── templates/      │◄───┤ .gitlab-ci.yml     │    │ .gitlab-ci.yml     │
│ ├── configs/        │    │ includes templates  │    │ includes templates  │
│ ├── scripts/        │    │                     │    │                     │
│ └── docs/           │    └─────────────────────┘    └─────────────────────┘
└─────────────────────┘                ▲                        ▲
                                       │                        │
                              ┌─────────────────────┐          │
                              │   linux-agent       │          │
                              │   (Python agent)    │          │
                              │                     │          │
                              │ .gitlab-ci.yml     │◄─────────┘
                              │ includes templates  │
                              │                     │
                              └─────────────────────┘
```

## 🚀 Quick Start

### 1. Include Templates in Your Project

Add this to your project's `.gitlab-ci.yml`:

```yaml
# For Python projects (backend, linux-agent)
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/python.yml'

variables:
  PROJECT_NAME: "your-project"
  PYTHON_VERSION: "3.9"
```

```yaml
# For Node.js projects (dashboard)
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'

variables:
  PROJECT_NAME: "your-project"
  NODE_VERSION: "18"
```

### 2. Add Project-Specific Validation

Create simple validation jobs for your project:

```yaml
# Example: Backend project validation
validate_backend_structure:
  stage: validate
  extends: .base_job
  image: python:3.9
  script:
    - echo "Validating backend project structure..."
    - echo "✓ Found main.py" && test -f main.py
    - echo "✓ Found requirements.txt" && test -f requirements.txt
    - echo "Backend structure validation passed"
```

### 3. Handle Platform-Specific Dependencies

For Python projects with platform-specific dependencies, create a `requirements-ci.txt` file:

```txt
# requirements-ci.txt - CI-compatible dependencies
PyYAML>=6.0
requests>=2.28.0
psutil>=5.9.0
# Exclude: pywin32 (Windows-only)
# Exclude: python-systemd>=234 (unavailable version)
```

## 📦 What's Included

### 🔧 Templates (Simplified & Robust)
- **`base.yml`** - Basic stages (validate, test) and job templates
- **`python.yml`** - Python setup with dependency handling and basic testing
- **`nodejs.yml`** - Node.js setup with workspace support and basic testing
- **`docker.yml`** - Basic Docker job template
- **`security-simple.yml`** - Simple security validation

### 🛡️ Key Features
- **Robust Dependency Handling**: Automatically handles platform-specific dependencies
- **Graceful Error Handling**: Continues pipeline execution even with missing dependencies
- **Platform Compatibility**: Works on both Windows and Linux projects
- **Workspace Support**: Handles npm workspace projects correctly
- **CI-Specific Requirements**: Supports `requirements-ci.txt` for Python projects

## 🔍 Pipeline Stages

Current simplified pipeline structure:

```
┌─────────────┐  ┌─────────────┐
│   Validate  │─▶│    Test     │
└─────────────┘  └─────────────┘
      │                │
      ▼                ▼
  • Project        • Basic tests
  • Structure      • Syntax checks
  • Dependencies   • Fallback validation
```

**Future Enhancement Path:**
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Validate  │─▶│    Test     │─▶│   Quality   │─▶│    Build    │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
```

The templates are designed for **incremental enhancement** - start simple, add complexity gradually.

## 🐍 Python Projects

### Current Features (v2.0.0)
- **Robust Dependency Installation**: Handles platform-specific dependencies gracefully
- **CI-Specific Requirements**: Supports `requirements-ci.txt` for Linux CI compatibility
- **Basic Testing**: pytest execution with fallback to syntax checks
- **Error Handling**: Continues pipeline even with missing dependencies

### Dependency Handling
```bash
# Template logic:
1. Check for requirements-ci.txt → Use if exists
2. Else filter requirements.txt → Remove problematic deps
3. Install available dependencies → Continue on failures
4. Ensure pytest availability → For testing
```

### Supported Projects
- **Backend Agent** - Windows logging agent (excludes `pywin32` in CI)
- **Linux Agent** - Linux logging agent (uses compatible `python-systemd` version)

## 🟢 Node.js Projects

### Current Features (v2.0.0)
- **Workspace Support**: Automatically detects and handles npm workspaces
- **Smart Installation**: Uses `npm install` for workspaces, `npm ci` for regular projects
- **Fallback Strategy**: Falls back from `npm ci` to `npm install` on lock file issues
- **Basic Testing**: npm test execution with graceful failure handling

### Installation Logic
```bash
# Template logic:
1. Check for workspace project → Use npm install
2. Else try npm ci → Fallback to npm install
3. Handle missing package.json → Continue gracefully
4. Run tests with --if-present → Avoid failures
```

### Supported Projects
- **Dashboard** - React/Node.js workspace project with frontend/backend

## 🔒 Security Features (Simplified)

### Current Security (v2.0.0)
- **Basic Security Validation**: Simple security checks in validate stage
- **Dependency Awareness**: Handles security-related dependency issues gracefully

### Future Security Enhancements
The templates are designed to support gradual addition of:
- **SAST** (Static Application Security Testing)
- **Secret Detection** - Prevent credential leaks
- **Dependency Scanning** - Vulnerable dependency detection
- **Container Scanning** - Docker image vulnerabilities

## 🎯 Design Philosophy

### Reliability First
- **Graceful Degradation**: Pipelines continue even with partial failures
- **Platform Compatibility**: Works across Windows/Linux development environments
- **Dependency Resilience**: Handles missing or incompatible dependencies
- **Clear Logging**: Informative messages about what's happening and why

### Incremental Enhancement
- **Start Simple**: Basic validation and testing
- **Add Gradually**: Quality checks, security scanning, deployment
- **Test Each Addition**: Validate each enhancement before adding the next
- **Maintain Stability**: Never break existing functionality

## 🎯 Supported Projects

### ✅ Currently Active Projects
- **Backend Agent** (`backend/`) - Windows logging agent with Linux CI compatibility
- **Linux Agent** (`linux-agent/`) - Linux logging agent with dependency handling
- **Dashboard** (`dashboard/`) - React/Node.js workspace project

### ✅ Template Compatibility
- **Python Projects**: Any Python project with requirements.txt
- **Node.js Projects**: Regular projects and npm workspaces
- **Mixed Projects**: Projects with both Python and Node.js components

## 🔧 Configuration Examples

### Basic Python Project
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/python.yml'

variables:
  PYTHON_VERSION: "3.9"
  PROJECT_NAME: "my-python-project"
```

### Basic Node.js Project
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'

variables:
  NODE_VERSION: "18"
  PROJECT_NAME: "my-nodejs-project"
```

## 🚀 Migration from v1.x

If you're upgrading from v1.x templates:

1. **Update includes**: Remove complex templates, use simplified ones
2. **Add requirements-ci.txt**: For Python projects with platform-specific deps
3. **Test pipelines**: Verify they pass with new error handling
4. **Gradual enhancement**: Add back complexity incrementally

## 📚 Documentation

- **[CHANGELOG.md](CHANGELOG.md)** - Complete version history and changes
- **[MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)** - Upgrading from v1.x to v2.0.0
- **[docs/README.md](docs/README.md)** - Detailed technical documentation

## 🤝 Contributing

1. Fork this repository
2. Create a feature branch: `git checkout -b feature/improvement`
3. Test changes with real projects
4. Update documentation and changelog
5. Submit a merge request

### Development Guidelines
- **Reliability first**: Don't break existing functionality
- **Test thoroughly**: Validate with all supported projects
- **Document changes**: Update README and CHANGELOG
- **Keep it simple**: Prefer robust simplicity over complex features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Report bugs or request features in GitLab issues
- **Changelog**: Check [CHANGELOG.md](CHANGELOG.md) for recent changes
- **Testing**: Use provided test scripts to validate configurations

## 🏷️ Version History

- **v2.0.0** - 🎉 **Current** - Simplified, robust templates with dependency handling
- **v1.2.0** - Enhanced security scanning and quality gates
- **v1.1.0** - Added Docker and security templates
- **v1.0.0** - Initial release with Python and Node.js templates

See [CHANGELOG.md](CHANGELOG.md) for detailed version history.

---

**Made with ❤️ for the ExLog project ecosystem**
**Simplified for reliability and ease of use** 🛡️
