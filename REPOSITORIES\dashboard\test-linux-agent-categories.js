#!/usr/bin/env node

/**
 * Test script for Linux Agent Categories Integration
 * 
 * This script tests the new Linux agent log categories by sending
 * sample logs to the dashboard API to verify proper handling.
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api/v1';
const API_KEY = 'bb93493a1a62ace8d41d25cf233f67b5cb8dbdd709d5617c914c299bc5e4e9a0'; // Default API key

// Sample logs for each new Linux agent category
const sampleLogs = [
  {
    logId: 'linux-auth-001',
    timestamp: new Date().toISOString(),
    source: 'Auth',
    sourceType: 'auth',
    host: 'linux-server-01',
    logLevel: 'info',
    message: 'User john logged in via SSH from *************',
    additionalFields: {
      syslog_identifier: 'sshd',
      pid: 1234,
      uid: 1000,
      systemd_unit: 'ssh.service',
      collector: 'journalctl'
    }
  },
  {
    logId: 'linux-kernel-001',
    timestamp: new Date().toISOString(),
    source: 'Kernel',
    sourceType: 'kernel',
    host: 'linux-server-01',
    logLevel: 'warning',
    message: 'USB device disconnected: /dev/sdb1',
    additionalFields: {
      syslog_identifier: 'kernel',
      kernel_timestamp: '[12345.678901]',
      kernel_event_type: 'hardware',
      collector: 'kernel'
    }
  },
  {
    logId: 'linux-service-001',
    timestamp: new Date().toISOString(),
    source: 'Service',
    sourceType: 'service',
    host: 'linux-server-01',
    logLevel: 'info',
    message: 'Started Apache HTTP Server',
    additionalFields: {
      systemd_unit: 'apache2.service',
      syslog_identifier: 'systemd',
      pid: 5678,
      collector: 'journalctl'
    }
  },
  {
    logId: 'linux-scheduler-001',
    timestamp: new Date().toISOString(),
    source: 'Scheduler',
    sourceType: 'scheduler',
    host: 'linux-server-01',
    logLevel: 'info',
    message: 'Cron job executed: /usr/bin/backup-script.sh',
    additionalFields: {
      syslog_identifier: 'cron',
      pid: 9012,
      cmdline: '/usr/bin/backup-script.sh',
      collector: 'journalctl'
    }
  },
  {
    logId: 'linux-hardware-001',
    timestamp: new Date().toISOString(),
    source: 'Hardware',
    sourceType: 'hardware',
    host: 'linux-server-01',
    logLevel: 'info',
    message: 'Bluetooth device connected: Mouse MX Master 3',
    additionalFields: {
      syslog_identifier: 'bluetoothd',
      pid: 3456,
      device_type: 'input',
      collector: 'journalctl'
    }
  },
  {
    logId: 'linux-systemd-001',
    timestamp: new Date().toISOString(),
    source: 'Systemd',
    sourceType: 'systemd',
    host: 'linux-server-01',
    logLevel: 'info',
    message: 'System startup completed in 15.2s',
    additionalFields: {
      systemd_unit: 'systemd',
      syslog_identifier: 'systemd',
      boot_id: 'abc123def456',
      collector: 'journalctl'
    }
  },
  {
    logId: 'linux-journal-001',
    timestamp: new Date().toISOString(),
    source: 'Journal',
    sourceType: 'journal',
    host: 'linux-server-01',
    logLevel: 'debug',
    message: 'General system message from unknown source',
    additionalFields: {
      pid: 7890,
      collector: 'journalctl'
    }
  }
];

async function testLinuxAgentCategories() {
  console.log('🧪 Testing Linux Agent Categories Integration\n');

  try {
    // Test API health
    console.log('1. Testing API health...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log(`   ✅ API is healthy: ${healthResponse.data.message}\n`);

    // Send sample logs
    console.log('2. Sending sample logs for each category...');
    const response = await axios.post(
      `${API_BASE_URL}/logs`,
      { logs: sampleLogs },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY
        }
      }
    );

    console.log(`   ✅ Successfully sent ${sampleLogs.length} logs`);
    console.log(`   📊 Response: ${response.data.message}\n`);

    // Test filtering by each new category
    console.log('3. Testing category filtering...');
    const newCategories = ['Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd', 'Journal'];
    
    for (const category of newCategories) {
      try {
        const filterResponse = await axios.get(
          `${API_BASE_URL}/logs?source=${category}&limit=10`,
          {
            headers: {
              'Authorization': `Bearer ${await getAuthToken()}`
            }
          }
        );
        
        const count = filterResponse.data.data?.length || 0;
        console.log(`   ✅ ${category}: Found ${count} logs`);
      } catch (error) {
        console.log(`   ❌ ${category}: Error - ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n4. Summary of test logs sent:');
    sampleLogs.forEach(log => {
      console.log(`   📝 ${log.source} (${log.sourceType}): ${log.message.substring(0, 50)}...`);
    });

    console.log('\n✅ Linux Agent Categories Integration Test Completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. Check the dashboard UI at http://localhost:3000/logs');
    console.log('   2. Verify new categories appear in the source filter');
    console.log('   3. Test filtering by each new category');
    console.log('   4. Confirm color coding is working correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure the API key is correct and the dashboard is running');
    } else if (error.response?.status === 400) {
      console.log('\n💡 Tip: Check if the new categories are properly configured in the backend');
    }
    
    process.exit(1);
  }
}

async function getAuthToken() {
  // For testing purposes, we'll skip authentication
  // In a real scenario, you'd authenticate with admin credentials
  return 'dummy-token';
}

// Run the test
if (require.main === module) {
  testLinuxAgentCategories();
}

module.exports = { testLinuxAgentCategories, sampleLogs };
