import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Grid,
  FormControlLabel,
  Checkbox,
  Chip,
  FormGroup,
  Divider,
  Alert,
} from '@mui/material'
import { createUser } from '../../../store/slices/usersSlice'
import PermissionManager from './PermissionManager'

const steps = ['Basic Information', 'Role & Permissions', 'Review']

const CreateUserDialog = ({ open, onClose, roles, permissions, showSnackbar }) => {
  const dispatch = useDispatch()
  const { isCreating, groupedPermissions } = useSelector((state) => state.users)

  const [activeStep, setActiveStep] = useState(0)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: 'security_analyst',
    customPermissions: [],
    status: 'active',
    forcePasswordChange: true,
  })
  const [errors, setErrors] = useState({})

  useEffect(() => {
    if (!open) {
      // Reset form when dialog closes
      setActiveStep(0)
      setFormData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        firstName: '',
        lastName: '',
        role: 'security_analyst',
        customPermissions: [],
        status: 'active',
        forcePasswordChange: true,
      })
      setErrors({})
    }
  }, [open])

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handlePermissionToggle = (permission) => {
    const newPermissions = formData.customPermissions.includes(permission)
      ? formData.customPermissions.filter(p => p !== permission)
      : [...formData.customPermissions, permission]
    
    setFormData(prev => ({ ...prev, customPermissions: newPermissions }))
  }

  const validateStep = (step) => {
    const newErrors = {}

    if (step === 0) {
      // Basic Information validation
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
      if (!formData.username.trim()) newErrors.username = 'Username is required'
      if (formData.username.length < 3) newErrors.username = 'Username must be at least 3 characters'
      if (!formData.email.trim()) newErrors.email = 'Email is required'
      if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid'
      if (!formData.password) newErrors.password = 'Password is required'
      if (formData.password.length < 8) newErrors.password = 'Password must be at least 8 characters'
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1)
    }
  }

  const handleBack = () => {
    setActiveStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    try {
      const userData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        permissions: formData.customPermissions,
        status: formData.status,
      }

      await dispatch(createUser(userData)).unwrap()
      showSnackbar('User created successfully')
      onClose()
    } catch (error) {
      showSnackbar(error, 'error')
    }
  }

  const getSelectedRole = () => {
    return roles.find(role => role.id === formData.role)
  }

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                error={!!errors.firstName}
                helperText={errors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={!!errors.lastName}
                helperText={errors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                error={!!errors.username}
                helperText={errors.username}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={!!errors.password}
                helperText={errors.password}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.forcePasswordChange}
                    onChange={(e) => handleInputChange('forcePasswordChange', e.target.checked)}
                  />
                }
                label="Force password change on first login"
              />
            </Grid>
          </Grid>
        )

      case 1:
        return (
          <Box>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Role</InputLabel>
              <Select
                value={formData.role}
                label="Role"
                onChange={(e) => handleInputChange('role', e.target.value)}
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <PermissionManager
              selectedRole={formData.role}
              userPermissions={formData.customPermissions}
              onPermissionsChange={(newPermissions) => handleInputChange('customPermissions', newPermissions)}
              roles={roles}
              groupedPermissions={groupedPermissions}
              disabled={isCreating}
            />
          </Box>
        )

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review User Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Name</Typography>
                <Typography variant="body2" color="text.secondary">
                  {formData.firstName} {formData.lastName}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Username</Typography>
                <Typography variant="body2" color="text.secondary">
                  {formData.username}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Email</Typography>
                <Typography variant="body2" color="text.secondary">
                  {formData.email}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Role</Typography>
                <Typography variant="body2" color="text.secondary">
                  {getSelectedRole()?.name}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">Custom Permissions</Typography>
                <Box sx={{ mt: 1 }}>
                  {formData.customPermissions.length > 0 ? (
                    formData.customPermissions.map((permissionId) => {
                      const permission = permissions.find(p => p.id === permissionId)
                      return (
                        <Chip
                          key={permissionId}
                          label={permission?.name || permissionId}
                          size="small"
                          sx={{ mr: 1, mb: 1 }}
                        />
                      )
                    })
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No custom permissions
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        )

      default:
        return null
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New User</DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent(activeStep)}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={isCreating}>
          Cancel
        </Button>
        
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={isCreating}>
            Back
          </Button>
        )}
        
        {activeStep < steps.length - 1 ? (
          <Button onClick={handleNext} variant="contained">
            Next
          </Button>
        ) : (
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={isCreating}
          >
            {isCreating ? 'Creating...' : 'Create User'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default CreateUserDialog
