# Linux Agent Integration Updates

## Overview

This document describes the changes made to the ExLog dashboard to support the enhanced log categorization system from the Linux Log Collection Agent v1.2.0.

## Changes Made

### 1. Backend Schema Updates

#### Database Model (`backend/src/models/Log.js`)
- **Extended `source` enum** to include new Linux agent categories:
  - `Auth` - Authentication and authorization events
  - `Kernel` - Kernel messages and hardware events  
  - `Service` - System service management events
  - `Scheduler` - Cron jobs and scheduled tasks
  - `Hardware` - USB, Bluetooth, and hardware events
  - `Systemd` - System management and service control
  - `Journal` - systemd journal entries

- **Extended `sourceType` enum** to include new source types:
  - `auth` - Authentication logs
  - `kernel` - Kernel messages
  - `service` - Service management
  - `scheduler` - Scheduled tasks
  - `hardware` - Hardware events
  - `systemd` - System management
  - `syslog` - Syslog entries
  - `journal` - Journal entries

#### MongoDB Schema (`scripts/mongo-init.js`)
- Updated database validation schema to accept new categories
- Maintains backward compatibility with existing Windows agent logs

#### API Validation (`backend/src/routes/logs.js`)
- Updated request validation for log ingestion endpoint
- Updated query parameter validation for log filtering
- Supports both old and new category systems

#### API Documentation (`backend/src/config/swagger.js`)
- Updated OpenAPI specification with new source categories
- Enhanced documentation for Linux agent integration

### 2. Frontend Updates

#### Log Display (`frontend/src/pages/Logs/Logs.jsx`)
- **Enhanced Source Filter Dropdown**: Added all new Linux agent categories
- **Color-Coded Source Display**: Each source category now has a distinct color:
  - `Auth` - Warning (orange)
  - `Kernel` - Error (red) 
  - `Service` - Success (green)
  - `Scheduler` - Info (blue)
  - `Hardware` - Secondary (purple)
  - `Systemd` - Primary (blue)
  - `Journal` - Default (gray)

- **Improved Display Names**: User-friendly names for categories:
  - `Auth` → "Authentication"
  - `Systemd` → "System Management"
  - `Scheduler` → "Scheduled Tasks"

- **Visual Enhancements**: 
  - Source categories displayed as colored chips instead of plain text
  - Consistent styling in both table view and detail modal

## Linux Agent Categories

### Authentication (`Auth`)
- **Source Type**: `auth`
- **Description**: Authentication and authorization events
- **Examples**: SSH logins, sudo commands, PAM authentication
- **Color**: Warning (Orange)

### Kernel (`Kernel`)
- **Source Type**: `kernel`
- **Description**: Kernel messages and hardware events
- **Examples**: Hardware errors, driver messages, system panics
- **Color**: Error (Red)

### Network (`Network`)
- **Source Type**: `network`
- **Description**: Network-related events and connectivity
- **Examples**: DHCP, DNS, interface changes
- **Color**: Info (Blue)

### Application (`Application`)
- **Source Type**: `application`
- **Description**: Web servers, databases, and application logs
- **Examples**: Apache, Nginx, MySQL, PostgreSQL
- **Color**: Secondary (Purple)

### Security (`Security`)
- **Source Type**: `security`
- **Description**: Firewall, SELinux, and security events
- **Examples**: iptables, fail2ban, AppArmor
- **Color**: Error (Red)

### Service (`Service`)
- **Source Type**: `service`
- **Description**: System service management events
- **Examples**: systemd service starts/stops/failures
- **Color**: Success (Green)

### Scheduler (`Scheduler`)
- **Source Type**: `scheduler`
- **Description**: Cron jobs and scheduled tasks
- **Examples**: cron executions, systemd timers
- **Color**: Info (Blue)

### Hardware (`Hardware`)
- **Source Type**: `hardware`
- **Description**: USB, Bluetooth, and hardware events
- **Examples**: Device connections, audio/video events
- **Color**: Secondary (Purple)

### System Management (`Systemd`)
- **Source Type**: `systemd`
- **Description**: System management and service control
- **Examples**: systemd unit management, system state changes
- **Color**: Primary (Blue)

### Journal (`Journal`)
- **Source Type**: `journal`
- **Description**: General systemd journal entries
- **Examples**: Miscellaneous system messages
- **Color**: Default (Gray)

## Backward Compatibility

The dashboard maintains full backward compatibility with existing Windows agent logs:

### Windows Agent Categories (Unchanged)
- `System` - Windows system events
- `Application` - Windows application logs
- `Security` - Windows security events
- `Network` - Windows network events
- `Custom` - Custom log sources

### Windows Agent Source Types (Unchanged)
- `event` - Windows event logs
- `application` - Application-specific logs
- `security` - Security audit logs
- `network` - Network-related logs
- `audit` - Audit trail logs
- `performance` - Performance monitoring logs

## Migration Notes

### For Existing Deployments
1. **No database migration required** - new categories are additive
2. **Existing logs remain unchanged** - all current data is preserved
3. **API endpoints unchanged** - same endpoints support both agent types
4. **Frontend automatically supports** both old and new categories

### For New Linux Agent Deployments
1. **Configure API endpoint** in Linux agent config
2. **Verify API key** is properly set
3. **Test log collection** with various source types
4. **Monitor dashboard** for proper categorization

## Testing

### Verification Steps
1. **Deploy updated dashboard** with new schema support
2. **Configure Linux agent** to send logs to dashboard
3. **Verify log categorization** in dashboard UI
4. **Test filtering** by new source categories
5. **Confirm color coding** displays correctly

### Expected Results
- Linux agent logs appear with proper categories
- Filtering works for all new source types
- Color coding helps distinguish log types
- Detail view shows enhanced metadata
- No impact on existing Windows agent logs

## Future Enhancements

### Planned Improvements
1. **Source-specific icons** for better visual distinction
2. **Advanced filtering** by source type combinations
3. **Category-based alerting** rules
4. **Enhanced statistics** by source category
5. **Custom category mapping** for specific environments

### Integration Opportunities
1. **Real-time categorization** updates via WebSocket
2. **Machine learning** for automatic categorization
3. **Cross-platform correlation** between Windows and Linux logs
4. **Unified alerting** across all agent types

## Support

For issues related to Linux agent integration:

1. **Check agent logs** for categorization errors
2. **Verify API connectivity** between agent and dashboard
3. **Review dashboard logs** for ingestion issues
4. **Test with sample data** to isolate problems
5. **Consult troubleshooting guides** for both components
