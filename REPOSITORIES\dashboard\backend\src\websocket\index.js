const WebSocket = require('ws');
const config = require('../config');
const logger = require('../utils/logger');

class WebSocketServer {
  constructor() {
    this.wss = null;
    this.clients = new Map();
  }

  start() {
    const port = config.websocket.port || 5001;

    this.wss = new WebSocket.Server({
      port,
      host: '0.0.0.0', // Bind to all interfaces for network access
      perMessageDeflate: false,
    });

    this.wss.on('connection', (ws, req) => {
      const clientId = this.generateClientId();
      this.clients.set(clientId, {
        ws,
        userId: null,
        subscriptions: new Set(),
        lastPing: Date.now(),
      });

      logger.info(`WebSocket client connected: ${clientId}`);

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          this.handleMessage(clientId, data);
        } catch (error) {
          logger.error('Invalid WebSocket message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format',
          }));
        }
      });

      ws.on('close', () => {
        logger.info(`WebSocket client disconnected: ${clientId}`);
        this.clients.delete(clientId);
      });

      ws.on('error', (error) => {
        logger.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        clientId,
        timestamp: new Date().toISOString(),
      }));
    });

    // Setup ping/pong for connection health
    this.setupHeartbeat();

    logger.info(`WebSocket server started on port ${port}`);
  }

  handleMessage(clientId, data) {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (data.type) {
      case 'ping':
        client.lastPing = Date.now();
        client.ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString(),
        }));
        break;

      case 'subscribe':
        if (data.channel) {
          client.subscriptions.add(data.channel);
          client.ws.send(JSON.stringify({
            type: 'subscribed',
            channel: data.channel,
          }));
        }
        break;

      case 'unsubscribe':
        if (data.channel) {
          client.subscriptions.delete(data.channel);
          client.ws.send(JSON.stringify({
            type: 'unsubscribed',
            channel: data.channel,
          }));
        }
        break;

      case 'auth':
        // TODO: Implement authentication
        client.userId = data.userId;
        client.ws.send(JSON.stringify({
          type: 'authenticated',
          userId: data.userId,
        }));
        break;

      default:
        client.ws.send(JSON.stringify({
          type: 'error',
          message: 'Unknown message type',
        }));
    }
  }

  broadcast(channel, data) {
    const message = JSON.stringify({
      type: 'broadcast',
      channel,
      data,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.subscriptions.has(channel) && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  sendToUser(userId, data) {
    const message = JSON.stringify({
      type: 'message',
      data,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.userId === userId && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  setupHeartbeat() {
    const interval = setInterval(() => {
      const now = Date.now();
      
      this.clients.forEach((client, clientId) => {
        if (now - client.lastPing > 60000) { // 60 seconds timeout
          logger.warn(`Removing inactive WebSocket client: ${clientId}`);
          client.ws.terminate();
          this.clients.delete(clientId);
        }
      });
    }, 30000); // Check every 30 seconds

    this.wss.on('close', () => {
      clearInterval(interval);
    });
  }

  generateClientId() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  stop() {
    if (this.wss) {
      this.wss.close();
      logger.info('WebSocket server stopped');
    }
  }
}

// Start the WebSocket server if this file is run directly
if (require.main === module) {
  const wsServer = new WebSocketServer();
  wsServer.start();

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('Received SIGTERM, shutting down WebSocket server...');
    wsServer.stop();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('Received SIGINT, shutting down WebSocket server...');
    wsServer.stop();
    process.exit(0);
  });
}

module.exports = WebSocketServer;
