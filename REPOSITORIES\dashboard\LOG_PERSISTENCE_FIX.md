# Log Persistence Fix

## Issue Description

**Problem**: Real logs from agents were being automatically deleted from the database after ingestion, while only manually inserted sample logs persisted.

**Root Cause**: The MongoDB Log model had a TTL (Time To Live) index configured with `expireAfterSeconds: 0`, which caused all logs to be deleted immediately after creation.

## Technical Details

### The Problem
In `backend/src/models/Log.js`, line 133 had:
```javascript
// TTL index for automatic deletion based on retention policy
logSchema.index({ createdAt: 1 }, { expireAfterSeconds: 0 });
```

The `expireAfterSeconds: 0` setting meant that MongoDB would delete documents immediately after they were created, which is why:
- Real logs from agents disappeared quickly
- Only manually inserted sample logs (which might have been inserted differently) persisted
- The log management page showed inconsistent data

### The Solution

1. **Added Retention Configuration** (`backend/src/config/index.js`):
   ```javascript
   // Data Retention Configuration
   retention: {
     // Log retention in seconds (default: 90 days)
     logRetentionSeconds: parseInt(process.env.LOG_RETENTION_SECONDS) || (90 * 24 * 60 * 60),
     // Alert retention in seconds (default: 365 days)
     alertRetentionSeconds: parseInt(process.env.ALERT_RETENTION_SECONDS) || (365 * 24 * 60 * 60),
     // Disable automatic deletion in development
     enableAutoDelete: process.env.ENABLE_AUTO_DELETE === 'true' || process.env.NODE_ENV === 'production',
   }
   ```

2. **Updated Log Model** (`backend/src/models/Log.js`):
   ```javascript
   // TTL index for automatic deletion based on retention policy
   // Only enable automatic deletion if configured to do so
   if (config.retention.enableAutoDelete) {
     logSchema.index({ createdAt: 1 }, { expireAfterSeconds: config.retention.logRetentionSeconds });
   } else {
     // In development, don't auto-delete logs
     logSchema.index({ createdAt: 1 });
   }
   ```

3. **Environment Configuration**:
   - In development (`NODE_ENV=development`): Auto-deletion is disabled by default
   - In production: Auto-deletion can be enabled by setting `ENABLE_AUTO_DELETE=true`
   - Configurable retention periods via environment variables

## Implementation Steps

### 1. Removed Existing TTL Index
```bash
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.logs.dropIndex('createdAt_1')"
```

### 2. Updated Configuration Files
- Modified `backend/src/config/index.js` to add retention configuration
- Updated `backend/src/models/Log.js` to use conditional TTL indexing
- Updated `.env.example` with new environment variables

### 3. Restarted Backend Service
```bash
docker-compose restart backend
```

### 4. Verified Fix
- Tested log ingestion and persistence
- Confirmed logs remain in database after ingestion
- Verified no automatic deletion occurs

## Environment Variables

Add these to your `.env` file for production:

```bash
# Data Retention Configuration
# Log retention in seconds (default: 90 days = 7776000 seconds)
LOG_RETENTION_SECONDS=7776000

# Alert retention in seconds (default: 365 days = 31536000 seconds)  
ALERT_RETENTION_SECONDS=31536000

# Enable automatic deletion (set to 'true' in production, 'false' in development)
ENABLE_AUTO_DELETE=false
```

## Testing Results

### Before Fix
- Logs were deleted immediately after ingestion
- Only sample logs persisted
- Log count remained constant despite new log ingestion

### After Fix
- ✅ Logs persist correctly after ingestion
- ✅ No automatic deletion in development environment
- ✅ Log count increases as expected when new logs are ingested
- ✅ All logs remain searchable and accessible

### Test Script Results
```
Initial log count: 5
After ingestion: 7 (+2 logs)
After 10 seconds: 7 (no deletion)
✅ Logs persisted successfully!
```

## Production Considerations

### Enabling Auto-Deletion in Production
For production environments, you may want to enable automatic log deletion to manage storage:

1. Set environment variable: `ENABLE_AUTO_DELETE=true`
2. Configure retention period: `LOG_RETENTION_SECONDS=7776000` (90 days)
3. Restart the backend service

### Storage Management
- **Development**: Logs persist indefinitely (manual cleanup required)
- **Production**: Configurable automatic deletion after retention period
- **Default Retention**: 90 days for logs, 365 days for alerts

### Monitoring
- Monitor database storage usage
- Set up alerts for storage capacity
- Consider implementing log archiving for compliance requirements

## Verification Commands

### Check Current Indexes
```bash
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.logs.getIndexes().filter(idx => idx.name.includes('createdAt'))"
```

### Check Log Count
```bash
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.logs.countDocuments()"
```

### Test Log Persistence
```bash
powershell -ExecutionPolicy Bypass -File test-log-persistence.ps1
```

## Future Enhancements

1. **Log Archiving**: Implement archiving to cold storage before deletion
2. **Retention Policies**: Different retention periods based on log type/severity
3. **Compliance Features**: Immutable log storage for regulatory requirements
4. **Storage Optimization**: Compression and indexing strategies for large volumes

## Conclusion

The log persistence issue has been resolved by:
- Removing the immediate TTL deletion (`expireAfterSeconds: 0`)
- Adding configurable retention policies
- Disabling auto-deletion in development by default
- Providing production-ready configuration options

Real logs from agents will now persist correctly and be available for analysis in the log management interface.
