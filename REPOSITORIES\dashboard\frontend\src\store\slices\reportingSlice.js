import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks for API calls
export const fetchReports = createAsyncThunk(
  'reporting/fetchReports',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/reports', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch reports')
    }
  }
)

export const fetchReport = createAsyncThunk(
  'reporting/fetchReport',
  async (reportId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/${reportId}`)
      return response.data.data.report
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch report')
    }
  }
)

export const createReport = createAsyncThunk(
  'reporting/createReport',
  async (reportData, { rejectWithValue }) => {
    try {
      const response = await api.post('/reports', reportData)
      return response.data.data.report
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create report')
    }
  }
)

export const updateReport = createAsyncThunk(
  'reporting/updateReport',
  async ({ reportId, reportData }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/reports/${reportId}`, reportData)
      return response.data.data.report
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update report')
    }
  }
)

export const deleteReport = createAsyncThunk(
  'reporting/deleteReport',
  async (reportId, { rejectWithValue }) => {
    try {
      await api.delete(`/reports/${reportId}`)
      return reportId
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete report')
    }
  }
)

export const executeReport = createAsyncThunk(
  'reporting/executeReport',
  async ({ reportId, parameters = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/reports/${reportId}/execute`, { parameters })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to execute report')
    }
  }
)

// Analytics thunks
export const fetchSecurityAnalytics = createAsyncThunk(
  'reporting/fetchSecurityAnalytics',
  async (timeRange = '30d', { rejectWithValue }) => {
    try {
      const response = await api.get('/analytics/security', { params: { timeRange } })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch security analytics')
    }
  }
)

export const fetchIncidentAnalytics = createAsyncThunk(
  'reporting/fetchIncidentAnalytics',
  async (timeRange = '30d', { rejectWithValue }) => {
    try {
      const response = await api.get('/analytics/incidents', { params: { timeRange } })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch incident analytics')
    }
  }
)

export const fetchThreatIntelligence = createAsyncThunk(
  'reporting/fetchThreatIntelligence',
  async (timeRange = '30d', { rejectWithValue }) => {
    try {
      const response = await api.get('/analytics/threats', { params: { timeRange } })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch threat intelligence')
    }
  }
)

export const fetchOperationalMetrics = createAsyncThunk(
  'reporting/fetchOperationalMetrics',
  async (timeRange = '24h', { rejectWithValue }) => {
    try {
      const response = await api.get('/analytics/operations', { params: { timeRange } })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch operational metrics')
    }
  }
)

export const fetchComplianceAnalytics = createAsyncThunk(
  'reporting/fetchComplianceAnalytics',
  async (framework = null, { rejectWithValue }) => {
    try {
      const params = framework ? { framework } : {}
      const response = await api.get('/analytics/compliance', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch compliance analytics')
    }
  }
)

export const fetchDashboardOverview = createAsyncThunk(
  'reporting/fetchDashboardOverview',
  async (timeRange = '24h', { rejectWithValue }) => {
    try {
      const response = await api.get('/analytics/dashboard-overview', { params: { timeRange } })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard overview')
    }
  }
)

const initialState = {
  // Reports
  reports: [],
  currentReport: null,
  reportsPagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  },
  
  // Analytics
  securityAnalytics: null,
  incidentAnalytics: null,
  threatIntelligence: null,
  operationalMetrics: null,
  complianceAnalytics: null,
  dashboardOverview: null,
  
  // Report execution
  executionResult: null,
  
  // Loading states
  loading: {
    reports: false,
    currentReport: false,
    creating: false,
    updating: false,
    deleting: false,
    executing: false,
    securityAnalytics: false,
    incidentAnalytics: false,
    threatIntelligence: false,
    operationalMetrics: false,
    complianceAnalytics: false,
    dashboardOverview: false,
  },
  
  // Error states
  error: {
    reports: null,
    currentReport: null,
    creating: null,
    updating: null,
    deleting: null,
    executing: null,
    securityAnalytics: null,
    incidentAnalytics: null,
    threatIntelligence: null,
    operationalMetrics: null,
    complianceAnalytics: null,
    dashboardOverview: null,
  },
  
  // Filters and settings
  filters: {
    type: '',
    folder: '',
    search: '',
    status: 'published',
  },
  
  // UI state
  selectedTimeRange: '30d',
  activeTab: 0,
}

const reportingSlice = createSlice({
  name: 'reporting',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    setSelectedTimeRange: (state, action) => {
      state.selectedTimeRange = action.payload
    },
    setActiveTab: (state, action) => {
      state.activeTab = action.payload
    },
    clearCurrentReport: (state) => {
      state.currentReport = null
      state.error.currentReport = null
    },
    clearExecutionResult: (state) => {
      state.executionResult = null
      state.error.executing = null
    },
    clearErrors: (state) => {
      Object.keys(state.error).forEach(key => {
        state.error[key] = null
      })
    },
  },
  extraReducers: (builder) => {
    // Fetch reports
    builder
      .addCase(fetchReports.pending, (state) => {
        state.loading.reports = true
        state.error.reports = null
      })
      .addCase(fetchReports.fulfilled, (state, action) => {
        state.loading.reports = false
        state.reports = action.payload.reports
        state.reportsPagination = action.payload.pagination
      })
      .addCase(fetchReports.rejected, (state, action) => {
        state.loading.reports = false
        state.error.reports = action.payload
      })

    // Fetch single report
    builder
      .addCase(fetchReport.pending, (state) => {
        state.loading.currentReport = true
        state.error.currentReport = null
      })
      .addCase(fetchReport.fulfilled, (state, action) => {
        state.loading.currentReport = false
        state.currentReport = action.payload
      })
      .addCase(fetchReport.rejected, (state, action) => {
        state.loading.currentReport = false
        state.error.currentReport = action.payload
      })

    // Create report
    builder
      .addCase(createReport.pending, (state) => {
        state.loading.creating = true
        state.error.creating = null
      })
      .addCase(createReport.fulfilled, (state, action) => {
        state.loading.creating = false
        state.reports.unshift(action.payload)
        state.currentReport = action.payload
      })
      .addCase(createReport.rejected, (state, action) => {
        state.loading.creating = false
        state.error.creating = action.payload
      })

    // Update report
    builder
      .addCase(updateReport.pending, (state) => {
        state.loading.updating = true
        state.error.updating = null
      })
      .addCase(updateReport.fulfilled, (state, action) => {
        state.loading.updating = false
        const index = state.reports.findIndex(r => r._id === action.payload._id)
        if (index !== -1) {
          state.reports[index] = action.payload
        }
        if (state.currentReport?._id === action.payload._id) {
          state.currentReport = action.payload
        }
      })
      .addCase(updateReport.rejected, (state, action) => {
        state.loading.updating = false
        state.error.updating = action.payload
      })

    // Delete report
    builder
      .addCase(deleteReport.pending, (state) => {
        state.loading.deleting = true
        state.error.deleting = null
      })
      .addCase(deleteReport.fulfilled, (state, action) => {
        state.loading.deleting = false
        state.reports = state.reports.filter(r => r._id !== action.payload)
        if (state.currentReport?._id === action.payload) {
          state.currentReport = null
        }
      })
      .addCase(deleteReport.rejected, (state, action) => {
        state.loading.deleting = false
        state.error.deleting = action.payload
      })

    // Execute report
    builder
      .addCase(executeReport.pending, (state) => {
        state.loading.executing = true
        state.error.executing = null
      })
      .addCase(executeReport.fulfilled, (state, action) => {
        state.loading.executing = false
        state.executionResult = action.payload
      })
      .addCase(executeReport.rejected, (state, action) => {
        state.loading.executing = false
        state.error.executing = action.payload
      })

    // Analytics cases
    builder
      .addCase(fetchSecurityAnalytics.pending, (state) => {
        state.loading.securityAnalytics = true
        state.error.securityAnalytics = null
      })
      .addCase(fetchSecurityAnalytics.fulfilled, (state, action) => {
        state.loading.securityAnalytics = false
        state.securityAnalytics = action.payload
      })
      .addCase(fetchSecurityAnalytics.rejected, (state, action) => {
        state.loading.securityAnalytics = false
        state.error.securityAnalytics = action.payload
      })

    builder
      .addCase(fetchIncidentAnalytics.pending, (state) => {
        state.loading.incidentAnalytics = true
        state.error.incidentAnalytics = null
      })
      .addCase(fetchIncidentAnalytics.fulfilled, (state, action) => {
        state.loading.incidentAnalytics = false
        state.incidentAnalytics = action.payload
      })
      .addCase(fetchIncidentAnalytics.rejected, (state, action) => {
        state.loading.incidentAnalytics = false
        state.error.incidentAnalytics = action.payload
      })

    builder
      .addCase(fetchDashboardOverview.pending, (state) => {
        state.loading.dashboardOverview = true
        state.error.dashboardOverview = null
      })
      .addCase(fetchDashboardOverview.fulfilled, (state, action) => {
        state.loading.dashboardOverview = false
        state.dashboardOverview = action.payload
      })
      .addCase(fetchDashboardOverview.rejected, (state, action) => {
        state.loading.dashboardOverview = false
        state.error.dashboardOverview = action.payload
      })
  },
})

export const {
  setFilters,
  setSelectedTimeRange,
  setActiveTab,
  clearCurrentReport,
  clearExecutionResult,
  clearErrors,
} = reportingSlice.actions

export default reportingSlice.reducer
