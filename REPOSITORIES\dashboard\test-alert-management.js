const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

async function testAlertManagement() {
  try {
    console.log('Testing Alert Management API...');
    
    // Test 1: Get alerts (should work)
    console.log('\n1. Testing GET /api/v1/alerts');
    const alertsResponse = await axios.get(`${API_BASE}/alerts?page=1&limit=5`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    console.log('✓ Success:', {
      alertsCount: alertsResponse.data.data.alerts.length,
      totalCount: alertsResponse.data.data.pagination.totalCount
    });
    
    if (alertsResponse.data.data.alerts.length > 0) {
      const firstAlert = alertsResponse.data.data.alerts[0];
      console.log('First alert ID:', firstAlert._id);
      
      // Test 2: Get specific alert
      console.log('\n2. Testing GET /api/v1/alerts/:id');
      const alertResponse = await axios.get(`${API_BASE}/alerts/${firstAlert._id}`, {
        headers: {
          'X-API-Key': 'test-api-key-12345'
        }
      });
      console.log('✓ Success: Got alert details');
      
      // Test 3: Update alert (need manage_alerts permission)
      console.log('\n3. Testing PATCH /api/v1/alerts/:id');
      try {
        const updateResponse = await axios.patch(`${API_BASE}/alerts/${firstAlert._id}`, {
          status: 'acknowledged',
          note: 'Test update from API'
        }, {
          headers: {
            'X-API-Key': 'test-api-key-12345'
          }
        });
        console.log('✓ Success: Alert updated');
      } catch (error) {
        console.log('❌ Update failed (expected - need manage_alerts permission):', error.response?.data?.message);
      }
      
      // Test 4: Delete alert (need manage_alerts permission)
      console.log('\n4. Testing DELETE /api/v1/alerts/:id');
      try {
        await axios.delete(`${API_BASE}/alerts/${firstAlert._id}`, {
          headers: {
            'X-API-Key': 'test-api-key-12345'
          }
        });
        console.log('✓ Success: Alert deleted');
      } catch (error) {
        console.log('❌ Delete failed (expected - need manage_alerts permission):', error.response?.data?.message);
      }
    }
    
    // Test 5: Get alert rules
    console.log('\n5. Testing GET /api/v1/alerts/rules');
    const rulesResponse = await axios.get(`${API_BASE}/alerts/rules`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    console.log('✓ Success:', {
      rulesCount: rulesResponse.data.data.rules.length
    });
    
    if (rulesResponse.data.data.rules.length > 0) {
      const firstRule = rulesResponse.data.data.rules[0];
      console.log('First rule ID:', firstRule._id);
      
      // Test 6: Get specific rule
      console.log('\n6. Testing GET /api/v1/alerts/rules/:id');
      const ruleResponse = await axios.get(`${API_BASE}/alerts/rules/${firstRule._id}`, {
        headers: {
          'X-API-Key': 'test-api-key-12345'
        }
      });
      console.log('✓ Success: Got rule details');
      
      // Test 7: Update rule (need manage_alerts permission)
      console.log('\n7. Testing PUT /api/v1/alerts/rules/:id');
      try {
        const updateRuleResponse = await axios.put(`${API_BASE}/alerts/rules/${firstRule._id}`, {
          enabled: !firstRule.enabled
        }, {
          headers: {
            'X-API-Key': 'test-api-key-12345'
          }
        });
        console.log('✓ Success: Rule updated');
      } catch (error) {
        console.log('❌ Rule update failed (expected - need manage_alerts permission):', error.response?.data?.message);
      }
    }
    
    console.log('\n✅ Alert Management API testing completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testAlertManagement();
