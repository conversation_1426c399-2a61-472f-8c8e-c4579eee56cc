import React, { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
} from '@mui/material'
import {
  Add,
  BarChart,
  PieChart,
  Timeline,
  TableChart,
  Map,
  Assessment,
  DragIndicator,
  Settings,
  Preview,
  Save,
} from '@mui/icons-material'

const CustomReportBuilder = () => {
  const [reportName, setReportName] = useState('')
  const [reportDescription, setReportDescription] = useState('')
  const [selectedComponents, setSelectedComponents] = useState([])
  const [previewMode, setPreviewMode] = useState(false)

  const availableComponents = [
    {
      id: 'line-chart',
      name: 'Line Chart',
      icon: <Timeline />,
      description: 'Display trends over time',
      category: 'Charts',
    },
    {
      id: 'bar-chart',
      name: 'Bar Chart',
      icon: <BarChart />,
      description: 'Compare values across categories',
      category: 'Charts',
    },
    {
      id: 'pie-chart',
      name: 'Pie Chart',
      icon: <PieChart />,
      description: 'Show proportional data',
      category: 'Charts',
    },
    {
      id: 'data-table',
      name: 'Data Table',
      icon: <TableChart />,
      description: 'Display tabular data',
      category: 'Tables',
    },
    {
      id: 'metric-card',
      name: 'Metric Card',
      icon: <Assessment />,
      description: 'Show key performance indicators',
      category: 'Metrics',
    },
    {
      id: 'geo-map',
      name: 'Geographic Map',
      icon: <Map />,
      description: 'Display location-based data',
      category: 'Maps',
    },
  ]

  const handleAddComponent = (component) => {
    setSelectedComponents([...selectedComponents, { ...component, id: `${component.id}-${Date.now()}` }])
  }

  const handleRemoveComponent = (componentId) => {
    setSelectedComponents(selectedComponents.filter(c => c.id !== componentId))
  }

  const handleSaveReport = () => {
    // TODO: Implement save functionality
    console.log('Saving report:', {
      name: reportName,
      description: reportDescription,
      components: selectedComponents,
    })
  }

  const renderComponentPalette = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Component Palette
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Drag and drop components to build your custom report
        </Typography>
        
        <List>
          {availableComponents.map((component) => (
            <ListItem
              key={component.id}
              sx={{
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                mb: 1,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
              onClick={() => handleAddComponent(component)}
            >
              <ListItemIcon>
                {component.icon}
              </ListItemIcon>
              <ListItemText
                primary={component.name}
                secondary={component.description}
              />
              <Chip label={component.category} size="small" variant="outlined" />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  )

  const renderReportCanvas = () => (
    <Card sx={{ minHeight: 600 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            Report Canvas
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<Preview />}
              onClick={() => setPreviewMode(!previewMode)}
            >
              {previewMode ? 'Edit' : 'Preview'}
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSaveReport}
              disabled={!reportName || selectedComponents.length === 0}
            >
              Save Report
            </Button>
          </Box>
        </Box>

        {selectedComponents.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: 400,
              border: 2,
              borderStyle: 'dashed',
              borderColor: 'divider',
              borderRadius: 2,
            }}
          >
            <Add sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Start Building Your Report
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Add components from the palette to create your custom report
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {selectedComponents.map((component, index) => (
              <Grid item xs={12} md={6} key={component.id}>
                <Paper
                  sx={{
                    p: 2,
                    border: 1,
                    borderColor: 'divider',
                    position: 'relative',
                    '&:hover .component-actions': {
                      opacity: 1,
                    },
                  }}
                >
                  <Box
                    className="component-actions"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      opacity: 0,
                      transition: 'opacity 0.2s',
                      display: 'flex',
                      gap: 1,
                    }}
                  >
                    <Button
                      size="small"
                      startIcon={<Settings />}
                      variant="outlined"
                    >
                      Configure
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleRemoveComponent(component.id)}
                    >
                      Remove
                    </Button>
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DragIndicator sx={{ mr: 1, color: 'text.secondary' }} />
                    {component.icon}
                    <Typography variant="subtitle1" sx={{ ml: 1 }}>
                      {component.name}
                    </Typography>
                  </Box>
                  
                  <Box
                    sx={{
                      height: 200,
                      backgroundColor: 'grey.50',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography color="text.secondary">
                      {component.name} Preview
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        )}
      </CardContent>
    </Card>
  )

  const renderReportSettings = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Report Settings
        </Typography>
        
        <TextField
          fullWidth
          label="Report Name"
          value={reportName}
          onChange={(e) => setReportName(e.target.value)}
          sx={{ mb: 2 }}
          required
        />
        
        <TextField
          fullWidth
          label="Description"
          value={reportDescription}
          onChange={(e) => setReportDescription(e.target.value)}
          multiline
          rows={3}
          sx={{ mb: 2 }}
        />
        
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Report Type</InputLabel>
          <Select
            value="custom"
            label="Report Type"
          >
            <MenuItem value="custom">Custom Report</MenuItem>
            <MenuItem value="dashboard">Dashboard</MenuItem>
            <MenuItem value="summary">Summary Report</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Data Source</InputLabel>
          <Select
            value="logs"
            label="Data Source"
          >
            <MenuItem value="logs">Security Logs</MenuItem>
            <MenuItem value="alerts">Alerts</MenuItem>
            <MenuItem value="users">User Activity</MenuItem>
            <MenuItem value="agents">Agent Data</MenuItem>
          </Select>
        </FormControl>
        
        <Alert severity="info" sx={{ mt: 2 }}>
          Custom report builder is in development. Basic functionality is available for testing.
        </Alert>
      </CardContent>
    </Card>
  )

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Custom Report Builder
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Create custom reports with drag-and-drop components and flexible layouts.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          {renderComponentPalette()}
        </Grid>
        
        <Grid item xs={12} md={6}>
          {renderReportCanvas()}
        </Grid>
        
        <Grid item xs={12} md={3}>
          {renderReportSettings()}
        </Grid>
      </Grid>
    </Box>
  )
}

export default CustomReportBuilder
