import React, { useState, useEffect } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Alert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Snackbar,
  CircularProgress,
} from '@mui/material'
import {
  Add,
  FilterList,
  Refresh,
  MoreVert,
  Notifications,
  Security,
  Settings,
  TrendingUp,
} from '@mui/icons-material'
import { useDispatch, useSelector } from 'react-redux'
import AlertsList from './components/AlertsList'
import AlertsStatistics from './components/AlertsStatistics'
import RulesList from './components/RulesList'
import RuleBuilder from './components/RuleBuilder'
import { fetchAlerts, fetchAlertRules, fetchAlertStatistics } from '../../store/slices/alertsSlice'

const Alerts = () => {
  const dispatch = useDispatch()
  const { alerts, rules, statistics, loading, error } = useSelector(state => state.alerts)

  const [activeTab, setActiveTab] = useState(0)
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null)
  const [ruleBuilderOpen, setRuleBuilderOpen] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  const [filters, setFilters] = useState({
    status: '',
    severity: '',
    timeRange: '24h',
  })

  useEffect(() => {
    // Load initial data
    dispatch(fetchAlertStatistics())
    dispatch(fetchAlerts({ page: 1, limit: 25, ...filters })) // Match default page size
    dispatch(fetchAlertRules())
  }, [dispatch, filters])

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const handleRefresh = () => {
    dispatch(fetchAlertStatistics())
    if (activeTab === 0) {
      dispatch(fetchAlerts({ page: 1, limit: 25, ...filters })) // Match default page size
    } else if (activeTab === 1) {
      dispatch(fetchAlertRules())
    }
  }

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setFilterMenuAnchor(null)
  }

  const handleCreateRule = () => {
    setRuleBuilderOpen(true)
  }

  const handleRuleCreated = (rule) => {
    setRuleBuilderOpen(false)
    setSnackbar({
      open: true,
      message: `Alert rule "${rule.name}" created successfully`,
      severity: 'success'
    })
    dispatch(fetchAlertRules())
  }

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity })
  }

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }))
  }

  if (loading && !alerts.length && !rules.length) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Alert Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor security events and manage correlation rules in real-time.
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={loading}>
            <Refresh />
          </IconButton>
          <IconButton onClick={(e) => setFilterMenuAnchor(e.currentTarget)}>
            <FilterList />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateRule}
          >
            Create Rule
          </Button>
        </Box>
      </Box>

      {/* Statistics Overview */}
      <AlertsStatistics statistics={statistics} />

      {/* Main Content Tabs */}
      <Card sx={{ mt: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Notifications />
                  Alerts
                  {statistics?.total > 0 && (
                    <Chip
                      label={statistics.total}
                      size="small"
                      color="primary"
                    />
                  )}
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Settings />
                  Rules
                  {rules?.length > 0 && (
                    <Chip
                      label={rules.length}
                      size="small"
                      color="secondary"
                    />
                  )}
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp />
                  Analytics
                </Box>
              }
            />
          </Tabs>
        </Box>

        <CardContent>
          {activeTab === 0 && (
            <AlertsList
              alerts={alerts}
              loading={loading}
              filters={filters}
              onShowSnackbar={showSnackbar}
            />
          )}
          {activeTab === 1 && (
            <RulesList
              rules={rules}
              loading={loading}
              onShowSnackbar={showSnackbar}
            />
          )}
          {activeTab === 2 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" gutterBottom>
                Analytics Dashboard
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced analytics and trending data coming soon.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleFilterChange({ status: '' })}>
          All Statuses
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange({ status: 'new' })}>
          New Alerts
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange({ status: 'acknowledged' })}>
          Acknowledged
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange({ status: 'resolved' })}>
          Resolved
        </MenuItem>
      </Menu>

      {/* Rule Builder Dialog */}
      <RuleBuilder
        open={ruleBuilderOpen}
        onClose={() => setRuleBuilderOpen(false)}
        onRuleCreated={handleRuleCreated}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <MuiAlert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </MuiAlert>
      </Snackbar>
    </Box>
  )
}

export default Alerts
