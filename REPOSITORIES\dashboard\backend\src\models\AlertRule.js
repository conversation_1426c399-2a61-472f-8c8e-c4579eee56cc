const mongoose = require('mongoose');

const alertRuleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    unique: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  enabled: {
    type: Boolean,
    default: true,
    index: true,
  },
  severity: {
    type: String,
    required: true,
    enum: ['critical', 'high', 'medium', 'low', 'informational'],
    index: true,
  },
  category: {
    type: String,
    required: true,
    enum: ['security', 'performance', 'system', 'application', 'network', 'compliance'],
    index: true,
  },
  ruleType: {
    type: String,
    required: true,
    enum: ['threshold', 'pattern', 'correlation', 'anomaly', 'sequence'],
    index: true,
  },
  conditions: {
    type: mongoose.Schema.Types.Mixed,
    required: true,
    validate: {
      validator: function(v) {
        return v && typeof v === 'object' && Object.keys(v).length > 0;
      },
      message: 'Conditions must be a non-empty object'
    }
  },
  timeWindow: {
    value: {
      type: Number,
      required: true,
      min: 1,
    },
    unit: {
      type: String,
      required: true,
      enum: ['seconds', 'minutes', 'hours', 'days'],
    },
  },
  threshold: {
    operator: {
      type: String,
      enum: ['>', '>=', '<', '<=', '==', '!='],
      default: '>',
    },
    value: {
      type: Number,
      default: 1,
    },
  },
  actions: [{
    type: {
      type: String,
      required: true,
      enum: ['create_alert', 'send_email', 'send_webhook', 'escalate', 'suppress'],
    },
    config: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    enabled: {
      type: Boolean,
      default: true,
    },
  }],
  suppressionRules: {
    enabled: {
      type: Boolean,
      default: false,
    },
    duration: {
      value: {
        type: Number,
        default: 60,
      },
      unit: {
        type: String,
        enum: ['seconds', 'minutes', 'hours'],
        default: 'minutes',
      },
    },
    conditions: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },
  escalationRules: {
    enabled: {
      type: Boolean,
      default: false,
    },
    levels: [{
      level: {
        type: Number,
        required: true,
        min: 1,
        max: 5,
      },
      delay: {
        value: {
          type: Number,
          required: true,
          min: 1,
        },
        unit: {
          type: String,
          required: true,
          enum: ['minutes', 'hours'],
        },
      },
      actions: [{
        type: {
          type: String,
          required: true,
          enum: ['send_email', 'send_webhook', 'assign_user', 'increase_priority'],
        },
        config: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
      }],
    }],
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 50,
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  isDefault: {
    type: Boolean,
    default: false,
    index: true,
  },
  priority: {
    type: Number,
    min: 1,
    max: 10,
    default: 5,
    index: true,
  },
  statistics: {
    totalTriggers: {
      type: Number,
      default: 0,
    },
    lastTriggered: {
      type: Date,
      default: null,
    },
    falsePositives: {
      type: Number,
      default: 0,
    },
    avgResolutionTime: {
      type: Number,
      default: 0,
    },
  },
  testData: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },
  metadata: {
    version: {
      type: String,
      default: '1.0.0',
    },
    source: {
      type: String,
      enum: ['user', 'system', 'import'],
      default: 'user',
    },
    lastTested: {
      type: Date,
      default: null,
    },
    performance: {
      avgExecutionTime: {
        type: Number,
        default: 0,
      },
      lastExecutionTime: {
        type: Number,
        default: 0,
      },
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for efficient querying
alertRuleSchema.index({ enabled: 1, category: 1 });
alertRuleSchema.index({ severity: 1, enabled: 1 });
alertRuleSchema.index({ ruleType: 1, enabled: 1 });
alertRuleSchema.index({ createdBy: 1, createdAt: -1 });
alertRuleSchema.index({ isDefault: 1, enabled: 1 });
alertRuleSchema.index({ tags: 1 });
alertRuleSchema.index({ 'statistics.lastTriggered': -1 });

// Virtual for time window in milliseconds
alertRuleSchema.virtual('timeWindowMs').get(function() {
  const multipliers = {
    seconds: 1000,
    minutes: 60 * 1000,
    hours: 60 * 60 * 1000,
    days: 24 * 60 * 60 * 1000,
  };
  return this.timeWindow.value * (multipliers[this.timeWindow.unit] || 1000);
});

// Virtual for suppression duration in milliseconds
alertRuleSchema.virtual('suppressionDurationMs').get(function() {
  if (!this.suppressionRules.enabled) return 0;
  
  const multipliers = {
    seconds: 1000,
    minutes: 60 * 1000,
    hours: 60 * 60 * 1000,
  };
  return this.suppressionRules.duration.value * (multipliers[this.suppressionRules.duration.unit] || 60000);
});

// Virtual for rule effectiveness
alertRuleSchema.virtual('effectiveness').get(function() {
  if (this.statistics.totalTriggers === 0) return 0;
  const falsePositiveRate = this.statistics.falsePositives / this.statistics.totalTriggers;
  return Math.max(0, (1 - falsePositiveRate) * 100);
});

// Pre-save middleware
alertRuleSchema.pre('save', function(next) {
  // Update lastModifiedBy if not set
  if (this.isModified() && !this.isModified('lastModifiedBy')) {
    // This would be set by the controller
  }
  
  // Validate conditions based on rule type
  if (this.isModified('conditions') || this.isModified('ruleType')) {
    if (!this.validateConditions()) {
      return next(new Error('Invalid conditions for rule type'));
    }
  }
  
  next();
});

// Method to validate conditions based on rule type
alertRuleSchema.methods.validateConditions = function() {
  const { ruleType, conditions } = this;

  switch (ruleType) {
    case 'threshold':
      // For threshold rules, we need at least a field to monitor
      return conditions.field && (conditions.value !== undefined || conditions.pattern);
    case 'pattern':
      return conditions.pattern || conditions.regex;
    case 'correlation':
      return conditions.events && Array.isArray(conditions.events) && conditions.events.length >= 2;
    case 'anomaly':
      return conditions.field && conditions.baseline;
    case 'sequence':
      return conditions.sequence && Array.isArray(conditions.sequence) && conditions.sequence.length >= 2;
    default:
      return true;
  }
};

// Method to convert rule to json-rules-engine format
alertRuleSchema.methods.toEngineRule = function() {
  const rule = {
    conditions: this.conditions,
    event: {
      type: 'alert-triggered',
      params: {
        ruleId: this._id,
        ruleName: this.name,
        severity: this.severity,
        category: this.category,
        actions: this.actions,
      },
    },
    priority: this.priority,
  };
  
  return rule;
};

// Static method to get default rules
alertRuleSchema.statics.getDefaultRules = function() {
  return this.find({ isDefault: true, enabled: true });
};

// Static method to get rules by category
alertRuleSchema.statics.getRulesByCategory = function(category) {
  return this.find({ category, enabled: true });
};

// Static method to get rule statistics
alertRuleSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        enabled: { $sum: { $cond: ['$enabled', 1, 0] } },
        disabled: { $sum: { $cond: ['$enabled', 0, 1] } },
        byCategory: {
          $push: {
            category: '$category',
            count: 1,
          },
        },
        bySeverity: {
          $push: {
            severity: '$severity',
            count: 1,
          },
        },
        totalTriggers: { $sum: '$statistics.totalTriggers' },
        totalFalsePositives: { $sum: '$statistics.falsePositives' },
      },
    },
  ]);
  
  return stats[0] || {
    total: 0,
    enabled: 0,
    disabled: 0,
    byCategory: [],
    bySeverity: [],
    totalTriggers: 0,
    totalFalsePositives: 0,
  };
};

module.exports = mongoose.model('AlertRule', alertRuleSchema);
