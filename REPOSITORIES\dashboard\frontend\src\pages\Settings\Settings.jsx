import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Snackbar,
} from '@mui/material'
import {
  Person as PersonIcon,
  Tune as TuneIcon,
  VpnKey as VpnKeyIcon,
  Security as SecurityIcon,
  AdminPanelSettings as AdminIcon,
  History as HistoryIcon,
} from '@mui/icons-material'
import { useSelector } from 'react-redux'

// Import tab components
import ProfileTab from './components/ProfileTab'
import PreferencesTab from './components/PreferencesTab'
import ApiKeysTab from './components/ApiKeysTab'
import SecurityTab from './components/SecurityTab'
import SystemTab from './components/SystemTab'
import ActivityTab from './components/ActivityTab'

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  )
}

const Settings = () => {
  const [tabValue, setTabValue] = useState(0)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })
  const { user } = useSelector((state) => state.auth)

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue)
  }

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity })
  }

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false })
  }

  // Define tabs based on user permissions
  const tabs = [
    {
      label: 'Profile',
      icon: <PersonIcon />,
      component: <ProfileTab onSuccess={showSnackbar} />,
      permission: null, // Always available
    },
    {
      label: 'Preferences',
      icon: <TuneIcon />,
      component: <PreferencesTab onSuccess={showSnackbar} />,
      permission: null, // Always available
    },
    {
      label: 'API Keys',
      icon: <VpnKeyIcon />,
      component: <ApiKeysTab onSuccess={showSnackbar} />,
      permission: null, // Always available
    },
    {
      label: 'Security',
      icon: <SecurityIcon />,
      component: <SecurityTab onSuccess={showSnackbar} />,
      permission: null, // Always available
    },
    {
      label: 'Activity',
      icon: <HistoryIcon />,
      component: <ActivityTab onSuccess={showSnackbar} />,
      permission: null, // Always available
    },
    {
      label: 'System',
      icon: <AdminIcon />,
      component: <SystemTab onSuccess={showSnackbar} />,
      permission: 'system_admin', // Admin only
    },
  ]

  // Filter tabs based on user permissions
  const availableTabs = tabs.filter(tab => {
    if (!tab.permission) return true
    return user?.permissions?.includes(tab.permission) || user?.role === 'admin'
  })

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Settings
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage your account settings, preferences, and system configuration.
      </Typography>

      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="settings tabs"
          >
            {availableTabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
                id={`settings-tab-${index}`}
                aria-controls={`settings-tabpanel-${index}`}
                sx={{
                  minHeight: 64,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              />
            ))}
          </Tabs>
        </Box>

        {availableTabs.map((tab, index) => (
          <TabPanel key={index} value={tabValue} index={index}>
            {tab.component}
          </TabPanel>
        ))}
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default Settings
