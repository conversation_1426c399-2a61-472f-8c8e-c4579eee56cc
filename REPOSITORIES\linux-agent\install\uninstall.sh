#!/bin/bash

# Linux Log Collection Agent Uninstallation Script
# This script removes the Linux log collection agent and all its components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AGENT_USER="linux-log-agent"
AGENT_GROUP="linux-log-agent"
INSTALL_DIR="/opt/linux-log-agent"
CONFIG_DIR="/etc/linux-log-agent"
LOG_DIR="/var/log/linux-log-agent"
SERVICE_NAME="linux-log-agent"

# Functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

confirm_uninstall() {
    echo -e "${YELLOW}WARNING: This will completely remove the Linux Log Collection Agent${NC}"
    echo "This includes:"
    echo "  - Service files"
    echo "  - Installation directory: $INSTALL_DIR"
    echo "  - Configuration directory: $CONFIG_DIR"
    echo "  - Log directory: $LOG_DIR"
    echo "  - User and group: $AGENT_USER"
    echo
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Uninstallation cancelled"
        exit 0
    fi
}

stop_and_disable_service() {
    print_info "Stopping and disabling service..."
    
    # Stop service if running
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
        print_info "Service stopped"
    fi
    
    # Disable service if enabled
    if systemctl is-enabled --quiet $SERVICE_NAME; then
        systemctl disable $SERVICE_NAME
        print_info "Service disabled"
    fi
    
    print_success "Service stopped and disabled"
}

remove_systemd_service() {
    print_info "Removing systemd service..."
    
    # Remove service file
    if [ -f /etc/systemd/system/$SERVICE_NAME.service ]; then
        rm -f /etc/systemd/system/$SERVICE_NAME.service
        print_info "Service file removed"
    fi
    
    # Reload systemd
    systemctl daemon-reload
    systemctl reset-failed
    
    print_success "Systemd service removed"
}

remove_logrotate_config() {
    print_info "Removing logrotate configuration..."
    
    if [ -f /etc/logrotate.d/linux-log-agent ]; then
        rm -f /etc/logrotate.d/linux-log-agent
        print_info "Logrotate configuration removed"
    fi
    
    print_success "Logrotate configuration removed"
}

remove_directories() {
    print_info "Removing directories..."
    
    # Remove installation directory
    if [ -d $INSTALL_DIR ]; then
        rm -rf $INSTALL_DIR
        print_info "Installation directory removed: $INSTALL_DIR"
    fi
    
    # Ask about configuration directory
    if [ -d $CONFIG_DIR ]; then
        read -p "Remove configuration directory $CONFIG_DIR? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf $CONFIG_DIR
            print_info "Configuration directory removed: $CONFIG_DIR"
        else
            print_warning "Configuration directory preserved: $CONFIG_DIR"
        fi
    fi
    
    # Ask about log directory
    if [ -d $LOG_DIR ]; then
        read -p "Remove log directory $LOG_DIR? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf $LOG_DIR
            print_info "Log directory removed: $LOG_DIR"
        else
            print_warning "Log directory preserved: $LOG_DIR"
        fi
    fi
    
    print_success "Directories processed"
}

remove_user() {
    print_info "Removing user and group..."
    
    # Remove user if exists
    if getent passwd $AGENT_USER > /dev/null 2>&1; then
        userdel $AGENT_USER
        print_info "User removed: $AGENT_USER"
    fi
    
    # Remove group if exists and no other users are in it
    if getent group $AGENT_GROUP > /dev/null 2>&1; then
        if [ -z "$(getent group $AGENT_GROUP | cut -d: -f4)" ]; then
            groupdel $AGENT_GROUP
            print_info "Group removed: $AGENT_GROUP"
        else
            print_warning "Group $AGENT_GROUP has other members, not removing"
        fi
    fi
    
    print_success "User and group processed"
}

cleanup_python_packages() {
    print_info "Python packages installed with --user flag will remain"
    print_info "You can manually remove them if needed"
}

show_post_uninstall_info() {
    print_success "Uninstallation completed!"
    echo
    print_info "The following may still exist:"
    echo "  - Python packages installed with --user flag"
    echo "  - Configuration files (if you chose to keep them)"
    echo "  - Log files (if you chose to keep them)"
    echo
    print_info "To completely clean up, you may want to:"
    echo "  - Remove any remaining configuration files"
    echo "  - Remove any remaining log files"
    echo "  - Check for any remaining Python packages"
}

# Main uninstallation process
main() {
    print_info "Starting Linux Log Collection Agent uninstallation..."
    
    check_root
    confirm_uninstall
    stop_and_disable_service
    remove_systemd_service
    remove_logrotate_config
    remove_directories
    remove_user
    cleanup_python_packages
    
    show_post_uninstall_info
}

# Run main function
main "$@"
