# Python project configuration for CI/CD pipelines
# This file contains common configurations for Python tools

[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[tool.black]
# Black code formatter configuration
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | env
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
# isort import sorting configuration
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = [
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
    "*/env/*",
    "*/.env/*"
]

[tool.mypy]
# MyPy type checking configuration
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# Per-module options
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = [
    "setuptools.*",
    "pkg_resources.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
# Pytest configuration
minversion = "6.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-report=html",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "smoke: marks tests as smoke tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
# Coverage configuration
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "*_test.py",
    "*/venv/*",
    "*/.venv/*",
    "*/env/*",
    "*/.env/*",
    "*/migrations/*",
    "setup.py",
    "*/site-packages/*",
]
branch = true

[tool.coverage.report]
# Coverage reporting
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.bandit]
# Bandit security linting configuration
exclude_dirs = ["tests", "test", ".venv", "venv", "env", ".env"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]
