#!/bin/bash

# Linux Log Collection Agent Development Setup Script
# This script sets up a development environment with virtual environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

check_python() {
    print_info "Checking Python installation..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.7 or higher."
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    print_info "Found Python $PYTHON_VERSION"
    
    # Check if python3-venv is available
    if ! python3 -c "import venv" 2>/dev/null; then
        print_error "python3-venv module is not available."
        print_info "Please install it with:"
        print_info "  Ubuntu/Debian: sudo apt install python3-venv python3-full"
        print_info "  CentOS/RHEL/Fedora: sudo dnf install python3-venv"
        print_info "  SUSE: sudo zypper install python3-venv"
        exit 1
    fi
}

install_system_packages() {
    print_info "Checking for system packages..."
    
    # Detect distribution
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
    else
        print_warning "Cannot detect Linux distribution, skipping system package installation"
        return
    fi
    
    print_info "Detected distribution: $DISTRO"
    
    case $DISTRO in
        ubuntu|debian)
            print_info "Installing system packages for Ubuntu/Debian..."
            sudo apt update
            sudo apt install -y python3-venv python3-full python3-dev
            # Try to install system packages for dependencies (optional)
            sudo apt install -y python3-requests python3-yaml python3-psutil python3-systemd python3-dateutil python3-tz 2>/dev/null || print_warning "Some system packages not available, will install via pip"
            ;;
        centos|rhel|fedora)
            print_info "Installing system packages for CentOS/RHEL/Fedora..."
            if command -v dnf &> /dev/null; then
                sudo dnf install -y python3-venv python3-devel
                sudo dnf install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz 2>/dev/null || print_warning "Some system packages not available, will install via pip"
            else
                sudo yum install -y python3-devel
                sudo yum install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz 2>/dev/null || print_warning "Some system packages not available, will install via pip"
            fi
            ;;
        suse|opensuse*)
            print_info "Installing system packages for SUSE..."
            sudo zypper install -y python3-venv python3-devel
            sudo zypper install -y python3-requests python3-PyYAML python3-psutil python3-systemd python3-dateutil python3-pytz 2>/dev/null || print_warning "Some system packages not available, will install via pip"
            ;;
        *)
            print_warning "Unknown distribution, skipping system package installation"
            ;;
    esac
}

create_virtual_environment() {
    print_info "Creating virtual environment..."
    
    if [ -d "$VENV_DIR" ]; then
        print_warning "Virtual environment already exists at $VENV_DIR"
        read -p "Do you want to recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$VENV_DIR"
        else
            print_info "Using existing virtual environment"
            return
        fi
    fi
    
    python3 -m venv "$VENV_DIR"
    print_success "Virtual environment created at $VENV_DIR"
}

install_dependencies() {
    print_info "Installing Python dependencies..."
    
    # Activate virtual environment and install dependencies
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r "$SCRIPT_DIR/requirements.txt"
    
    print_success "Dependencies installed in virtual environment"
}

create_activation_script() {
    print_info "Creating activation script..."
    
    cat > "$SCRIPT_DIR/activate_dev.sh" << 'EOF'
#!/bin/bash
# Activation script for development environment

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

if [ ! -d "$VENV_DIR" ]; then
    echo "Virtual environment not found. Please run setup_dev.sh first."
    exit 1
fi

echo "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

echo "Virtual environment activated!"
echo "You can now run:"
echo "  python main.py console --config config/default_config.yaml"
echo "  python test_api_client.py"
echo ""
echo "To deactivate, run: deactivate"
EOF
    
    chmod +x "$SCRIPT_DIR/activate_dev.sh"
    print_success "Activation script created: $SCRIPT_DIR/activate_dev.sh"
}

create_run_script() {
    print_info "Creating run script..."
    
    cat > "$SCRIPT_DIR/run_dev.sh" << 'EOF'
#!/bin/bash
# Run script for development environment

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

if [ ! -d "$VENV_DIR" ]; then
    echo "Virtual environment not found. Please run setup_dev.sh first."
    exit 1
fi

# Run with virtual environment
exec "$VENV_DIR/bin/python" "$SCRIPT_DIR/main.py" "$@"
EOF
    
    chmod +x "$SCRIPT_DIR/run_dev.sh"
    print_success "Run script created: $SCRIPT_DIR/run_dev.sh"
}

show_usage_info() {
    print_success "Development environment setup completed!"
    echo
    print_info "Usage options:"
    echo
    echo "1. Activate virtual environment manually:"
    echo "   source ./activate_dev.sh"
    echo "   python main.py console --config config/default_config.yaml"
    echo
    echo "2. Run directly with wrapper script:"
    echo "   ./run_dev.sh console --config config/default_config.yaml"
    echo
    echo "3. Test API client:"
    echo "   ./run_dev.sh test_api_client.py"
    echo "   # or after activation:"
    echo "   python test_api_client.py"
    echo
    print_info "Configuration file: config/default_config.yaml"
    print_info "Virtual environment: $VENV_DIR"
    echo
    print_warning "Note: For production deployment, use the install/install.sh script instead."
}

# Main setup process
main() {
    print_info "Setting up Linux Log Collection Agent development environment..."
    
    check_python
    install_system_packages
    create_virtual_environment
    install_dependencies
    create_activation_script
    create_run_script
    
    show_usage_info
}

# Run main function
main "$@"
