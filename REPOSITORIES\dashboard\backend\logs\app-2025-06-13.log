{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-13 21:08:39:839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 9 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 9 rules\u001b[39m","timestamp":"2025-06-13 21:08:52:852"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }","index":0,"keyPattern":{"apiKeys.key":1},"keyValue":{"apiKeys.key":null}},"index":0,"keyPattern":{"apiKeys.key":1},"keyValue":{"apiKeys.key":null},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize alert system: E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.users index: apiKeys.key_1 dup key: { apiKeys.key: null }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-13 21:08:53:853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-13 21:08:53:853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-13 21:08:53:853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-13 21:08:53:853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-13 21:08:58:858"}
