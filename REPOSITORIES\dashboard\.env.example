# Environment Configuration Example
# Copy this file to .env and update the values for your environment

# Node Environment
NODE_ENV=development

# Server Configuration
PORT=5000
WEBSOCKET_PORT=5001

# Database Configuration
MONGODB_URI=***************************************************************
TIMESCALEDB_URI=postgresql://postgres:password@localhost:5432/exlog
ELASTICSEARCH_URL=http://localhost:9200
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-please
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
# Rate limiting for authentication endpoints (stricter)
AUTH_RATE_LIMIT_MAX=20
# Rate limiting for authenticated users (per minute)
AUTHENTICATED_RATE_LIMIT_MAX=200

# CORS Configuration
# For development: Allow all origins (use with caution in production)
CORS_ORIGIN=*
# For production: Specify allowed origins (comma-separated)
# CORS_ORIGIN=http://localhost:3000,http://*************:3000,http://your-domain.com

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Data Retention Configuration
# Log retention in seconds (default: 90 days = 7776000 seconds)
LOG_RETENTION_SECONDS=7776000
# Alert retention in seconds (default: 365 days = 31536000 seconds)
ALERT_RETENTION_SECONDS=31536000
# Enable automatic deletion (set to 'true' in production, 'false' in development)
ENABLE_AUTO_DELETE=false

# Frontend Configuration
# Use relative URLs for network compatibility (recommended)
REACT_APP_API_URL=/api/v1
REACT_APP_WS_URL=/ws
# Fallback URLs for direct access (when not using nginx proxy)
REACT_APP_API_URL_FALLBACK=http://localhost:5000/api/v1
REACT_APP_WS_URL_FALLBACK=ws://localhost:5001

# Development Tools
VITE_API_URL=/api/v1
