#!/bin/bash
# Security scanning script for CI/CD pipelines
# This script provides comprehensive security scanning for different project types

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_TYPE=${PROJECT_TYPE:-"auto"}
SCAN_TYPE=${SCAN_TYPE:-"all"}
OUTPUT_DIR=${OUTPUT_DIR:-"reports"}
FAIL_ON_HIGH=${FAIL_ON_HIGH:-"true"}
FAIL_ON_CRITICAL=${FAIL_ON_CRITICAL:-"true"}

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Detect project type if not specified
detect_project_type() {
    if [[ "$PROJECT_TYPE" == "auto" ]]; then
        if [[ -f "requirements.txt" || -f "setup.py" ]]; then
            PROJECT_TYPE="python"
        elif [[ -f "package.json" ]]; then
            PROJECT_TYPE="nodejs"
        elif [[ -f "Dockerfile" ]]; then
            PROJECT_TYPE="docker"
        else
            PROJECT_TYPE="generic"
        fi
    fi
    
    log_info "Detected project type: $PROJECT_TYPE"
}

# Install security tools
install_security_tools() {
    log_info "Installing security scanning tools..."
    
    # Install common tools
    if ! command -v git &> /dev/null; then
        log_error "Git is required but not installed"
        exit 1
    fi
    
    # Install Gitleaks for secret detection
    if ! command -v gitleaks &> /dev/null; then
        log_info "Installing Gitleaks..."
        curl -sSfL https://github.com/zricethezav/gitleaks/releases/latest/download/gitleaks_linux_x64.tar.gz | tar -xz
        chmod +x gitleaks
        sudo mv gitleaks /usr/local/bin/ || mv gitleaks ./
    fi
    
    # Install Trivy for vulnerability scanning
    if ! command -v trivy &> /dev/null; then
        log_info "Installing Trivy..."
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
    fi
    
    log_success "Security tools installation completed"
}

# Secret detection
scan_secrets() {
    log_info "Running secret detection scan..."
    
    # Gitleaks scan
    if command -v gitleaks &> /dev/null; then
        log_info "Running Gitleaks secret detection..."
        gitleaks detect --source . --report-format json --report-path "$OUTPUT_DIR/gitleaks.json" || true
        gitleaks detect --source . --verbose || true
        log_success "Gitleaks scan completed"
    else
        log_warning "Gitleaks not available, skipping secret detection"
    fi
    
    # Custom secret patterns
    log_info "Scanning for common secret patterns..."
    SECRET_PATTERNS=(
        "password\s*=\s*['\"][^'\"]{8,}['\"]"
        "api[_-]?key\s*=\s*['\"][^'\"]{16,}['\"]"
        "secret\s*=\s*['\"][^'\"]{16,}['\"]"
        "token\s*=\s*['\"][^'\"]{16,}['\"]"
        "-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----"
    )
    
    secret_found=false
    for pattern in "${SECRET_PATTERNS[@]}"; do
        if grep -r -E "$pattern" . --exclude-dir=.git --exclude-dir=node_modules --exclude-dir=.cache; then
            secret_found=true
        fi
    done
    
    if [[ "$secret_found" == "true" ]]; then
        log_warning "Potential secrets found in code"
    else
        log_success "No obvious secrets detected"
    fi
}

# Python-specific security scans
scan_python_security() {
    log_info "Running Python security scans..."
    
    # Bandit security linting
    if command -v bandit &> /dev/null || python3 -m bandit --help &> /dev/null; then
        log_info "Running Bandit security scan..."
        python3 -m bandit -r . -f json -o "$OUTPUT_DIR/bandit.json" || true
        python3 -m bandit -r . -f txt || true
        log_success "Bandit scan completed"
    else
        log_info "Installing Bandit..."
        pip3 install bandit[toml]
        python3 -m bandit -r . -f json -o "$OUTPUT_DIR/bandit.json" || true
        python3 -m bandit -r . -f txt || true
    fi
    
    # Safety dependency check
    if command -v safety &> /dev/null || python3 -m safety --help &> /dev/null; then
        log_info "Running Safety dependency check..."
        python3 -m safety check --json --output "$OUTPUT_DIR/safety.json" || true
        python3 -m safety check || true
        log_success "Safety scan completed"
    else
        log_info "Installing Safety..."
        pip3 install safety
        python3 -m safety check --json --output "$OUTPUT_DIR/safety.json" || true
        python3 -m safety check || true
    fi
}

# Node.js-specific security scans
scan_nodejs_security() {
    log_info "Running Node.js security scans..."
    
    # npm audit
    if [[ -f "package.json" ]]; then
        log_info "Running npm audit..."
        npm audit --audit-level=moderate --json > "$OUTPUT_DIR/npm-audit.json" || true
        npm audit --audit-level=moderate || true
        log_success "npm audit completed"
    fi
    
    # Snyk scan (if token available)
    if [[ ! -z "${SNYK_TOKEN:-}" ]]; then
        if ! command -v snyk &> /dev/null; then
            log_info "Installing Snyk..."
            npm install -g snyk
        fi
        
        log_info "Running Snyk security scan..."
        snyk test --json > "$OUTPUT_DIR/snyk.json" || true
        snyk test || true
        log_success "Snyk scan completed"
    else
        log_info "SNYK_TOKEN not set, skipping Snyk scan"
    fi
}

# Docker security scans
scan_docker_security() {
    log_info "Running Docker security scans..."
    
    # Trivy filesystem scan
    if command -v trivy &> /dev/null; then
        log_info "Running Trivy filesystem scan..."
        trivy fs --format json --output "$OUTPUT_DIR/trivy-fs.json" . || true
        trivy fs . || true
        log_success "Trivy filesystem scan completed"
    fi
    
    # Dockerfile best practices check
    if [[ -f "Dockerfile" ]]; then
        log_info "Checking Dockerfile best practices..."
        
        # Check for common issues
        dockerfile_issues=()
        
        if grep -q "FROM.*:latest" Dockerfile; then
            dockerfile_issues+=("Using 'latest' tag is not recommended")
        fi
        
        if ! grep -q "USER" Dockerfile; then
            dockerfile_issues+=("No USER instruction found - running as root")
        fi
        
        if grep -q "ADD http" Dockerfile; then
            dockerfile_issues+=("Using ADD with URLs is not recommended, use RUN wget/curl instead")
        fi
        
        if [[ ${#dockerfile_issues[@]} -gt 0 ]]; then
            log_warning "Dockerfile issues found:"
            for issue in "${dockerfile_issues[@]}"; do
                echo "  - $issue"
            done
        else
            log_success "Dockerfile follows best practices"
        fi
    fi
}

# Infrastructure as Code security
scan_iac_security() {
    log_info "Running Infrastructure as Code security scans..."
    
    # Check for IaC files
    iac_files=(
        "docker-compose.yml"
        "docker-compose.yaml"
        "*.tf"
        "*.yaml"
        "*.yml"
    )
    
    iac_found=false
    for pattern in "${iac_files[@]}"; do
        if ls $pattern 1> /dev/null 2>&1; then
            iac_found=true
            break
        fi
    done
    
    if [[ "$iac_found" == "true" ]]; then
        # Install and run Checkov
        if ! command -v checkov &> /dev/null; then
            log_info "Installing Checkov..."
            pip3 install checkov
        fi
        
        log_info "Running Checkov IaC security scan..."
        checkov -d . --framework dockerfile docker_compose terraform --output json --output-file "$OUTPUT_DIR/checkov.json" || true
        checkov -d . --framework dockerfile docker_compose terraform || true
        log_success "Checkov scan completed"
    else
        log_info "No IaC files found, skipping IaC security scan"
    fi
}

# License compliance check
scan_license_compliance() {
    log_info "Running license compliance check..."
    
    if [[ "$PROJECT_TYPE" == "python" && -f "requirements.txt" ]]; then
        log_info "Checking Python package licenses..."
        pip3 install pip-licenses
        pip-licenses --format=json --output-file="$OUTPUT_DIR/python-licenses.json"
        pip-licenses --format=csv --output-file="$OUTPUT_DIR/python-licenses.csv"
        pip-licenses
    fi
    
    if [[ "$PROJECT_TYPE" == "nodejs" && -f "package.json" ]]; then
        log_info "Checking Node.js package licenses..."
        npm install -g license-checker
        license-checker --json --out "$OUTPUT_DIR/nodejs-licenses.json"
        license-checker --csv --out "$OUTPUT_DIR/nodejs-licenses.csv"
        license-checker
    fi
    
    log_success "License compliance check completed"
}

# Generate security report
generate_security_report() {
    log_info "Generating security report summary..."
    
    cat > "$OUTPUT_DIR/security-summary.md" << 'EOF'
# Security Scan Summary

This report summarizes the security scanning results.

## Scans Performed

EOF
    
    # List all generated reports
    if [[ -f "$OUTPUT_DIR/gitleaks.json" ]]; then
        echo "- ✅ Secret Detection (Gitleaks)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/bandit.json" ]]; then
        echo "- ✅ Python Security Linting (Bandit)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/safety.json" ]]; then
        echo "- ✅ Python Dependency Check (Safety)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/npm-audit.json" ]]; then
        echo "- ✅ Node.js Dependency Check (npm audit)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/snyk.json" ]]; then
        echo "- ✅ Advanced Security Scan (Snyk)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/trivy-fs.json" ]]; then
        echo "- ✅ Filesystem Vulnerability Scan (Trivy)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    if [[ -f "$OUTPUT_DIR/checkov.json" ]]; then
        echo "- ✅ Infrastructure as Code Security (Checkov)" >> "$OUTPUT_DIR/security-summary.md"
    fi
    
    echo "" >> "$OUTPUT_DIR/security-summary.md"
    echo "## Report Files" >> "$OUTPUT_DIR/security-summary.md"
    echo "" >> "$OUTPUT_DIR/security-summary.md"
    
    find "$OUTPUT_DIR" -name "*.json" -o -name "*.csv" | while read file; do
        echo "- \`$file\`" >> "$OUTPUT_DIR/security-summary.md"
    done
    
    log_success "Security report generated: $OUTPUT_DIR/security-summary.md"
}

# Main execution
main() {
    log_info "Starting security scanning..."
    log_info "Configuration:"
    log_info "  Project type: $PROJECT_TYPE"
    log_info "  Scan type: $SCAN_TYPE"
    log_info "  Output directory: $OUTPUT_DIR"
    
    detect_project_type
    install_security_tools
    
    # Run scans based on type
    if [[ "$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "secrets" ]]; then
        scan_secrets
    fi
    
    if [[ "$PROJECT_TYPE" == "python" && ("$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "dependencies") ]]; then
        scan_python_security
    fi
    
    if [[ "$PROJECT_TYPE" == "nodejs" && ("$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "dependencies") ]]; then
        scan_nodejs_security
    fi
    
    if [[ ("$PROJECT_TYPE" == "docker" || -f "Dockerfile") && ("$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "docker") ]]; then
        scan_docker_security
    fi
    
    if [[ "$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "iac" ]]; then
        scan_iac_security
    fi
    
    if [[ "$SCAN_TYPE" == "all" || "$SCAN_TYPE" == "licenses" ]]; then
        scan_license_compliance
    fi
    
    generate_security_report
    
    log_success "Security scanning completed!"
    log_info "Reports available in: $OUTPUT_DIR"
}

# Error handling
handle_error() {
    log_error "An error occurred during security scanning"
    log_error "Line $1: Command '$2' failed with exit code $3"
    exit 1
}

# Set up error handling
trap 'handle_error $LINENO "$BASH_COMMAND" $?' ERR

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
