#!/usr/bin/env python3
"""
Windows Service Runner for Python Logging Agent

This is a standalone script that can be used to install and run the
Python Logging Agent as a Windows service. It sets up the proper
Python path and imports before starting the service.
"""

import sys
import os
import logging
from pathlib import Path

def setup_service_environment():
    """Set up the environment for service execution."""
    try:
        # Get the directory where this script is located (project root)
        script_dir = Path(__file__).parent.absolute()

        # Add project root to Python path if not already there
        script_dir_str = str(script_dir)
        if script_dir_str not in sys.path:
            sys.path.insert(0, script_dir_str)

        # Change working directory to project root
        os.chdir(script_dir_str)

        # Set up basic logging for debugging
        log_dir = script_dir / "logs"
        log_dir.mkdir(exist_ok=True)

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'service_runner.log'),
                logging.StreamHandler()
            ]
        )

        logger = logging.getLogger(__name__)
        logger.info(f"Service environment set up. Working directory: {script_dir_str}")
        logger.info(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

        return True

    except Exception as e:
        print(f"Error setting up service environment: {e}")
        return False

# Set up environment before any other imports
if not setup_service_environment():
    sys.exit(1)

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False
    print("Error: pywin32 is required for Windows service functionality")
    print("Install with: pip install pywin32")
    sys.exit(1)

# Now import our service classes
from service.windows_service import PythonLoggingAgentService


class PythonLoggingAgentServiceRunner(win32serviceutil.ServiceFramework, PythonLoggingAgentService):
    """Standalone Windows service runner for the Python Logging Agent."""

    # Service configuration
    _svc_name_ = "PythonLoggingAgent"
    _svc_display_name_ = "Python Logging Agent"
    _svc_description_ = "Collects and standardizes Windows logs for security monitoring"

    def __init__(self, args):
        """Initialize the service."""
        try:
            # Set up environment again to ensure it's correct
            setup_service_environment()

            # Log initialization
            logger = logging.getLogger(__name__)
            logger.info("Initializing PythonLoggingAgentServiceRunner")

            # Initialize parent classes
            win32serviceutil.ServiceFramework.__init__(self, args)
            PythonLoggingAgentService.__init__(self, args)

            logger.info("Service runner initialized successfully")

        except Exception as e:
            # Log to Windows Event Log if possible
            try:
                servicemanager.LogErrorMsg(f"Error initializing service: {e}")
            except:
                pass
            # Also log to file
            logger = logging.getLogger(__name__)
            logger.error(f"Error initializing service: {e}")
            raise


def install_service():
    """Install the Windows service."""
    try:
        # Use the full path to this script for service installation
        script_path = str(Path(__file__).absolute())

        win32serviceutil.HandleCommandLine(
            PythonLoggingAgentServiceRunner,
            argv=[script_path, 'install']
        )
        print(f"Service '{PythonLoggingAgentServiceRunner._svc_display_name_}' installed successfully")
        print(f"Service script path: {script_path}")
        return True
    except Exception as e:
        print(f"Error installing service: {e}")
        return False


def remove_service():
    """Remove the Windows service."""
    try:
        script_path = str(Path(__file__).absolute())

        win32serviceutil.HandleCommandLine(
            PythonLoggingAgentServiceRunner,
            argv=[script_path, 'remove']
        )
        print(f"Service '{PythonLoggingAgentServiceRunner._svc_display_name_}' removed successfully")
        return True
    except Exception as e:
        print(f"Error removing service: {e}")
        return False


def start_service():
    """Start the Windows service."""
    try:
        win32serviceutil.StartService(PythonLoggingAgentServiceRunner._svc_name_)
        print(f"Service '{PythonLoggingAgentServiceRunner._svc_display_name_}' started successfully")
        return True
    except Exception as e:
        print(f"Error starting service: {e}")
        return False


def stop_service():
    """Stop the Windows service."""
    try:
        win32serviceutil.StopService(PythonLoggingAgentServiceRunner._svc_name_)
        print(f"Service '{PythonLoggingAgentServiceRunner._svc_display_name_}' stopped successfully")
        return True
    except Exception as e:
        print(f"Error stopping service: {e}")
        return False


def get_service_status():
    """Get the current service status."""
    try:
        status = win32serviceutil.QueryServiceStatus(PythonLoggingAgentServiceRunner._svc_name_)

        status_map = {
            win32service.SERVICE_STOPPED: "Stopped",
            win32service.SERVICE_START_PENDING: "Start Pending",
            win32service.SERVICE_STOP_PENDING: "Stop Pending",
            win32service.SERVICE_RUNNING: "Running",
            win32service.SERVICE_CONTINUE_PENDING: "Continue Pending",
            win32service.SERVICE_PAUSE_PENDING: "Pause Pending",
            win32service.SERVICE_PAUSED: "Paused"
        }

        current_state = status_map.get(status[1], "Unknown")
        print(f"Service '{PythonLoggingAgentServiceRunner._svc_display_name_}' status: {current_state}")
        return current_state
    except Exception as e:
        print(f"Error getting service status: {e}")
        return None


def run_service_debug():
    """Run the service in debug mode (console)."""
    try:
        script_path = str(Path(__file__).absolute())

        print(f"Running service in debug mode...")
        print(f"Script path: {script_path}")
        print("Press Ctrl+C to stop.")

        win32serviceutil.HandleCommandLine(
            PythonLoggingAgentServiceRunner,
            argv=[script_path, 'debug']
        )
        return True
    except Exception as e:
        print(f"Error running service in debug mode: {e}")
        return False


def main():
    """Main entry point for service management."""
    try:
        if len(sys.argv) == 1:
            # No arguments - try to start as service
            logger = logging.getLogger(__name__)
            logger.info("Starting service dispatcher")

            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(PythonLoggingAgentServiceRunner)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            # Handle command line arguments
            command = sys.argv[1].lower()

            if command == 'install':
                install_service()
            elif command == 'remove':
                remove_service()
            elif command == 'start':
                start_service()
            elif command == 'stop':
                stop_service()
            elif command == 'status':
                get_service_status()
            elif command == 'debug':
                run_service_debug()
            else:
                print("Usage: python service_runner.py [install|remove|start|stop|status|debug]")

    except Exception as e:
        print(f"Error in main: {e}")
        logger = logging.getLogger(__name__)
        logger.error(f"Error in main: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
