import React, { useEffect, useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Collapse,
  Tooltip,
  Badge,
} from '@mui/material'
import {
  Search,
  Refresh,
  FilterList,
  Download,
  Visibility,
  ExpandMore,
  ExpandLess,
  Clear,
  DateRange,
  GetApp,
} from '@mui/icons-material'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { useDispatch, useSelector } from 'react-redux'
import { fetchLogs, setFilters, clearFilters, searchLogs } from '../../store/slices/logsSlice'
import LoadingSpinner from '../../components/Common/LoadingSpinner'
import { format } from 'date-fns'

const getLogLevelColor = (level) => {
  switch (level.toLowerCase()) {
    case 'critical':
      return 'error'
    case 'error':
      return 'warning'
    case 'warning':
      return 'info'
    case 'info':
      return 'success'
    case 'debug':
      return 'default'
    default:
      return 'default'
  }
}

const getSourceColor = (source) => {
  switch (source) {
    // Windows Agent Categories
    case 'System':
      return 'primary'
    case 'Application':
      return 'secondary'
    case 'Security':
      return 'error'
    case 'Network':
      return 'info'
    case 'Custom':
      return 'default'
    // Linux Agent Categories
    case 'Auth':
      return 'warning'
    case 'Kernel':
      return 'error'
    case 'Service':
      return 'success'
    case 'Scheduler':
      return 'info'
    case 'Hardware':
      return 'secondary'
    case 'Systemd':
      return 'primary'
    case 'Journal':
      return 'default'
    default:
      return 'default'
  }
}

const getSourceDisplayName = (source) => {
  switch (source) {
    case 'Auth':
      return 'Authentication'
    case 'Systemd':
      return 'System Management'
    case 'Scheduler':
      return 'Scheduled Tasks'
    default:
      return source
  }
}

const Logs = () => {
  const dispatch = useDispatch()
  const { logs, pagination, filters, isLoading, error } = useSelector((state) => state.logs)

  const [localFilters, setLocalFilters] = useState({
    search: '',
    source: '',
    logLevel: '',
    host: '',
    startTime: null,
    endTime: null,
  })

  const [selectedLog, setSelectedLog] = useState(null)
  const [showLogDetail, setShowLogDetail] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  useEffect(() => {
    dispatch(fetchLogs({ page: 1, limit: 50 }))
  }, [dispatch])

  // Helper function to clean filters and remove empty values
  const cleanFilters = (filtersObj) => {
    return Object.keys(filtersObj).reduce((acc, key) => {
      const value = filtersObj[key]
      if (value !== '' && value !== null && value !== undefined) {
        acc[key] = value
      }
      return acc
    }, {})
  }

  const handleFilterChange = (field, value) => {
    setLocalFilters(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleApplyFilters = () => {
    const filtersToApply = { ...localFilters }

    // Format dates for API
    if (filtersToApply.startTime) {
      filtersToApply.startTime = filtersToApply.startTime.toISOString()
    }
    if (filtersToApply.endTime) {
      filtersToApply.endTime = filtersToApply.endTime.toISOString()
    }

    // Remove empty string values to avoid validation errors
    const cleanedFilters = cleanFilters(filtersToApply)

    dispatch(setFilters(cleanedFilters))
    dispatch(fetchLogs({
      page: 1,
      limit: 50,
      ...cleanedFilters,
    }))
  }

  const handleClearFilters = () => {
    setLocalFilters({
      search: '',
      source: '',
      logLevel: '',
      host: '',
      startTime: null,
      endTime: null,
    })
    dispatch(clearFilters())
    dispatch(fetchLogs({ page: 1, limit: 50 }))
  }

  const handleRefresh = () => {
    // Clean filters to avoid validation errors
    const cleanedFilters = cleanFilters(filters)

    dispatch(fetchLogs({
      page: pagination.currentPage,
      limit: 50,
      ...cleanedFilters,
    }))
  }

  const handlePageChange = (event, page) => {
    // Clean filters to avoid validation errors
    const cleanedFilters = cleanFilters(filters)

    dispatch(fetchLogs({
      page,
      limit: 50,
      ...cleanedFilters,
    }))
  }

  const handleViewLogDetail = (log) => {
    setSelectedLog(log)
    setShowLogDetail(true)
  }

  const handleCloseLogDetail = () => {
    setShowLogDetail(false)
    setSelectedLog(null)
  }

  const handleExportLogs = async () => {
    try {
      // This would typically call an export API endpoint
      setSnackbar({
        open: true,
        message: 'Export functionality will be implemented soon',
        severity: 'info'
      })
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Export failed: ' + error.message,
        severity: 'error'
      })
    }
  }

  const handleAdvancedSearch = () => {
    // Clean filters to avoid validation errors
    const cleanFilters = {}
    if (localFilters.source && localFilters.source !== '') cleanFilters.source = localFilters.source
    if (localFilters.logLevel && localFilters.logLevel !== '') cleanFilters.logLevel = localFilters.logLevel
    if (localFilters.host && localFilters.host !== '') cleanFilters.host = localFilters.host

    const timeRange = {}
    if (localFilters.startTime) timeRange.start = localFilters.startTime.toISOString()
    if (localFilters.endTime) timeRange.end = localFilters.endTime.toISOString()

    const searchParams = {
      query: localFilters.search || undefined,
      filters: cleanFilters,
      timeRange: Object.keys(timeRange).length > 0 ? timeRange : undefined,
      limit: 50,
    }

    // Remove undefined values
    Object.keys(searchParams).forEach(key => {
      if (searchParams[key] === undefined) {
        delete searchParams[key]
      }
    })

    dispatch(searchLogs(searchParams))
  }

  const formatTimestamp = (timestamp) => {
    return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')
  }

  if (isLoading && logs.length === 0) {
    return <LoadingSpinner message="Loading logs..." />
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          Log Management
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Search, filter, and analyze security logs from across your infrastructure.
        </Typography>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Filters
              </Typography>
              <Button
                startIcon={showAdvancedFilters ? <ExpandLess /> : <ExpandMore />}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                variant="text"
              >
                Advanced Filters
              </Button>
            </Box>

            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Search"
                  value={localFilters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  placeholder="Search logs..."
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Source</InputLabel>
                <Select
                  value={localFilters.source}
                  label="Source"
                  onChange={(e) => handleFilterChange('source', e.target.value)}
                >
                  <MenuItem value="">All Sources</MenuItem>
                  {/* Windows Agent Categories */}
                  <MenuItem value="System">System</MenuItem>
                  <MenuItem value="Application">Application</MenuItem>
                  <MenuItem value="Security">Security</MenuItem>
                  <MenuItem value="Network">Network</MenuItem>
                  <MenuItem value="Custom">Custom</MenuItem>
                  {/* Linux Agent Categories */}
                  <MenuItem value="Auth">Auth</MenuItem>
                  <MenuItem value="Kernel">Kernel</MenuItem>
                  <MenuItem value="Service">Service</MenuItem>
                  <MenuItem value="Scheduler">Scheduler</MenuItem>
                  <MenuItem value="Hardware">Hardware</MenuItem>
                  <MenuItem value="Systemd">Systemd</MenuItem>
                  <MenuItem value="Journal">Journal</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Log Level</InputLabel>
                <Select
                  value={localFilters.logLevel}
                  label="Log Level"
                  onChange={(e) => handleFilterChange('logLevel', e.target.value)}
                >
                  <MenuItem value="">All Levels</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                  <MenuItem value="error">Error</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="debug">Debug</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Host"
                value={localFilters.host}
                onChange={(e) => handleFilterChange('host', e.target.value)}
                placeholder="Filter by host..."
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleApplyFilters}
                  startIcon={<FilterList />}
                  size="small"
                >
                  Apply
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleClearFilters}
                  startIcon={<Clear />}
                  size="small"
                >
                  Clear
                </Button>
              </Box>
            </Grid>
          </Grid>

          {/* Advanced Filters */}
          <Collapse in={showAdvancedFilters}>
            <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
              <Typography variant="subtitle1" gutterBottom>
                Time Range
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <DateTimePicker
                    label="Start Time"
                    value={localFilters.startTime}
                    onChange={(value) => handleFilterChange('startTime', value)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <DateTimePicker
                    label="End Time"
                    value={localFilters.endTime}
                    onChange={(value) => handleFilterChange('endTime', value)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <Button
                    variant="outlined"
                    onClick={handleAdvancedSearch}
                    startIcon={<Search />}
                    fullWidth
                  >
                    Advanced Search
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Collapse>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 1, mt: 2, justifyContent: 'flex-end' }}>
            <Tooltip title="Refresh logs">
              <IconButton onClick={handleRefresh}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export logs">
              <IconButton onClick={handleExportLogs}>
                <GetApp />
              </IconButton>
            </Tooltip>
          </Box>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Log Entries ({pagination.totalCount?.toLocaleString() || 0})
            </Typography>
            {isLoading && <LoadingSpinner size={20} message="" />}
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Level</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell>Host</TableCell>
                  <TableCell>Message</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography color="text.secondary">
                        No logs found. Try adjusting your filters.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log._id || log.logId} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {formatTimestamp(log.timestamp)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={log.logLevel.toUpperCase()}
                          color={getLogLevelColor(log.logLevel)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getSourceDisplayName(log.source)}
                          color={getSourceColor(log.source)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {log.host}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            maxWidth: 300,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {log.message}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewLogDetail(log)}
                          >
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {pagination.totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={pagination.totalPages}
                page={pagination.currentPage}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Log Detail Modal */}
      <Dialog
        open={showLogDetail}
        onClose={handleCloseLogDetail}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Log Details</Typography>
            <IconButton onClick={handleCloseLogDetail}>
              <Clear />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedLog && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Log ID
                  </Typography>
                  <Typography variant="body2" fontFamily="monospace" gutterBottom>
                    {selectedLog.logId}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Timestamp
                  </Typography>
                  <Typography variant="body2" fontFamily="monospace" gutterBottom>
                    {formatTimestamp(selectedLog.timestamp)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Source
                  </Typography>
                  <Box gutterBottom>
                    <Chip
                      label={getSourceDisplayName(selectedLog.source)}
                      color={getSourceColor(selectedLog.source)}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Source Type
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedLog.sourceType}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Host
                  </Typography>
                  <Typography variant="body2" fontFamily="monospace" gutterBottom>
                    {selectedLog.host}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Log Level
                  </Typography>
                  <Box gutterBottom>
                    <Chip
                      label={selectedLog.logLevel.toUpperCase()}
                      color={getLogLevelColor(selectedLog.logLevel)}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Message
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2, mt: 1, backgroundColor: 'grey.50' }}>
                    <Typography variant="body2" fontFamily="monospace">
                      {selectedLog.message}
                    </Typography>
                  </Paper>
                </Grid>
                {selectedLog.additionalFields && Object.keys(selectedLog.additionalFields).length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Additional Fields
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2, mt: 1, backgroundColor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontSize: '0.875rem', fontFamily: 'monospace' }}>
                        {JSON.stringify(selectedLog.additionalFields, null, 2)}
                      </pre>
                    </Paper>
                  </Grid>
                )}
                {selectedLog.tags && selectedLog.tags.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Tags
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      {selectedLog.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 1, mb: 1 }}
                        />
                      ))}
                    </Box>
                  </Grid>
                )}
                {selectedLog.metadata && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Metadata
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2, mt: 1, backgroundColor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontSize: '0.875rem', fontFamily: 'monospace' }}>
                        {JSON.stringify(selectedLog.metadata, null, 2)}
                      </pre>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseLogDetail}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
    </LocalizationProvider>
  )
}

export default Logs
