#!/usr/bin/env node

/**
 * ExLog API Demo Script
 * 
 * This script demonstrates the ExLog API functionality including:
 * - User registration and authentication
 * - Log ingestion from agents
 * - Log retrieval with filtering
 * - API documentation access
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

// Demo data
const demoUser = {
  username: 'demo_user',
  email: '<EMAIL>',
  password: 'Admin123!',
  firstName: 'Demo',
  lastName: 'User',
  role: 'security_analyst'
};

const demoLogs = [
  {
    logId: 'demo_log_001',
    timestamp: new Date().toISOString(),
    source: 'System',
    sourceType: 'event',
    host: 'demo-server-01',
    logLevel: 'info',
    message: 'System startup completed successfully',
    additionalFields: {
      processId: '1234',
      userId: 'system',
      duration: '2.5s'
    },
    tags: ['startup', 'system'],
    severity: 2
  },
  {
    logId: 'demo_log_002',
    timestamp: new Date(Date.now() - 60000).toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'demo-server-01',
    logLevel: 'warning',
    message: 'Failed login attempt detected',
    additionalFields: {
      sourceIP: '*************',
      username: 'admin',
      attempts: 3
    },
    tags: ['security', 'authentication', 'failed-login'],
    severity: 3
  },
  {
    logId: 'demo_log_003',
    timestamp: new Date(Date.now() - 120000).toISOString(),
    source: 'Application',
    sourceType: 'application',
    host: 'demo-server-02',
    logLevel: 'error',
    message: 'Database connection timeout',
    additionalFields: {
      database: 'exlog_main',
      timeout: '30s',
      retryCount: 3
    },
    tags: ['database', 'error', 'timeout'],
    severity: 4
  }
];

class ExLogAPIDemo {
  constructor() {
    this.authToken = null;
    this.apiKey = null;
    this.userId = null;
  }

  async run() {
    console.log('🚀 ExLog API Demo Starting...\n');

    try {
      // Step 1: Check API health
      await this.checkHealth();

      // Step 2: Register demo user
      await this.registerUser();

      // Step 3: Login and get auth token
      await this.loginUser();

      // Step 4: Generate API key for agent
      await this.generateApiKey();

      // Step 5: Ingest demo logs
      await this.ingestLogs();

      // Step 6: Retrieve logs with filtering
      await this.retrieveLogs();

      // Step 7: Show API documentation info
      await this.showApiDocumentation();

      console.log('\n✅ Demo completed successfully!');
      console.log('\n📚 Access the interactive API documentation at:');
      console.log(`   ${API_BASE_URL}/api/docs`);

    } catch (error) {
      console.error('\n❌ Demo failed:', error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
      }
    }
  }

  async checkHealth() {
    console.log('1️⃣ Checking API health...');
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);
      console.log('   ✅ API is healthy');
      console.log(`   📊 Uptime: ${Math.round(response.data.uptime)}s`);
      console.log(`   🌍 Environment: ${response.data.environment}\n`);
    } catch (error) {
      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  async registerUser() {
    console.log('2️⃣ Registering demo user...');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/v1/auth/register`, demoUser);
      this.authToken = response.data.data.token;
      this.userId = response.data.data.user._id;
      console.log('   ✅ User registered successfully');
      console.log(`   👤 User ID: ${this.userId}`);
      console.log(`   🔑 Auth token received\n`);
    } catch (error) {
      if (error.response?.status === 400 && error.response.data.message?.includes('already exists')) {
        console.log('   ℹ️ User already exists, proceeding to login...\n');
      } else {
        throw new Error(`User registration failed: ${error.message}`);
      }
    }
  }

  async loginUser() {
    console.log('3️⃣ Logging in...');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/v1/auth/login`, {
        email: demoUser.email,
        password: demoUser.password
      });
      this.authToken = response.data.data.token;
      this.userId = response.data.data.user._id;
      console.log('   ✅ Login successful');
      console.log(`   👤 Welcome, ${response.data.data.user.firstName} ${response.data.data.user.lastName}`);
      console.log(`   🎭 Role: ${response.data.data.user.role}\n`);
    } catch (error) {
      throw new Error(`Login failed: ${error.message}`);
    }
  }

  async generateApiKey() {
    console.log('4️⃣ Getting API key for agent...');
    try {
      // Use the predefined API key that was added to the admin user
      this.apiKey = "exlog_api_key_1234567890abcdef1234567890abcdef";
      console.log('   ✅ Using predefined API key');
      console.log(`   🔐 API Key: ${this.apiKey.substring(0, 20)}...\n`);
    } catch (error) {
      throw new Error(`API key retrieval failed: ${error.message}`);
    }
  }

  async ingestLogs() {
    console.log('5️⃣ Ingesting demo logs...');
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/v1/logs`,
        { logs: demoLogs },
        {
          headers: {
            'X-API-Key': this.apiKey,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('   ✅ Logs ingested successfully');
      console.log(`   📊 Processed: ${response.data.data.processed} logs`);
      console.log(`   ❌ Failed: ${response.data.data.failed} logs`);
      console.log(`   📝 Log IDs: ${demoLogs.map(log => log.logId).join(', ')}\n`);
    } catch (error) {
      throw new Error(`Log ingestion failed: ${error.message}`);
    }
  }

  async retrieveLogs() {
    console.log('6️⃣ Retrieving logs...');
    try {
      // Get all logs
      const allLogsResponse = await axios.get(`${API_BASE_URL}/api/v1/logs`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` }
      });
      console.log(`   📋 Total logs: ${allLogsResponse.data.data.logs.length}`);

      // Get logs with filtering
      const securityLogsResponse = await axios.get(`${API_BASE_URL}/api/v1/logs?source=Security`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` }
      });
      console.log(`   🔒 Security logs: ${securityLogsResponse.data.data.logs.length}`);

      // Get error logs
      const errorLogsResponse = await axios.get(`${API_BASE_URL}/api/v1/logs?logLevel=error`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` }
      });
      console.log(`   ❌ Error logs: ${errorLogsResponse.data.data.logs.length}`);

      console.log('   ✅ Log retrieval successful\n');
    } catch (error) {
      throw new Error(`Log retrieval failed: ${error.message}`);
    }
  }

  async showApiDocumentation() {
    console.log('7️⃣ API Documentation Information...');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/docs.json`);
      console.log('   ✅ API documentation available');
      console.log(`   📖 API Title: ${response.data.info.title}`);
      console.log(`   📝 Version: ${response.data.info.version}`);
      console.log(`   🏷️ Available tags: ${response.data.tags.map(tag => tag.name).join(', ')}`);
      console.log(`   🔗 Interactive docs: ${API_BASE_URL}/api/docs`);
    } catch (error) {
      console.log('   ⚠️ API documentation not available (Swagger dependencies may not be installed)');
    }
  }
}

// Run the demo
if (require.main === module) {
  const demo = new ExLogAPIDemo();
  demo.run().catch(console.error);
}

module.exports = ExLogAPIDemo;


