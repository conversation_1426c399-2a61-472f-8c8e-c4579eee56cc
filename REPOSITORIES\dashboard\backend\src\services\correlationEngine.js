const { Engine } = require('json-rules-engine');
const AlertRule = require('../models/AlertRule');
const Alert = require('../models/Alert');
const Log = require('../models/Log');
const logger = require('../utils/logger');
const EventEmitter = require('events');

class CorrelationEngine extends EventEmitter {
  constructor() {
    super();
    this.engine = new Engine();
    this.isInitialized = false;
    this.ruleCache = new Map();
    this.logBuffer = [];
    this.bufferSize = 1000;
    this.processingInterval = 5000; // 5 seconds
    this.lastProcessTime = Date.now();
    
    // Statistics
    this.stats = {
      rulesLoaded: 0,
      logsProcessed: 0,
      alertsTriggered: 0,
      processingErrors: 0,
      avgProcessingTime: 0,
    };
    
    this.initialize();
  }

  async initialize() {
    try {
      logger.info('Initializing Correlation Engine...');

      // Add custom operators
      this.addCustomOperators();

      // Load all enabled alert rules
      await this.loadRules();

      // Set up periodic processing
      this.setupPeriodicProcessing();

      // Set up rule change listeners
      this.setupRuleChangeListeners();

      this.isInitialized = true;
      logger.info(`Correlation Engine initialized with ${this.stats.rulesLoaded} rules`);

      this.emit('initialized');
    } catch (error) {
      logger.error('Failed to initialize Correlation Engine:', error);
      throw error;
    }
  }

  addCustomOperators() {
    // Add regex operator for pattern matching
    this.engine.addOperator('regex', (factValue, jsonValue) => {
      if (!factValue || !jsonValue) return false;
      try {
        const regex = new RegExp(jsonValue, 'i'); // Case insensitive

        // If factValue is an object (full log), search through all its values
        if (typeof factValue === 'object' && factValue !== null) {
          return this.searchObjectRecursively(factValue, regex);
        }

        // If it's a string, test directly
        return regex.test(String(factValue));
      } catch (error) {
        logger.warn(`Invalid regex pattern: ${jsonValue}`, error);
        return false;
      }
    });

    // Add contains operator for simple string matching
    this.engine.addOperator('contains', (factValue, jsonValue) => {
      if (!factValue || !jsonValue) return false;

      const searchTerm = jsonValue.toLowerCase();

      // If factValue is an object (full log), search through all its values
      if (typeof factValue === 'object' && factValue !== null) {
        return this.searchObjectForString(factValue, searchTerm);
      }

      // If it's a string, search directly
      return String(factValue).toLowerCase().includes(searchTerm);
    });
  }

  // Helper method to recursively search object with regex
  searchObjectRecursively(obj, regex) {
    if (obj === null || obj === undefined) return false;

    // If it's a string, test it
    if (typeof obj === 'string') {
      return regex.test(obj);
    }

    // If it's an array, search each element
    if (Array.isArray(obj)) {
      return obj.some(item => this.searchObjectRecursively(item, regex));
    }

    // If it's an object, search all values
    if (typeof obj === 'object') {
      return Object.values(obj).some(value => this.searchObjectRecursively(value, regex));
    }

    // For other types (numbers, booleans), convert to string and test
    return regex.test(String(obj));
  }

  // Helper method to recursively search object for string
  searchObjectForString(obj, searchTerm) {
    if (obj === null || obj === undefined) return false;

    // If it's a string, search it
    if (typeof obj === 'string') {
      return obj.toLowerCase().includes(searchTerm);
    }

    // If it's an array, search each element
    if (Array.isArray(obj)) {
      return obj.some(item => this.searchObjectForString(item, searchTerm));
    }

    // If it's an object, search all values
    if (typeof obj === 'object') {
      return Object.values(obj).some(value => this.searchObjectForString(value, searchTerm));
    }

    // For other types (numbers, booleans), convert to string and search
    return String(obj).toLowerCase().includes(searchTerm);
  }

  async loadRules() {
    try {
      const rules = await AlertRule.find({ enabled: true });
      this.engine = new Engine(); // Reset engine
      this.addCustomOperators(); // Re-add custom operators after reset
      this.ruleCache.clear();

      for (const rule of rules) {
        await this.addRule(rule);
      }

      this.stats.rulesLoaded = rules.length;
      logger.info(`Loaded ${rules.length} alert rules into correlation engine`);
    } catch (error) {
      logger.error('Failed to load alert rules:', error);
      throw error;
    }
  }

  async addRule(alertRule) {
    try {
      // Convert Mongoose document to plain object to avoid serialization issues
      const ruleObj = alertRule.toObject ? alertRule.toObject() : alertRule;
      const engineRule = this.convertToEngineRule(ruleObj);
      await this.engine.addRule(engineRule);
      this.ruleCache.set(ruleObj._id.toString(), ruleObj);

      logger.debug(`Added rule: ${ruleObj.name}`);
    } catch (error) {
      logger.error(`Failed to add rule ${alertRule.name}:`, error);
      throw error;
    }
  }

  convertToEngineRule(alertRule) {
    const conditions = this.buildConditions(alertRule);
    
    return {
      conditions,
      event: {
        type: 'alert-triggered',
        params: {
          ruleId: alertRule._id.toString(),
          ruleName: alertRule.name,
          severity: alertRule.severity,
          category: alertRule.category,
          description: alertRule.description,
          actions: alertRule.actions,
          timeWindow: alertRule.timeWindow,
          threshold: alertRule.threshold,
        },
      },
      priority: alertRule.priority,
    };
  }

  buildConditions(alertRule) {
    const { ruleType, conditions, threshold, timeWindow } = alertRule;
    
    switch (ruleType) {
      case 'threshold':
        return this.buildThresholdConditions(conditions, threshold, timeWindow);
      case 'pattern':
        return this.buildPatternConditions(conditions);
      case 'correlation':
        return this.buildCorrelationConditions(conditions, timeWindow);
      case 'anomaly':
        return this.buildAnomalyConditions(conditions);
      case 'sequence':
        return this.buildSequenceConditions(conditions, timeWindow);
      default:
        throw new Error(`Unsupported rule type: ${ruleType}`);
    }
  }

  buildThresholdConditions(conditions, threshold, timeWindow) {
    // Map operators to json-rules-engine format
    const operatorMap = {
      '>': 'greaterThan',
      '>=': 'greaterThanInclusive',
      '<': 'lessThan',
      '<=': 'lessThanInclusive',
      '==': 'equal',
      '!=': 'notEqual',
    };

    return {
      all: [
        {
          fact: 'logCount',
          params: {
            field: conditions.field,
            value: conditions.value,
            timeWindow: timeWindow,
          },
          operator: operatorMap[threshold.operator] || 'greaterThan',
          value: threshold.value,
        },
      ],
    };
  }

  buildPatternConditions(conditions) {
    const patternConditions = [];

    // Handle field-value matching (e.g., logLevel: 'error')
    if (conditions.field && conditions.value) {
      patternConditions.push({
        fact: conditions.field,
        operator: 'equal',
        value: conditions.value,
      });
    }

    // Handle pattern matching - search across all log fields
    if (conditions.pattern) {
      // Use a custom operator that searches the entire log object
      patternConditions.push({
        fact: 'fullLogSearch',
        operator: 'contains',
        value: conditions.pattern,
      });
    }

    if (conditions.regex) {
      // For explicit regex patterns - search across entire log
      patternConditions.push({
        fact: 'fullLogSearch',
        operator: 'regex',
        value: conditions.regex,
      });
    }

    // Handle additional field conditions for backward compatibility
    if (conditions.logLevel) {
      patternConditions.push({
        fact: 'logLevel',
        operator: 'equal',
        value: conditions.logLevel,
      });
    }

    if (conditions.source) {
      patternConditions.push({
        fact: 'source',
        operator: 'equal',
        value: conditions.source,
      });
    }

    return { all: patternConditions };
  }

  buildCorrelationConditions(conditions, timeWindow) {
    const correlationConditions = [];
    
    for (const event of conditions.events) {
      correlationConditions.push({
        fact: 'correlatedEvents',
        params: {
          eventType: event.type,
          field: event.field,
          value: event.value,
          timeWindow: timeWindow,
        },
        operator: 'greaterThanInclusive',
        value: event.threshold || 1,
      });
    }
    
    return { all: correlationConditions };
  }

  buildAnomalyConditions(conditions) {
    return {
      all: [
        {
          fact: 'anomalyScore',
          params: {
            field: conditions.field,
            baseline: conditions.baseline,
            threshold: conditions.threshold || 2, // Standard deviations
          },
          operator: 'greaterThan',
          value: conditions.threshold || 2,
        },
      ],
    };
  }

  buildSequenceConditions(conditions, timeWindow) {
    return {
      all: [
        {
          fact: 'eventSequence',
          params: {
            sequence: conditions.sequence,
            timeWindow: timeWindow,
            strict: conditions.strict || false,
          },
          operator: 'equal',
          value: true,
        },
      ],
    };
  }

  async processLog(logData) {
    if (!this.isInitialized) {
      logger.warn('Correlation engine not initialized, buffering log');
      this.bufferLog(logData);
      return;
    }

    try {
      const startTime = Date.now();
      
      // Add log to buffer for batch processing
      this.bufferLog(logData);
      
      // Process individual log for real-time rules
      await this.processLogRealTime(logData);
      
      const processingTime = Date.now() - startTime;
      this.updateProcessingStats(processingTime);
      
    } catch (error) {
      logger.error('Error processing log in correlation engine:', error);
      this.stats.processingErrors++;
    }
  }

  bufferLog(logData) {
    this.logBuffer.push({
      ...logData,
      processedAt: Date.now(),
    });
    
    // Keep buffer size manageable
    if (this.logBuffer.length > this.bufferSize) {
      this.logBuffer = this.logBuffer.slice(-this.bufferSize);
    }
  }

  async processLogRealTime(logData) {
    try {
      // Prepare facts for rule evaluation
      const facts = await this.prepareFacts(logData);
      
      // Run the rules engine
      const results = await this.engine.run(facts);
      
      // Process triggered events
      for (const event of results.events) {
        if (event.type === 'alert-triggered') {
          await this.handleAlertTriggered(event, logData, facts);
        }
      }
      
    } catch (error) {
      logger.error('Error in real-time log processing:', error);
      throw error;
    }
  }

  async prepareFacts(logData) {
    const facts = {
      // Basic log facts
      logId: logData.logId,
      timestamp: logData.timestamp,
      source: logData.source,
      sourceType: logData.sourceType,
      host: logData.host,
      logLevel: logData.logLevel,
      message: logData.message,
      severity: logData.severity,

      // Full log object for comprehensive pattern matching
      fullLogSearch: logData,

      // Dynamic facts that require database queries
      logCount: async (params) => {
        return await this.getLogCount(params, logData);
      },

      correlatedEvents: async (params) => {
        return await this.getCorrelatedEvents(params, logData);
      },

      anomalyScore: async (params) => {
        return await this.calculateAnomalyScore(params, logData);
      },

      eventSequence: async (params) => {
        return await this.checkEventSequence(params, logData);
      },
    };

    return facts;
  }

  async getLogCount(params, currentLog) {
    const { field, value, timeWindow } = params;
    const startTime = new Date(Date.now() - this.getTimeWindowMs(timeWindow));
    
    const query = {
      timestamp: { $gte: startTime },
    };
    
    if (field && value) {
      query[field] = value;
    }
    
    return await Log.countDocuments(query);
  }

  async getCorrelatedEvents(params, currentLog) {
    const { eventType, field, value, timeWindow } = params;
    const startTime = new Date(Date.now() - this.getTimeWindowMs(timeWindow));

    const query = {
      timestamp: { $gte: startTime },
      host: currentLog.host, // Correlate by host
    };

    // Add event type filtering - this is crucial for correlation rules
    if (eventType) {
      // Map event types to actual log patterns/fields
      switch (eventType) {
        case 'login_success':
          query.$and = [
            { logLevel: 'info' },
            { message: { $regex: /login.*success|authentication.*success|logged.*in/i } }
          ];
          break;
        case 'login_failed':
          query.$and = [
            { logLevel: 'error' },
            { message: { $regex: /login.*failed|authentication.*failed|invalid.*credentials/i } }
          ];
          break;
        case 'access_denied':
          query.$and = [
            { message: { $regex: /access.*denied|permission.*denied|unauthorized/i } }
          ];
          break;
        default:
          // If eventType doesn't match known patterns, return 0 to prevent false triggers
          logger.debug(`Unknown event type for correlation: ${eventType}`);
          return 0;
      }
    }

    if (field && value) {
      query[field] = value;
    }

    const count = await Log.countDocuments(query);
    logger.debug(`Correlated events for ${eventType}: ${count}`);
    return count;
  }

  async calculateAnomalyScore(params, currentLog) {
    // Simplified anomaly detection - in production, this would be more sophisticated
    const { field, baseline, threshold } = params;
    
    // Get recent average for the field
    const recentAvg = await this.getRecentAverage(field, currentLog.host);
    const currentValue = this.extractFieldValue(currentLog, field);
    
    if (recentAvg === 0) return 0;
    
    const deviation = Math.abs(currentValue - recentAvg) / recentAvg;
    return deviation;
  }

  async checkEventSequence(params, currentLog) {
    const { sequence, timeWindow, strict } = params;
    const startTime = new Date(Date.now() - this.getTimeWindowMs(timeWindow));
    
    // Check if the sequence of events occurred in order
    const logs = await Log.find({
      host: currentLog.host,
      timestamp: { $gte: startTime },
    }).sort({ timestamp: 1 });
    
    return this.matchSequence(logs, sequence, strict);
  }

  getTimeWindowMs(timeWindow) {
    const multipliers = {
      seconds: 1000,
      minutes: 60 * 1000,
      hours: 60 * 60 * 1000,
      days: 24 * 60 * 60 * 1000,
    };
    return timeWindow.value * (multipliers[timeWindow.unit] || 60000);
  }

  async handleAlertTriggered(event, logData, facts) {
    try {
      const { params } = event;
      const alertRule = this.ruleCache.get(params.ruleId);
      
      if (!alertRule) {
        logger.error(`Alert rule not found: ${params.ruleId}`);
        return;
      }
      
      // Check suppression rules
      if (await this.isAlertSuppressed(alertRule, logData)) {
        logger.debug(`Alert suppressed for rule: ${alertRule.name}`);
        return;
      }
      
      // Create alert
      const alert = await this.createAlert(alertRule, logData, facts);
      
      // Update rule statistics
      await this.updateRuleStatistics(alertRule);
      
      // Execute actions
      await this.executeActions(alertRule.actions, alert, logData);
      
      this.stats.alertsTriggered++;
      
      // Emit event for real-time notifications
      this.emit('alertTriggered', alert);
      
      logger.info(`Alert triggered: ${alert.name} (${alert.severity})`);
      
    } catch (error) {
      logger.error('Error handling triggered alert:', error);
      throw error;
    }
  }

  async createAlert(alertRule, logData, facts) {
    const alert = new Alert({
      ruleId: alertRule._id,
      name: alertRule.name,
      description: alertRule.description,
      severity: alertRule.severity,
      triggerData: {
        log: logData,
        facts: this.sanitizeFacts(facts),
        timestamp: new Date(),
      },
      relatedLogs: [logData._id].filter(Boolean),
      metadata: {
        correlationId: logData.correlationId,
        sourceHost: logData.host,
        sourceAgent: logData.metadata?.agentId,
        eventCount: 1,
        firstOccurrence: new Date(),
        lastOccurrence: new Date(),
      },
      tags: alertRule.tags,
      priority: alertRule.priority,
    });
    
    await alert.save();
    
    // Update log with alert reference
    if (logData._id) {
      await Log.findByIdAndUpdate(logData._id, {
        $set: { alertTriggered: true },
        $push: { alertIds: alert._id },
      });
    }
    
    return alert;
  }

  sanitizeFacts(facts) {
    // Remove function facts for storage
    const sanitized = {};
    for (const [key, value] of Object.entries(facts)) {
      if (typeof value !== 'function') {
        sanitized[key] = value;
      }
    }
    return sanitized;
  }

  setupPeriodicProcessing() {
    setInterval(async () => {
      try {
        await this.processBatchedLogs();
      } catch (error) {
        logger.error('Error in periodic processing:', error);
      }
    }, this.processingInterval);
  }

  async processBatchedLogs() {
    if (this.logBuffer.length === 0) return;
    
    const logsToProcess = [...this.logBuffer];
    this.logBuffer = [];
    
    logger.debug(`Processing batch of ${logsToProcess.length} logs`);
    
    // Process batch rules that require multiple logs
    await this.processBatchRules(logsToProcess);
    
    this.stats.logsProcessed += logsToProcess.length;
  }

  async processBatchRules(logs) {
    // Implement batch processing for correlation and sequence rules
    // This is where you'd implement more complex multi-log analysis
    logger.debug('Batch rule processing completed');
  }

  setupRuleChangeListeners() {
    // In a production environment, you might use MongoDB change streams
    // For now, we'll implement a simple polling mechanism
    setInterval(async () => {
      try {
        const lastModified = await AlertRule.findOne({}, {}, { sort: { updatedAt: -1 } });
        if (lastModified && lastModified.updatedAt > this.lastProcessTime) {
          logger.info('Alert rules changed, reloading...');
          await this.loadRules();
          this.lastProcessTime = Date.now();
        }
      } catch (error) {
        logger.error('Error checking for rule changes:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  updateProcessingStats(processingTime) {
    this.stats.avgProcessingTime = 
      (this.stats.avgProcessingTime + processingTime) / 2;
  }

  getStatistics() {
    return {
      ...this.stats,
      isInitialized: this.isInitialized,
      bufferSize: this.logBuffer.length,
      cacheSize: this.ruleCache.size,
    };
  }

  // Additional helper methods would be implemented here...
  async isAlertSuppressed(alertRule, logData) {
    // Implement suppression logic
    return false;
  }

  async updateRuleStatistics(alertRule) {
    await AlertRule.findByIdAndUpdate(alertRule._id, {
      $inc: { 'statistics.totalTriggers': 1 },
      $set: { 'statistics.lastTriggered': new Date() },
    });
  }

  async executeActions(actions, alert, logData) {
    for (const action of actions) {
      if (!action.enabled) continue;
      
      try {
        switch (action.type) {
          case 'create_alert':
            // Already created
            break;
          case 'send_email':
            await this.sendEmailNotification(action.config, alert);
            break;
          case 'send_webhook':
            await this.sendWebhookNotification(action.config, alert);
            break;
          default:
            logger.warn(`Unknown action type: ${action.type}`);
        }
      } catch (error) {
        logger.error(`Error executing action ${action.type}:`, error);
      }
    }
  }

  async sendEmailNotification(config, alert) {
    // Implement email notification
    logger.debug(`Email notification would be sent for alert: ${alert.name}`);
  }

  async sendWebhookNotification(config, alert) {
    // Implement webhook notification
    logger.debug(`Webhook notification would be sent for alert: ${alert.name}`);
  }

  // Helper methods for fact calculation
  async getRecentAverage(field, host) {
    // Implement recent average calculation
    return 0;
  }

  extractFieldValue(log, field) {
    // Extract field value from log
    return log[field] || 0;
  }

  matchSequence(logs, sequence, strict) {
    // Implement sequence matching logic
    return false;
  }
}

// Singleton instance
let correlationEngineInstance = null;

function getCorrelationEngine() {
  if (!correlationEngineInstance) {
    correlationEngineInstance = new CorrelationEngine();
  }
  return correlationEngineInstance;
}

module.exports = {
  CorrelationEngine,
  getCorrelationEngine,
};
