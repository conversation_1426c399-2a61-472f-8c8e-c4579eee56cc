# Contributing to Linux Log Collection Agent

We welcome contributions to the Linux Log Collection Agent! This document provides guidelines for contributing to the project.

## 🤝 How to Contribute

### Types of Contributions

We welcome several types of contributions:

- **🐛 Bug Reports**: Report issues you encounter
- **✨ Feature Requests**: Suggest new features or improvements
- **📝 Documentation**: Improve documentation and guides
- **🔧 Code Contributions**: Fix bugs or implement new features
- **🧪 Testing**: Test on different distributions and environments
- **📊 Performance**: Optimize performance and resource usage

## 🚀 Getting Started

### Development Environment Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/linux-log-agent.git
   cd linux-log-agent
   ```

2. **Set Up Development Environment**
   ```bash
   # Create virtual environment
   python3 -m venv venv
   source venv/bin/activate
   
   # Install dependencies
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # If available
   ```

3. **Run Tests**
   ```bash
   # Run the agent in console mode for testing
   python main.py console --config config/default_config.yaml
   ```

### Development Guidelines

#### Code Style
- Follow PEP 8 Python style guidelines
- Use meaningful variable and function names
- Add docstrings to all functions and classes
- Keep functions focused and modular

#### Testing
- Test your changes on multiple Linux distributions
- Verify that existing functionality still works
- Add unit tests for new features when possible
- Test both console and service modes

#### Documentation
- Update relevant documentation for any changes
- Add comments for complex logic
- Update configuration examples if needed
- Include usage examples for new features

## 🐛 Reporting Bugs

### Before Reporting
1. Check existing issues to avoid duplicates
2. Test with the latest version
3. Gather relevant system information

### Bug Report Template
```markdown
**Environment:**
- OS: [e.g., Ubuntu 22.04]
- Python Version: [e.g., 3.10.6]
- Agent Version: [e.g., 1.2.0]

**Description:**
A clear description of the bug.

**Steps to Reproduce:**
1. Step one
2. Step two
3. Step three

**Expected Behavior:**
What you expected to happen.

**Actual Behavior:**
What actually happened.

**Logs:**
```
Include relevant log excerpts
```

**Additional Context:**
Any other relevant information.
```

## ✨ Feature Requests

### Feature Request Template
```markdown
**Feature Description:**
A clear description of the feature you'd like to see.

**Use Case:**
Describe the problem this feature would solve.

**Proposed Solution:**
Your ideas on how this could be implemented.

**Alternatives:**
Any alternative solutions you've considered.

**Additional Context:**
Any other relevant information.
```

## 🔧 Code Contributions

### Pull Request Process

1. **Create a Feature Branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```

2. **Make Your Changes**
   - Follow the coding guidelines
   - Add tests if applicable
   - Update documentation

3. **Test Your Changes**
   ```bash
   # Test in console mode
   python main.py console --config config/default_config.yaml
   
   # Test installation (in VM)
   sudo ./install.sh
   ```

4. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "Add amazing feature
   
   - Detailed description of changes
   - Any breaking changes
   - Related issue numbers"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/amazing-feature
   ```

### Pull Request Template
```markdown
**Description:**
Brief description of changes.

**Type of Change:**
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Other (please describe)

**Testing:**
- [ ] Tested on Ubuntu/Debian
- [ ] Tested on CentOS/RHEL
- [ ] Tested console mode
- [ ] Tested service mode
- [ ] Added/updated tests

**Checklist:**
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)

**Related Issues:**
Fixes #(issue number)
```

## 📋 Development Areas

### High Priority Areas
- **Performance Optimization**: Memory and CPU usage improvements
- **Additional Log Sources**: Support for more applications and services
- **Enhanced Categorization**: Improve log categorization accuracy
- **Security Features**: Enhanced security event detection
- **Cross-Platform**: Support for additional Linux distributions

### Code Structure

```
linux-agent/
├── main.py                     # Entry point
├── config/                     # Configuration management
│   ├── config_manager.py      # Configuration loading and validation
│   └── default_config.yaml    # Default configuration
├── logging_agent/              # Core agent functionality
│   ├── agent.py               # Main agent class
│   └── collectors/            # Log collectors
│       ├── base_collector.py  # Base collector class
│       ├── journalctl_collector.py
│       ├── syslog_collector.py
│       ├── auth_log_collector.py
│       └── application_log_collector.py
├── utils/                      # Utility modules
│   ├── api_client.py          # ExLog API client
│   ├── logger.py              # Logging utilities
│   └── buffer.py              # Log buffering
├── service/                    # Service management
│   └── systemd_service.py     # Systemd service wrapper
└── install/                    # Installation files
    ├── install.sh             # Installation script
    └── systemd/               # Systemd service files
```

### Adding New Collectors

To add a new log collector:

1. **Create Collector Class**
   ```python
   # logging_agent/collectors/my_collector.py
   from .base_collector import BaseLogCollector
   
   class MyCollector(BaseLogCollector):
       def collect_logs(self):
           # Implementation here
           pass
   ```

2. **Register in Agent**
   ```python
   # logging_agent/agent.py
   from .collectors.my_collector import MyCollector
   
   # Add to _initialize_collectors method
   if collection_config.get('my_logs', {}).get('enabled', False):
       self.collectors['my_logs'] = MyCollector(
           collection_config['my_logs'],
           self.logger
       )
   ```

3. **Add Configuration**
   ```yaml
   # config/default_config.yaml
   collection:
     my_logs:
       enabled: false
       # Additional configuration options
   ```

### Testing Guidelines

#### Unit Testing
```python
# tests/test_my_feature.py
import unittest
from logging_agent.collectors.my_collector import MyCollector

class TestMyCollector(unittest.TestCase):
    def test_collect_logs(self):
        # Test implementation
        pass
```

#### Integration Testing
```bash
# Test on different distributions
docker run -it ubuntu:22.04 /bin/bash
docker run -it centos:8 /bin/bash
docker run -it fedora:36 /bin/bash
```

## 📚 Documentation Contributions

### Documentation Structure
- `README.md` - Main project documentation
- `CHANGELOG.md` - Version history and changes
- `FIXES.md` - Bug fixes and improvements
- `docs/INSTALLATION.md` - Detailed installation guide
- `docs/TROUBLESHOOTING.md` - Common issues and solutions
- `docs/CONFIGURATION.md` - Configuration reference
- `docs/API.md` - API integration details

### Documentation Guidelines
- Use clear, concise language
- Include code examples where helpful
- Keep documentation up-to-date with code changes
- Use proper Markdown formatting
- Include screenshots for UI-related documentation

## 🏆 Recognition

Contributors will be recognized in:
- `CONTRIBUTORS.md` file
- Release notes for significant contributions
- GitHub contributor statistics

## 📞 Getting Help

### Development Questions
- **GitHub Discussions**: For general questions and ideas
- **GitHub Issues**: For specific bugs or feature requests
- **Code Review**: Request reviews on pull requests

### Communication Guidelines
- Be respectful and constructive
- Provide clear and detailed information
- Help others when you can
- Follow the code of conduct

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).

## 🎯 Roadmap

### Upcoming Features
- [ ] Enhanced log filtering and preprocessing
- [ ] Real-time alerting capabilities
- [ ] Web-based configuration interface
- [ ] Advanced security event correlation
- [ ] Support for custom log formats
- [ ] Performance monitoring dashboard

### Long-term Goals
- [ ] Machine learning-based log analysis
- [ ] Cloud-native deployment options
- [ ] Integration with popular SIEM platforms
- [ ] Multi-tenant support
- [ ] Advanced visualization capabilities

---

**Thank you for contributing to the Linux Log Collection Agent!** 🚀

Your contributions help make log collection more reliable and efficient for everyone.
