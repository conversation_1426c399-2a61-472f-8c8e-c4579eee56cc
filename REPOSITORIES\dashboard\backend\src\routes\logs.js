const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Log = require('../models/Log');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authenticateToken, authenticateA<PERSON><PERSON><PERSON>, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');
const { getCorrelationEngine } = require('../services/correlationEngine');

const router = express.Router();

// Middleware to normalize log data format (snake_case to camelCase)
const normalizeLogData = (req, res, next) => {
  if (req.body && req.body.logs && Array.isArray(req.body.logs)) {
    req.body.logs = req.body.logs.map(log => {
      const normalizedLog = { ...log };

      // Normalize field names from snake_case to camelCase
      if (log.log_id && !log.logId) {
        normalizedLog.logId = log.log_id;
        delete normalizedLog.log_id;
      }

      if (log.source_type && !log.sourceType) {
        normalizedLog.sourceType = log.source_type;
        delete normalizedLog.source_type;
      }

      if (log.log_level && !log.logLevel) {
        normalizedLog.logLevel = log.log_level;
        delete normalizedLog.log_level;
      }

      if (log.raw_data !== undefined && log.rawData === undefined) {
        normalizedLog.rawData = log.raw_data;
        delete normalizedLog.raw_data;
      }

      if (log.additional_fields && !log.additionalFields) {
        normalizedLog.additionalFields = log.additional_fields;
        delete normalizedLog.additional_fields;
      }

      return normalizedLog;
    });
  }
  next();
};

/**
 * @swagger
 * /api/v1/logs:
 *   post:
 *     summary: Ingest logs from agents
 *     description: Receives and processes log entries from ExLog agents
 *     tags: [Logs]
 *     security:
 *       - apiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LogIngestionRequest'
 *           examples:
 *             camelCase:
 *               summary: "Standard camelCase format"
 *               value:
 *                 logs:
 *                   - logId: "log_12345_20240101_120000"
 *                     timestamp: "2024-01-01T12:00:00.000Z"
 *                     source: "System"
 *                     sourceType: "event"
 *                     host: "server-01.example.com"
 *                     logLevel: "info"
 *                     message: "System started successfully"
 *                     additionalFields:
 *                       processId: "1234"
 *                       userId: "admin"
 *             agentFormat:
 *               summary: "Agent snake_case format (automatically normalized)"
 *               value:
 *                 logs:
 *                   - log_id: "5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e"
 *                     timestamp: "2025-05-26T16:21:38"
 *                     source: "System"
 *                     source_type: "event"
 *                     host: "DESKTOP-PLUAU4C"
 *                     log_level: "info"
 *                     message: "The time provider 'VMICTimeProvider' has indicated that the current hardware and operating environment is not supported"
 *                     raw_data: null
 *                     additional_fields:
 *                       record_number: 70192
 *                       computer_name: "DESKTOP-PLUAU4C"
 *                       event_id: 158
 *                       event_category: 0
 *                       event_type: 4
 *                       string_inserts: ["VMICTimeProvider"]
 *                       data: null
 *                       metadata:
 *                         collection_time: "2025-05-26T16:23:54.641425"
 *                         agent_version: "1.0.0"
 *                         standardizer_version: "1.0.0"
 *                         windows_event_log: true
 *                         event_log_source: "System"
 *     responses:
 *       201:
 *         description: Logs processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: "Processed 1 logs successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     processed:
 *                       type: integer
 *                       example: 1
 *                     failed:
 *                       type: integer
 *                       example: 0
 *                     results:
 *                       type: object
 *                       properties:
 *                         successful:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               logId:
 *                                 type: string
 *                               status:
 *                                 type: string
 *                                 example: success
 *                         failed:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               logId:
 *                                 type: string
 *                               status:
 *                                 type: string
 *                                 example: failed
 *                               error:
 *                                 type: string
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - Invalid API key
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', authenticateApiKey, normalizeLogData, [
  body('logs')
    .isArray({ min: 1 })
    .withMessage('Logs must be a non-empty array'),
  body('logs.*.logId')
    .notEmpty()
    .withMessage('Log ID is required'),
  body('logs.*.timestamp')
    .isISO8601()
    .withMessage('Timestamp must be in ISO8601 format'),
  body('logs.*.source')
    .isIn([
      'System', 'Application', 'Security', 'Network', 'Custom',
      'Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd', 'Journal'
    ])
    .withMessage('Invalid source type'),
  body('logs.*.sourceType')
    .isIn([
      'event', 'application', 'security', 'network', 'audit', 'performance',
      'auth', 'kernel', 'service', 'scheduler', 'hardware', 'systemd', 'syslog', 'journal'
    ])
    .withMessage('Invalid source type'),
  body('logs.*.host')
    .notEmpty()
    .withMessage('Host is required'),
  body('logs.*.logLevel')
    .isIn(['critical', 'error', 'warning', 'info', 'debug'])
    .withMessage('Invalid log level'),
  body('logs.*.message')
    .notEmpty()
    .withMessage('Message is required'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { logs } = req.body;
  const processedLogs = [];
  const failedLogs = [];

  for (const logData of logs) {
    try {
      // Add metadata
      logData.metadata = {
        ...logData.metadata,
        agentId: req.user._id.toString(),
        collectionTime: new Date(),
        processingTime: Date.now(),
      };

      // Create log entry
      const log = new Log(logData);
      await log.save();

      processedLogs.push({
        logId: logData.logId,
        status: 'success',
      });

      // Process log through correlation engine for real-time alert evaluation
      try {
        logger.info(`Processing log ${logData.logId} through correlation engine`);
        const correlationEngine = getCorrelationEngine();
        await correlationEngine.processLog({
          ...logData,
          _id: log._id, // Include the saved log ID for reference
        });
        logger.info(`Correlation engine processing completed for log ${logData.logId}`);
      } catch (correlationError) {
        logger.error(`Correlation engine processing failed for log ${logData.logId}:`, correlationError);
        // Don't fail the entire log ingestion if correlation fails
      }

    } catch (error) {
      logger.error(`Failed to process log ${logData.logId}:`, error);
      failedLogs.push({
        logId: logData.logId,
        status: 'failed',
        error: error.message,
      });
    }
  }

  logger.info(`Processed ${processedLogs.length} logs, failed ${failedLogs.length} logs from agent ${req.user._id}`);

  res.status(201).json({
    status: 'success',
    message: `Processed ${processedLogs.length} logs successfully`,
    data: {
      processed: processedLogs.length,
      failed: failedLogs.length,
      results: {
        successful: processedLogs,
        failed: failedLogs,
      },
    },
  });
}));

/**
 * @route   POST /api/v1/logs/batch
 * @desc    Ingest batched logs from agents (compressed)
 * @access  Private (API Key)
 */
router.post('/batch', authenticateApiKey, catchAsync(async (req, res) => {
  // TODO: Implement compressed batch processing
  // This would handle larger batches of logs with compression

  res.status(501).json({
    status: 'error',
    message: 'Batch log ingestion not yet implemented',
  });
}));

/**
 * @swagger
 * /api/v1/logs:
 *   get:
 *     summary: Retrieve logs with filtering and pagination
 *     description: Get logs from the system with various filtering options and pagination
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *           default: 100
 *         description: Number of logs per page
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start time for log filtering (ISO8601 format)
 *         example: "2024-01-01T00:00:00.000Z"
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End time for log filtering (ISO8601 format)
 *         example: "2024-01-02T00:00:00.000Z"
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *           enum: [System, Application, Security, Network, Custom]
 *         description: Filter by log source
 *       - in: query
 *         name: logLevel
 *         schema:
 *           type: string
 *           enum: [critical, error, warning, info, debug]
 *         description: Filter by log level
 *       - in: query
 *         name: host
 *         schema:
 *           type: string
 *         description: Filter by host name
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Full-text search in log messages
 *     responses:
 *       200:
 *         description: Logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Log'
 *                     pagination:
 *                       $ref: '#/components/schemas/PaginationResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', authenticateToken, authorize(['view_logs']), [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit must be between 1 and 1000'),
  query('startTime')
    .optional()
    .isISO8601()
    .withMessage('Start time must be in ISO8601 format'),
  query('endTime')
    .optional()
    .isISO8601()
    .withMessage('End time must be in ISO8601 format'),
  query('source')
    .optional()
    .isIn([
      'System', 'Application', 'Security', 'Network', 'Custom',
      'Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd', 'Journal'
    ])
    .withMessage('Invalid source'),
  query('logLevel')
    .optional()
    .isIn(['critical', 'error', 'warning', 'info', 'debug'])
    .withMessage('Invalid log level'),
  query('host')
    .optional()
    .isString()
    .withMessage('Host must be a string'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const {
    page = 1,
    limit = 100,
    startTime,
    endTime,
    source,
    logLevel,
    host,
    search,
  } = req.query;

  // Build query
  const query = {};

  // Time range filter
  if (startTime || endTime) {
    query.timestamp = {};
    if (startTime) query.timestamp.$gte = new Date(startTime);
    if (endTime) query.timestamp.$lte = new Date(endTime);
  }

  // Other filters
  if (source) query.source = source;
  if (logLevel) query.logLevel = logLevel;
  if (host) query.host = host;

  // Text search
  if (search) {
    query.$text = { $search: search };
  }

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Execute query
  const [logs, totalCount] = await Promise.all([
    Log.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Log.countDocuments(query),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.json({
    status: 'success',
    data: {
      logs,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        limit: parseInt(limit),
        hasNextPage,
        hasPrevPage,
      },
    },
  });
}));

/**
 * @route   GET /api/v1/logs/:logId
 * @desc    Get a specific log by ID
 * @access  Private
 */
router.get('/:logId', authenticateToken, authorize(['view_logs']), catchAsync(async (req, res) => {
  const { logId } = req.params;

  const log = await Log.findOne({ logId }).lean();

  if (!log) {
    throw new AppError('Log not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      log,
    },
  });
}));

/**
 * @route   POST /api/v1/logs/search
 * @desc    Advanced log search
 * @access  Private
 */
router.post('/search', authenticateToken, authorize(['search_logs']), [
  body('query')
    .optional()
    .isString()
    .withMessage('Query must be a string'),
  body('filters')
    .optional()
    .isObject()
    .withMessage('Filters must be an object'),
  body('timeRange')
    .optional()
    .isObject()
    .withMessage('Time range must be an object'),
  body('sort')
    .optional()
    .isObject()
    .withMessage('Sort must be an object'),
  body('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit must be between 1 and 1000'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const {
    query: searchQuery,
    filters = {},
    timeRange = {},
    sort = { timestamp: -1 },
    limit = 100,
  } = req.body;

  // Build MongoDB query
  const mongoQuery = {};

  // Text search
  if (searchQuery) {
    mongoQuery.$text = { $search: searchQuery };
  }

  // Time range
  if (timeRange.start || timeRange.end) {
    mongoQuery.timestamp = {};
    if (timeRange.start) mongoQuery.timestamp.$gte = new Date(timeRange.start);
    if (timeRange.end) mongoQuery.timestamp.$lte = new Date(timeRange.end);
  }

  // Apply filters
  Object.keys(filters).forEach(key => {
    if (filters[key] !== undefined && filters[key] !== null) {
      mongoQuery[key] = filters[key];
    }
  });

  // Execute search
  const logs = await Log.find(mongoQuery)
    .sort(sort)
    .limit(limit)
    .lean();

  res.json({
    status: 'success',
    data: {
      logs,
      count: logs.length,
      query: mongoQuery,
    },
  });
}));

/**
 * @route   GET /api/v1/logs/statistics/summary
 * @desc    Get log statistics summary
 * @access  Private
 */
router.get('/statistics/summary', authenticateToken, authorize(['view_logs']), catchAsync(async (req, res) => {
  const { timeRange = '24h' } = req.query;

  // Calculate time range
  const now = new Date();
  let startTime;

  switch (timeRange) {
    case '1h':
      startTime = new Date(now.getTime() - (60 * 60 * 1000));
      break;
    case '24h':
      startTime = new Date(now.getTime() - (24 * 60 * 60 * 1000));
      break;
    case '7d':
      startTime = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      break;
    case '30d':
      startTime = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      break;
    default:
      startTime = new Date(now.getTime() - (24 * 60 * 60 * 1000));
  }

  const statistics = await Log.getStatistics(timeRange);

  res.json({
    status: 'success',
    data: {
      timeRange,
      startTime,
      endTime: now,
      statistics,
    },
  });
}));

module.exports = router;
