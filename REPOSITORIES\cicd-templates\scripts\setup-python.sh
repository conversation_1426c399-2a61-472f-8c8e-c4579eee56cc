#!/bin/bash
# Python environment setup script for CI/CD pipelines

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PYTHON_VERSION=${PYTHON_VERSION:-"3.9"}
PIP_CACHE_DIR=${PIP_CACHE_DIR:-"$PWD/.cache/pip"}
REQUIREMENTS_FILE=${REQUIREMENTS_FILE:-"requirements.txt"}
DEV_REQUIREMENTS_FILE=${DEV_REQUIREMENTS_FILE:-"requirements-dev.txt"}
VENV_DIR=${VENV_DIR:-".venv"}
INSTALL_DEV_DEPS=${INSTALL_DEV_DEPS:-"false"}

# Main setup function
setup_python_environment() {
    log_info "Setting up Python environment..."
    
    # Check Python version
    check_python_version
    
    # Setup pip cache
    setup_pip_cache
    
    # Upgrade pip
    upgrade_pip
    
    # Install dependencies
    install_dependencies
    
    # Install development tools
    if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
        install_dev_tools
    fi
    
    # Verify installation
    verify_installation
    
    log_success "Python environment setup completed successfully!"
}

check_python_version() {
    log_info "Checking Python version..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed"
        exit 1
    fi
    
    CURRENT_VERSION=$(python3 --version | cut -d' ' -f2)
    log_info "Current Python version: $CURRENT_VERSION"
    
    # Check if version meets minimum requirements
    if ! python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
        log_error "Python 3.8 or higher is required"
        exit 1
    fi
    
    log_success "Python version check passed"
}

setup_pip_cache() {
    log_info "Setting up pip cache directory..."
    
    mkdir -p "$PIP_CACHE_DIR"
    export PIP_CACHE_DIR
    
    log_info "Pip cache directory: $PIP_CACHE_DIR"
    log_success "Pip cache setup completed"
}

upgrade_pip() {
    log_info "Upgrading pip..."
    
    python3 -m pip install --upgrade pip
    
    PIP_VERSION=$(python3 -m pip --version)
    log_info "Pip version: $PIP_VERSION"
    
    log_success "Pip upgrade completed"
}

install_dependencies() {
    log_info "Installing Python dependencies..."
    
    if [[ -f "$REQUIREMENTS_FILE" ]]; then
        log_info "Installing from $REQUIREMENTS_FILE..."
        python3 -m pip install --cache-dir "$PIP_CACHE_DIR" -r "$REQUIREMENTS_FILE"
        log_success "Main dependencies installed"
    else
        log_warning "No $REQUIREMENTS_FILE found, skipping main dependencies"
    fi
    
    if [[ "$INSTALL_DEV_DEPS" == "true" && -f "$DEV_REQUIREMENTS_FILE" ]]; then
        log_info "Installing from $DEV_REQUIREMENTS_FILE..."
        python3 -m pip install --cache-dir "$PIP_CACHE_DIR" -r "$DEV_REQUIREMENTS_FILE"
        log_success "Development dependencies installed"
    fi
}

install_dev_tools() {
    log_info "Installing development tools..."
    
    # Essential development tools
    DEV_TOOLS=(
        "pytest>=6.0"
        "pytest-cov>=2.0"
        "pytest-xdist>=2.0"
        "black>=21.0"
        "flake8>=3.8"
        "isort>=5.0"
        "mypy>=0.800"
        "bandit[toml]>=1.7"
        "safety>=1.10"
    )
    
    for tool in "${DEV_TOOLS[@]}"; do
        log_info "Installing $tool..."
        python3 -m pip install --cache-dir "$PIP_CACHE_DIR" "$tool"
    done
    
    log_success "Development tools installed"
}

verify_installation() {
    log_info "Verifying installation..."
    
    # List installed packages
    log_info "Installed packages:"
    python3 -m pip list
    
    # Check if essential tools are available
    if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
        TOOLS_TO_CHECK=("pytest" "black" "flake8" "isort" "mypy" "bandit" "safety")
        
        for tool in "${TOOLS_TO_CHECK[@]}"; do
            if command -v "$tool" &> /dev/null; then
                log_success "$tool is available"
            else
                log_warning "$tool is not available in PATH"
            fi
        done
    fi
    
    # Check Python import paths
    log_info "Python import paths:"
    python3 -c "import sys; print('\n'.join(sys.path))"
    
    log_success "Installation verification completed"
}

# Error handling
handle_error() {
    log_error "An error occurred during Python environment setup"
    log_error "Line $1: Command '$2' failed with exit code $3"
    exit 1
}

# Set up error handling
trap 'handle_error $LINENO "$BASH_COMMAND" $?' ERR

# Main execution
main() {
    log_info "Starting Python environment setup script"
    log_info "Configuration:"
    log_info "  Python version: $PYTHON_VERSION"
    log_info "  Pip cache dir: $PIP_CACHE_DIR"
    log_info "  Requirements file: $REQUIREMENTS_FILE"
    log_info "  Dev requirements file: $DEV_REQUIREMENTS_FILE"
    log_info "  Install dev deps: $INSTALL_DEV_DEPS"
    
    setup_python_environment
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
