2025-06-19 19:35:34 - LinuxLogAgent - INFO - Logging initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Linux Log Collection Agent initializing...
2025-06-19 19:35:34 - utils.api_client - INFO - Linux ExLog API Client initialized - Endpoint: http://192.168.2.38:5000/api/v1/logs
2025-06-19 19:35:34 - LinuxLogAgent - INFO - ExLog API client initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Log buffer initialized (size: 1000, max_age: 5s)
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Syslog collector initialized with 2 paths
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Syslog collector initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Auth log collector initialized with 2 paths
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Auth log collector initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Kernel log collector initialized with 1 paths
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Kernel log collector initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Journalctl collector initialized (available: True)
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Journalctl collector initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Application log collector initialized with 0 log files
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Application log collector initialized
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Initialized 5 collectors
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Linux Log Collection Agent initialized successfully
2025-06-19 19:35:34 - utils.api_client - INFO - Linux ExLog API Client started
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Monitoring syslog files: ['/var/log/syslog']
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Monitoring authentication log files: ['/var/log/auth.log']
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Monitoring kernel log files: ['/var/log/kern.log']
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Started following journal logs in real-time
2025-06-19 19:35:34 - LinuxLogAgent - WARNING - No accessible application log files found
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Collection loop started (interval: 5s)
2025-06-19 19:35:34 - LinuxLogAgent - INFO - Linux Log Collection Agent started successfully
2025-06-19 19:35:48 - LinuxLogAgent - INFO - Stopping Linux Log Collection Agent...
2025-06-19 19:35:48 - LinuxLogAgent - INFO - Collection loop stopped
2025-06-19 19:35:48 - LinuxLogAgent - INFO - Journalctl collector stopped
2025-06-19 19:35:55 - utils.api_client - WARNING - Connection error on attempt 1
