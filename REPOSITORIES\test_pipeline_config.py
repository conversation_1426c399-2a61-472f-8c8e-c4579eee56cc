#!/usr/bin/env python3
"""
GitLab CI/CD Pipeline Configuration Validator
This script validates the YAML syntax and structure of GitLab CI/CD templates and configurations.
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def log_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")

def log_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")

def log_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")

def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.END} {message}")

def validate_yaml_syntax(file_path: Path) -> tuple[bool, Optional[str]]:
    """Validate YAML syntax of a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            yaml.safe_load(f)
        return True, None
    except yaml.YAMLError as e:
        return False, str(e)
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def validate_gitlab_ci_structure(config: Dict[str, Any], file_path: Path) -> List[str]:
    """Validate GitLab CI/CD specific structure"""
    issues = []
    
    # Check for valid stages
    if 'stages' in config:
        stages = config['stages']
        if not isinstance(stages, list):
            issues.append("'stages' should be a list")
        else:
            valid_stages = {'validate', 'test', 'quality', 'security', 'build', 'deploy', '.pre', '.post'}
            for stage in stages:
                if not isinstance(stage, str):
                    issues.append(f"Stage '{stage}' should be a string")
    
    # Check for valid job structure
    for key, value in config.items():
        if key.startswith('.') or key in ['stages', 'variables', 'include', 'cache', 'default']:
            continue  # Skip special keys and hidden jobs
        
        if isinstance(value, dict):
            # This is likely a job
            if 'script' not in value and 'extends' not in value and 'include' not in value:
                issues.append(f"Job '{key}' should have 'script' or 'extends'")
            
            if 'stage' in value:
                stage = value['stage']
                if 'stages' in config and stage not in config['stages']:
                    issues.append(f"Job '{key}' references undefined stage '{stage}'")
    
    return issues

def validate_template_dependencies(config: Dict[str, Any], file_path: Path) -> List[str]:
    """Validate template dependencies and includes"""
    issues = []
    
    if 'include' in config:
        includes = config['include']
        if isinstance(includes, list):
            for include in includes:
                if isinstance(include, dict):
                    if 'project' in include and 'file' in include:
                        # Check if it's referencing our templates
                        if 'cicd-templates' in include['project']:
                            files = include['file']
                            if isinstance(files, list):
                                for file in files:
                                    template_path = Path('cicd-templates') / file
                                    if not template_path.exists():
                                        issues.append(f"Referenced template file '{file}' not found")
    
    return issues

def test_python_setup_script():
    """Test the Python setup script"""
    log_info("Testing Python setup script...")
    
    script_path = Path('cicd-templates/scripts/setup-python.sh')
    if not script_path.exists():
        log_error("Python setup script not found")
        return False
    
    # Check if script is executable (on Unix systems)
    if os.name != 'nt':  # Not Windows
        if not os.access(script_path, os.X_OK):
            log_warning("Python setup script is not executable")
    
    # Check script content for basic structure
    try:
        with open(script_path, 'r') as f:
            content = f.read()
            
        required_functions = ['setup_python_environment', 'install_dependencies', 'check_python_version']
        missing_functions = []
        
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            log_warning(f"Python setup script missing functions: {', '.join(missing_functions)}")
        else:
            log_success("Python setup script structure looks good")
            
        return len(missing_functions) == 0
        
    except Exception as e:
        log_error(f"Error reading Python setup script: {e}")
        return False

def test_nodejs_setup_script():
    """Test the Node.js setup script"""
    log_info("Testing Node.js setup script...")
    
    script_path = Path('cicd-templates/scripts/setup-nodejs.sh')
    if not script_path.exists():
        log_error("Node.js setup script not found")
        return False
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
            
        required_functions = ['setup_nodejs_environment', 'install_dependencies', 'check_nodejs_version']
        missing_functions = []
        
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            log_warning(f"Node.js setup script missing functions: {', '.join(missing_functions)}")
        else:
            log_success("Node.js setup script structure looks good")
            
        return len(missing_functions) == 0
        
    except Exception as e:
        log_error(f"Error reading Node.js setup script: {e}")
        return False

def test_configuration_files():
    """Test configuration files"""
    log_info("Testing configuration files...")
    
    config_files = [
        'cicd-templates/configs/python/.flake8',
        'cicd-templates/configs/python/pyproject.toml',
        'cicd-templates/configs/python/pytest.ini',
        'cicd-templates/configs/eslint/.eslintrc.js',
        'cicd-templates/configs/eslint/.prettierrc'
    ]
    
    all_good = True
    
    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            log_success(f"Found: {config_file}")
        else:
            log_error(f"Missing: {config_file}")
            all_good = False
    
    return all_good

def main():
    """Main validation function"""
    log_info("Starting GitLab CI/CD Pipeline Configuration Validation")
    log_info("=" * 60)
    
    # Files to validate
    yaml_files = [
        'cicd-templates/templates/base.yml',
        'cicd-templates/templates/python.yml',
        'cicd-templates/templates/nodejs.yml',
        'cicd-templates/templates/docker.yml',
        'cicd-templates/templates/security.yml',
        'backend/.gitlab-ci.yml',
        'dashboard/.gitlab-ci.yml',
        'linux-agent/.gitlab-ci.yml'
    ]
    
    total_files = len(yaml_files)
    valid_files = 0
    issues_found = []
    
    # Validate YAML syntax
    log_info("Validating YAML syntax...")
    for file_path_str in yaml_files:
        file_path = Path(file_path_str)
        
        if not file_path.exists():
            log_error(f"File not found: {file_path}")
            issues_found.append(f"Missing file: {file_path}")
            continue
        
        is_valid, error = validate_yaml_syntax(file_path)
        
        if is_valid:
            log_success(f"✓ {file_path}")
            valid_files += 1
            
            # Load and validate structure
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                if config:
                    # Validate GitLab CI structure
                    structure_issues = validate_gitlab_ci_structure(config, file_path)
                    if structure_issues:
                        for issue in structure_issues:
                            log_warning(f"  {issue}")
                            issues_found.append(f"{file_path}: {issue}")
                    
                    # Validate template dependencies
                    dep_issues = validate_template_dependencies(config, file_path)
                    if dep_issues:
                        for issue in dep_issues:
                            log_warning(f"  {issue}")
                            issues_found.append(f"{file_path}: {issue}")
                            
            except Exception as e:
                log_error(f"Error processing {file_path}: {e}")
                issues_found.append(f"{file_path}: Processing error - {e}")
        else:
            log_error(f"✗ {file_path}: {error}")
            issues_found.append(f"{file_path}: YAML syntax error - {error}")
    
    # Test setup scripts
    log_info("\nTesting setup scripts...")
    python_script_ok = test_python_setup_script()
    nodejs_script_ok = test_nodejs_setup_script()
    
    # Test configuration files
    log_info("\nTesting configuration files...")
    config_files_ok = test_configuration_files()
    
    # Summary
    log_info("\n" + "=" * 60)
    log_info("VALIDATION SUMMARY")
    log_info("=" * 60)
    
    log_info(f"YAML files validated: {valid_files}/{total_files}")
    
    if python_script_ok:
        log_success("✓ Python setup script")
    else:
        log_error("✗ Python setup script")
    
    if nodejs_script_ok:
        log_success("✓ Node.js setup script")
    else:
        log_error("✗ Node.js setup script")
    
    if config_files_ok:
        log_success("✓ Configuration files")
    else:
        log_error("✗ Configuration files")
    
    if issues_found:
        log_warning(f"\nIssues found ({len(issues_found)}):")
        for issue in issues_found:
            print(f"  - {issue}")
    
    # Overall result
    all_yaml_valid = valid_files == total_files
    all_scripts_ok = python_script_ok and nodejs_script_ok
    
    if all_yaml_valid and all_scripts_ok and config_files_ok and not issues_found:
        log_success("\n🎉 All validations passed! Pipeline configuration looks good.")
        return 0
    else:
        log_error("\n❌ Some validations failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        log_info("\nValidation interrupted by user")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)
