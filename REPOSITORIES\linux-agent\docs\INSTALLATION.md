# Installation Guide

This guide provides detailed installation instructions for the Linux Log Collection Agent across different Linux distributions and deployment scenarios.

## Prerequisites

### System Requirements
- **Operating System**: Linux with systemd support
  - Ubuntu 18.04+ / Debian 10+
  - CentOS 7+ / RHEL 7+ / Fedora 30+
  - SUSE Linux Enterprise 12+ / openSUSE 15+
- **Python**: Version 3.8 or higher
- **Memory**: Minimum 512MB RAM, 1GB+ recommended
- **Disk Space**: 100MB for installation, additional space for log storage
- **Network**: Internet access for package installation and API communication

### Required Permissions
- Root or sudo access for installation
- Read access to system log files (handled automatically)

## Quick Installation (Recommended)

### Automated Installation Script

The easiest way to install the agent is using the automated installation script:

```bash
# Download or clone the repository
git clone <repository-url>
cd linux-log-agent

# Run the installation script
sudo ./install.sh
```

The script will:
1. ✅ Detect your Linux distribution
2. ✅ Install required system packages and dependencies
3. ✅ Create the service user and groups
4. ✅ Set up directory structure with proper permissions
5. ✅ Install Python dependencies in a virtual environment
6. ✅ Configure and start the systemd service
7. ✅ Verify the installation

### Post-Installation Configuration

After installation, configure the agent for your environment:

```bash
# Edit the configuration file
sudo nano /etc/linux-log-agent/config.yaml

# Update the API endpoint and key
exlog_api:
  endpoint: "http://your-exlog-server:5000/api/v1/logs"
  api_key: "your-api-key-here"

# Restart the service to apply changes
sudo systemctl restart linux-log-agent

# Verify the service is running
sudo systemctl status linux-log-agent
```

## Manual Installation

### Step 1: Install System Dependencies

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install -y \
    python3 python3-pip python3-venv python3-dev \
    build-essential libsystemd-dev pkg-config \
    curl wget git
```

#### CentOS/RHEL 7
```bash
sudo yum install -y \
    python3 python3-pip python3-devel \
    gcc gcc-c++ make systemd-devel pkgconfig \
    curl wget git
```

#### CentOS/RHEL 8+ / Fedora
```bash
sudo dnf install -y \
    python3 python3-pip python3-devel \
    gcc gcc-c++ make systemd-devel pkgconfig \
    curl wget git
```

#### SUSE/openSUSE
```bash
sudo zypper install -y \
    python3 python3-pip python3-devel \
    gcc gcc-c++ make systemd-devel pkg-config \
    curl wget git
```

### Step 2: Create Service User and Groups

```bash
# Create the service user
sudo useradd -r -s /bin/false -d /opt/linux-log-agent linux-log-agent

# Add user to required groups for log access
sudo usermod -a -G adm,systemd-journal,syslog linux-log-agent
```

### Step 3: Set Up Directory Structure

```bash
# Create installation directory
sudo mkdir -p /opt/linux-log-agent
sudo mkdir -p /etc/linux-log-agent
sudo mkdir -p /var/log/linux-log-agent

# Set ownership
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /var/log/linux-log-agent
sudo chown root:linux-log-agent /etc/linux-log-agent
sudo chmod 750 /etc/linux-log-agent
```

### Step 4: Install Application Files

```bash
# Copy application files
sudo cp -r * /opt/linux-log-agent/
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent

# Copy configuration
sudo cp config/default_config.yaml /etc/linux-log-agent/config.yaml
sudo chown root:linux-log-agent /etc/linux-log-agent/config.yaml
sudo chmod 640 /etc/linux-log-agent/config.yaml
```

### Step 5: Install Python Dependencies

```bash
# Create virtual environment
cd /opt/linux-log-agent
sudo -u linux-log-agent python3 -m venv venv

# Install dependencies
sudo -u linux-log-agent ./venv/bin/pip install --upgrade pip
sudo -u linux-log-agent ./venv/bin/pip install -r requirements.txt
```

### Step 6: Install Systemd Service

```bash
# Copy service file
sudo cp install/systemd/linux-log-agent.service /etc/systemd/system/

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable linux-log-agent

# Start the service
sudo systemctl start linux-log-agent
```

### Step 7: Verify Installation

```bash
# Check service status
sudo systemctl status linux-log-agent

# Check if logs are being collected
sudo tail -f /var/log/linux-log-agent/standardized_logs.json

# Verify log categorization
sudo tail -n 10 /var/log/linux-log-agent/standardized_logs.json | jq -r '.source + " | " + .source_type'
```

## Distribution-Specific Notes

### Ubuntu 22.04+ (PEP 668)
Ubuntu 22.04+ enforces PEP 668 which restricts pip installations. The installation script handles this automatically by using virtual environments.

### CentOS 7
- Python 3.6 is the default, which meets minimum requirements
- May need to install `python3-pip` from EPEL repository:
  ```bash
  sudo yum install epel-release
  sudo yum install python3-pip
  ```

### RHEL 8+
- Use `dnf` instead of `yum`
- Enable PowerTools repository for development packages:
  ```bash
  sudo dnf config-manager --enable powertools
  ```

### Fedora
- All packages available in default repositories
- Use `dnf` package manager

## Troubleshooting Installation

### Common Issues

#### Permission Denied Errors
```bash
# Fix ownership issues
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /var/log/linux-log-agent

# Verify group membership
groups linux-log-agent
```

#### Python Dependency Issues
```bash
# Update pip and try again
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/pip install --upgrade pip
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/pip install -r requirements.txt
```

#### Service Won't Start
```bash
# Check detailed status
sudo systemctl status linux-log-agent -l

# Check logs
sudo journalctl -u linux-log-agent -n 50

# Verify configuration
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/python -c "import yaml; yaml.safe_load(open('/etc/linux-log-agent/config.yaml'))"
```

#### Missing System Packages
```bash
# Install build dependencies
sudo apt install build-essential python3-dev libsystemd-dev  # Ubuntu/Debian
sudo yum groupinstall "Development Tools"  # CentOS/RHEL 7
sudo dnf groupinstall "Development Tools"  # CentOS/RHEL 8+/Fedora
```

### Verification Commands

```bash
# Service health
sudo systemctl is-active linux-log-agent
sudo systemctl is-enabled linux-log-agent

# File permissions
ls -la /opt/linux-log-agent/
ls -la /var/log/linux-log-agent/
ls -la /etc/linux-log-agent/

# Process information
ps aux | grep linux-log-agent

# Network connectivity (if using API)
curl -I http://your-exlog-server:5000/api/v1/logs
```

## Uninstallation

To completely remove the agent:

```bash
# Stop and disable service
sudo systemctl stop linux-log-agent
sudo systemctl disable linux-log-agent

# Remove service file
sudo rm /etc/systemd/system/linux-log-agent.service
sudo systemctl daemon-reload

# Remove directories
sudo rm -rf /opt/linux-log-agent
sudo rm -rf /etc/linux-log-agent
sudo rm -rf /var/log/linux-log-agent

# Remove user
sudo userdel linux-log-agent
```

## Next Steps

After successful installation:

1. **Configure the agent**: Edit `/etc/linux-log-agent/config.yaml`
2. **Set up monitoring**: Review logs and service status
3. **Test functionality**: Generate test logs and verify collection
4. **Performance tuning**: Adjust settings based on your environment

See the [Configuration Guide](CONFIGURATION.md) for detailed configuration options.
