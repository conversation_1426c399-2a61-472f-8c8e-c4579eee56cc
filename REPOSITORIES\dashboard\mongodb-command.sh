docker exec dashboard-mongodb-1 mongosh exlog --eval 'db.users.updateOne({ email: "<EMAIL>" }, {$push: {apiKeys: {name: "Test API Key", key: "exlog_api_key_1234567890abcdef1234567890abcdef",createdAt: new Date(),lastUsed: null, isActive: true}}})'

docker exec dashboard-mongodb-1 mongosh exlog --eval "db.users.updateOne({email: '<EMAIL>'}, {\$set: {password: '\$2a\$12\$cr6szGDjXG7haljTZSxM7uMhahvHz/A6xqjYjSVWhKvEmsMYdws5G'}})"