# ExLog CI/CD Templates Documentation v2.0.0

Welcome to the documentation for ExLog CI/CD Templates v2.0.0!

This repository contains **simplified, robust** GitLab CI/CD templates for the ExLog project ecosystem. These templates prioritize reliability and ease of use over complexity, providing standardized validation and testing workflows that work consistently across all projects.

## 🚨 Major Update Notice

**v2.0.0 is a complete rewrite** focusing on reliability:
- ✅ **Simplified architecture**: 2-stage pipeline (validate → test)
- ✅ **Robust dependency handling**: Graceful error handling and platform compatibility
- ✅ **Enhanced reliability**: Pipelines that don't fail due to dependency issues
- ✅ **Easy maintenance**: Clear, understandable configurations

See [CHANGELOG.md](../CHANGELOG.md) for complete details.

## 📁 Repository Structure (v2.0.0)

```
cicd-templates/
├── templates/                    # Simplified, robust templates
│   ├── base.yml                 # Basic stages (validate, test) and job templates
│   ├── python.yml               # Python with dependency handling (50 lines)
│   ├── nodejs.yml               # Node.js with workspace support (53 lines)
│   ├── docker.yml               # Basic Docker job template (13 lines)
│   └── security-simple.yml      # Simple security validation (23 lines)
├── docs/                        # Documentation
│   └── README.md               # This file
├── examples/                    # Working project examples
│   ├── backend-example.yml     # Python backend configuration
│   ├── dashboard-example.yml   # Node.js workspace configuration
│   └── linux-agent-example.yml # Linux Python agent configuration
├── test_*.py                   # Validation and testing scripts
├── CHANGELOG.md                # Complete version history
├── MIGRATION_GUIDE.md          # v1.x to v2.0.0 upgrade guide
└── README.md                   # Main project documentation
```

## 📚 Documentation Index

### 🚀 Getting Started
- **[README.md](../README.md)** - Main project overview and quick start
- **[MIGRATION_GUIDE.md](../MIGRATION_GUIDE.md)** - Upgrading from v1.x to v2.0.0
- **[CHANGELOG.md](../CHANGELOG.md)** - Complete version history

### 🧪 Testing & Validation
- **[test_simple_pipeline.py](../test_simple_pipeline.py)** - YAML syntax validation
- **[test_pipeline_simulation.py](../test_pipeline_simulation.py)** - Pipeline execution simulation
- **[test_dependency_handling.py](../test_dependency_handling.py)** - Dependency handling validation

## 🚀 Quick Start (v2.0.0)

### Python Projects
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/python.yml'

variables:
  PYTHON_VERSION: "3.9"
  PROJECT_NAME: "my-python-project"

# Add project validation
validate_structure:
  stage: validate
  extends: .base_job
  image: python:3.9
  script:
    - echo "✓ Found main.py" && test -f main.py
    - echo "✓ Found requirements.txt" && test -f requirements.txt
```

### Node.js Projects
```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'

variables:
  NODE_VERSION: "18"
  PROJECT_NAME: "my-nodejs-project"

# Add project validation
validate_structure:
  stage: validate
  extends: .base_job
  image: node:18
  script:
    - echo "✓ Found package.json" && test -f package.json
```

### Platform-Specific Dependencies

For Python projects with platform-specific dependencies, create `requirements-ci.txt`:
```txt
# requirements-ci.txt - CI-compatible dependencies
PyYAML>=6.0
requests>=2.28.0
psutil>=5.9.0
# Exclude: pywin32 (Windows-only)
# Exclude: python-systemd>=234 (unavailable version)
```

## 📋 Pipeline Stages (v2.0.0)

Simplified 2-stage pipeline structure:

1. **Validate** - Project structure validation and dependency setup
2. **Test** - Basic testing with graceful fallbacks

```
┌─────────────┐  ┌─────────────┐
│   Validate  │─▶│    Test     │
└─────────────┘  └─────────────┘
      │                │
      ▼                ▼
  • Project        • Basic tests
  • Structure      • Syntax checks
  • Dependencies   • Fallback validation
```

**Future Enhancement Path:**
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Validate  │─▶│    Test     │─▶│   Quality   │─▶│    Build    │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
```

## 🎯 Key Features (v2.0.0)

### ✅ Reliability First
- **Graceful error handling**: Pipelines continue even with dependency failures
- **Platform compatibility**: Works on Windows and Linux projects
- **Robust dependency management**: Handles missing or incompatible dependencies
- **Clear logging**: Informative messages about what's happening

### ✅ Smart Dependency Handling
- **Python projects**: Supports `requirements-ci.txt` for CI-specific dependencies
- **Node.js projects**: Automatically handles npm workspaces
- **Platform filtering**: Excludes Windows-specific dependencies on Linux CI
- **Fallback strategies**: Multiple approaches to ensure something works

### ✅ Simplicity
- **2-stage pipeline**: validate → test
- **Minimal configuration**: Just include templates and set basic variables
- **Easy debugging**: Simple structure makes issues easy to identify
- **Incremental enhancement**: Add complexity gradually as needed

## 🐍 Python Template (v2.0.0)

### Current Features
- **Robust dependency installation**: Handles platform-specific dependencies gracefully
- **CI-specific requirements**: Supports `requirements-ci.txt` for Linux CI compatibility
- **Basic testing**: pytest execution with fallback to syntax checks
- **Error handling**: Continues pipeline even with missing dependencies

### Dependency Logic
```bash
1. Check for requirements-ci.txt → Use if exists
2. Else filter requirements.txt → Remove problematic deps
3. Install available dependencies → Continue on failures
4. Ensure pytest availability → For testing
```

## 🟢 Node.js Template (v2.0.0)

### Current Features
- **Workspace support**: Automatically detects and handles npm workspaces
- **Smart installation**: Uses `npm install` for workspaces, `npm ci` for regular projects
- **Fallback strategy**: Falls back from `npm ci` to `npm install` on lock file issues
- **Basic testing**: npm test execution with graceful failure handling

### Installation Logic
```bash
1. Check for workspace project → Use npm install
2. Else try npm ci → Fallback to npm install
3. Handle missing package.json → Continue gracefully
4. Run tests with --if-present → Avoid failures
```

## 🛠️ Troubleshooting

### Common Issues

**Pipeline fails with dependency errors**
- Create `requirements-ci.txt` for Python projects
- Check that Node.js projects use workspace-compatible installation

**Tests don't run**
- Templates provide fallback to syntax checks
- Add actual test commands to project-specific jobs

**Complex features missing**
- v2.0.0 focuses on reliability over features
- Add complexity incrementally using enhancement phases

### Getting Help

1. **Check logs**: v2.0.0 provides clear error messages
2. **Run test scripts**: Use provided validation tools
3. **Review examples**: Check working configurations
4. **Create issues**: Report problems in GitLab issues

## ⚙️ Configuration

### Environment Variables

Common variables you can set in your project:

```yaml
variables:
  # Coverage thresholds
  COVERAGE_THRESHOLD: "80"
  
  # Security scanning
  SECURITY_SCAN_ENABLED: "true"
  ZAP_TARGET_URL: "http://localhost:3000"
  
  # Python specific
  PYTHON_VERSION: "3.9"
  INSTALL_DEV_DEPS: "true"
  
  # Node.js specific
  NODE_VERSION: "18"
  NODE_ENV: "test"
  PACKAGE_MANAGER: "npm"  # or "yarn", "pnpm"
  
  # Docker specific
  DOCKER_BUILDKIT: 1
```

### Project-Specific Overrides

You can override or extend any template job:

```yaml
# Override the Python linting job
python_lint_flake8:
  extends: .python_base
  script:
    - echo "Custom linting script"
    - flake8 --config=custom-config.cfg .
```

## 📝 Examples

### Python Agent Project

```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/python.yml'
      - 'templates/security.yml'

variables:
  PROJECT_NAME: "logging-agent"
  PYTHON_VERSION: "3.9"
  COVERAGE_THRESHOLD: "75"

# Add custom validation
validate_agent_config:
  stage: validate
  extends: .python_base
  script:
    - python main.py validate-config
```

### Dashboard Project

```yaml
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file: 
      - 'templates/base.yml'
      - 'templates/nodejs.yml'
      - 'templates/docker.yml'
      - 'templates/security.yml'

variables:
  PROJECT_NAME: "dashboard"
  NODE_VERSION: "18"
  COVERAGE_THRESHOLD: "70"

# Custom E2E testing
test_e2e:
  stage: test
  extends: .nodejs_base
  script:
    - npm run test:e2e
```

## 🔧 Customization

### Adding New Tools

To add a new tool to the templates:

1. Add the tool installation to the appropriate setup script
2. Create a new job template in the relevant template file
3. Add configuration files to the `configs/` directory
4. Update documentation

### Creating Project-Specific Templates

For project-specific needs, you can:

1. Create a new template file in your project
2. Include both the centralized and project-specific templates
3. Override specific jobs as needed

## 🚀 Best Practices

### Template Usage
- Always pin template versions using `ref: main` or specific tags
- Override variables rather than entire jobs when possible
- Use `extends` to inherit from template jobs
- Keep project-specific logic in your project's `.gitlab-ci.yml`

### Code Quality
- Set appropriate coverage thresholds for your project
- Use the provided configuration files as starting points
- Customize rules based on your project's needs
- Enable all relevant security scans

### Performance
- Use caching effectively for dependencies
- Leverage parallel job execution
- Optimize Docker builds with multi-stage builds
- Use artifacts efficiently

## 🆘 Troubleshooting

### Common Issues

1. **Template not found**: Ensure the project path and file paths are correct
2. **Permission denied**: Check GitLab project permissions for template access
3. **Tool not found**: Verify tool installation in setup scripts
4. **Cache issues**: Clear cache or update cache keys

### Getting Help

- Check GitLab CI/CD logs for detailed error messages
- Review template job definitions for expected behavior
- Consult tool-specific documentation for configuration issues
- Create issues in the cicd-templates project for template bugs

## 📚 Additional Resources

- [GitLab CI/CD Documentation](https://docs.gitlab.com/ee/ci/)
- [GitLab CI/CD Templates](https://docs.gitlab.com/ee/ci/examples/)
- [Python Testing Best Practices](https://docs.python.org/3/library/unittest.html)
- [Node.js Testing Best Practices](https://nodejs.org/en/docs/guides/testing/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)

## 🤝 Contributing

To contribute to these templates:

1. Fork the cicd-templates repository
2. Create a feature branch
3. Make your changes
4. Test with example projects
5. Submit a merge request

## 📈 Enhancement Path (v2.0.0)

### Current: Foundation
- ✅ Basic validation and testing
- ✅ Robust dependency handling
- ✅ Platform compatibility

### Phase 1: Quality
- 🔄 Add linting and formatting
- 🔄 Enhanced test reporting
- 🔄 Code coverage

### Phase 2: Security
- 🔄 Basic security scanning
- 🔄 Dependency vulnerability checks
- 🔄 Secret detection

### Phase 3: Build & Deploy
- 🔄 Package building
- 🔄 Docker image creation
- 🔄 Deployment automation

## 🎉 Success Stories

The v2.0.0 templates have successfully resolved:
- ✅ Backend project: Fixed Windows dependency issues on Linux CI
- ✅ Linux agent: Fixed unavailable systemd version dependency
- ✅ Dashboard: Fixed npm workspace dependency sync issues
- ✅ All projects: Eliminated pipeline validation errors

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Need help?** Check the [MIGRATION_GUIDE.md](../MIGRATION_GUIDE.md) or create a GitLab issue!
