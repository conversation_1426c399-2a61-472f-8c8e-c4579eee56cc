{"name": "exlog-frontend", "version": "1.0.0", "description": "ExLog Frontend Dashboard Application", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@mui/material": "^5.14.19", "@mui/icons-material": "^5.14.19", "@mui/x-date-pickers": "^6.18.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "recharts": "^2.8.0", "axios": "^1.6.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-table": "^7.8.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-helmet-async": "^1.3.0", "notistack": "^3.0.1", "react-dropzone": "^14.2.3", "react-csv": "^2.2.2", "react-pdf": "^7.5.1", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^13.5.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.0", "cypress": "^13.6.0", "msw": "^2.0.8"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,json,css,md}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}