# Test script to verify the ExLog API is working

Write-Host "Testing ExLog API..." -ForegroundColor Green

# Test 1: Health check
Write-Host "`n1. Testing health endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method Get
    Write-Host "✅ Health check passed" -ForegroundColor Green
    Write-Host "Status: $($healthResponse.status)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Login to get token
Write-Host "`n2. Testing login..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/auth/login" -Method Post -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "✅ Login successful" -ForegroundColor Green
    Write-Host "Token received: $($token.Substring(0, 20))..." -ForegroundColor Cyan
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Fetch logs
Write-Host "`n3. Testing logs endpoint..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $logsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?limit=5" -Method Get -Headers $headers
    Write-Host "✅ Logs fetch successful" -ForegroundColor Green
    Write-Host "Total logs: $($logsResponse.data.pagination.totalCount)" -ForegroundColor Cyan
    Write-Host "Logs returned: $($logsResponse.data.logs.Count)" -ForegroundColor Cyan
    
    if ($logsResponse.data.logs.Count -gt 0) {
        Write-Host "`nSample log:" -ForegroundColor Cyan
        $sampleLog = $logsResponse.data.logs[0]
        Write-Host "  ID: $($sampleLog.logId)" -ForegroundColor White
        Write-Host "  Source: $($sampleLog.source)" -ForegroundColor White
        Write-Host "  Level: $($sampleLog.logLevel)" -ForegroundColor White
        Write-Host "  Host: $($sampleLog.host)" -ForegroundColor White
        Write-Host "  Message: $($sampleLog.message.Substring(0, [Math]::Min(50, $sampleLog.message.Length)))..." -ForegroundColor White
    }
} catch {
    Write-Host "❌ Logs fetch failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorDetails = $_.Exception.Response | ConvertFrom-Json
        Write-Host "Error details: $($errorDetails.message)" -ForegroundColor Red
    }
    exit 1
}

# Test 4: Search logs
Write-Host "`n4. Testing search endpoint..." -ForegroundColor Yellow
$searchData = @{
    query = "system"
    filters = @{
        source = "System"
    }
    limit = 5
} | ConvertTo-Json

try {
    $searchResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs/search" -Method Post -Body $searchData -Headers $headers
    Write-Host "✅ Search successful" -ForegroundColor Green
    Write-Host "Search results: $($searchResponse.data.logs.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Search failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API tests completed!" -ForegroundColor Green
Write-Host "You can now access the frontend at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Navigate to the Logs page to see the log management interface." -ForegroundColor Cyan
