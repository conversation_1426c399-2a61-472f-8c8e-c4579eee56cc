# ExLog - Cybersecurity Log Management Dashboard

ExLog is a comprehensive cybersecurity log management dashboard designed to collect, standardize, store, and analyze security logs from various sources across your infrastructure.

## Features

### Phase 1 (MVP) - Currently Implemented

- ✅ User authentication and authorization
- ✅ **Remember Me functionality** - Persistent login sessions with 30-day token expiration
- ✅ Log ingestion API for agents
- ✅ Basic log viewing and filtering
- ✅ **Functional dashboard** with real-time data and interactive charts
- ✅ Multi-database architecture (MongoDB, TimescaleDB, Elasticsearch, Redis)
- ✅ Containerized deployment with Docker
- ✅ Responsive web interface
- ✅ **Comprehensive Settings Management System**:
  - ✅ **Profile Management**: Update user information, secure password changes
  - ✅ **User Preferences**: Theme, language, timezone, notification settings
  - ✅ **API Key Management**: Generate, manage, and track API keys with permissions
  - ✅ **Security Settings**: Session management, login history, security audit trail
  - ✅ **System Configuration**: Log retention policies, email notifications (Admin only)

### Planned Features

- 🔄 Alert management and rules engine
- 🔄 Agent management and monitoring
- 🔄 Advanced search and analytics
- 🔄 Report generation
- 🔄 Real-time notifications
- 🔄 Compliance reporting
- 🔄 Advanced dashboards and visualizations

## Architecture

ExLog follows a simplified microservices architecture with the following components:

- **Frontend**: React.js application with Material-UI
- **Backend API**: Node.js/Express REST API
- **WebSocket Service**: Real-time communication
- **MongoDB**: Unified database for all data storage (logs, users, configurations, metadata)
- **Nginx**: Reverse proxy and load balancer

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for development)
- Git

### Using Docker (Recommended)

1. Clone the repository:

```bash
git clone <repository-url>
cd exlog-dashboard
```

2. Copy environment configuration:

```bash
cp .env.example .env
```

3. Start the application:

```bash
docker-compose up -d
```

4. Access the application:

- Dashboard: http://localhost:8080 or http://[your-ip]:8080
- API: http://localhost:8080/api/v1 or http://[your-ip]:8080/api/v1
- Direct Frontend: http://localhost:3000 or http://[your-ip]:3000
- Direct Backend: http://localhost:5000 or http://[your-ip]:5000
- Default login: <EMAIL> / Admin123!

**Network Access:** The application is configured to accept connections from any device on your local network. Find your computer's IP address and access the application from other devices using `http://[your-ip]:8080`.

### Development Setup

1. Install dependencies:

```bash
npm run setup
```

2. Start development servers:

```bash
npm run dev
```

3. Access the application:

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## API Documentation

### Authentication

```bash
# Login
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "Admin123!",
  "rememberMe": true  // Optional: extends token to 30 days
}

# Validate existing token
POST /api/v1/auth/validate
Authorization: Bearer <token>

# Get current user
GET /api/v1/auth/me
Authorization: Bearer <token>
```

### Log Ingestion

```bash
# Ingest logs (requires API key)
POST /api/v1/logs
X-API-Key: <api-key>
{
  "logs": [
    {
      "logId": "unique-log-id",
      "timestamp": "2024-01-01T12:00:00Z",
      "source": "System",
      "sourceType": "event",
      "host": "server-01",
      "logLevel": "info",
      "message": "System started successfully"
    }
  ]
}
```

### Log Retrieval

```bash
# Get logs with filtering
GET /api/v1/logs?page=1&limit=100&source=System&logLevel=error
Authorization: Bearer <token>

# Search logs
POST /api/v1/logs/search
Authorization: Bearer <token>
{
  "query": "error",
  "filters": {
    "source": "System",
    "logLevel": "error"
  },
  "timeRange": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-02T00:00:00Z"
  }
}
```

### Settings Management

```bash
# Get user profile
GET /api/v1/settings/profile
Authorization: Bearer <token>

# Update user profile
PUT /api/v1/settings/profile
Authorization: Bearer <token>
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>"
}

# Update user preferences
PUT /api/v1/settings/preferences
Authorization: Bearer <token>
{
  "theme": "dark",
  "language": "en",
  "timezone": "America/New_York",
  "notifications": {
    "email": true,
    "alerts": {
      "critical": true,
      "high": true
    }
  }
}

# Change password
PUT /api/v1/settings/password
Authorization: Bearer <token>
{
  "currentPassword": "oldPassword",
  "newPassword": "newPassword123!",
  "confirmPassword": "newPassword123!"
}

# Get API keys
GET /api/v1/settings/api-keys
Authorization: Bearer <token>

# Create API key
POST /api/v1/settings/api-keys
Authorization: Bearer <token>
{
  "name": "My API Key",
  "description": "For log ingestion",
  "permissions": ["view_logs", "search_logs"],
  "expiresAt": "2024-12-31"
}

# Get active sessions
GET /api/v1/settings/sessions
Authorization: Bearer <token>

# Get login history
GET /api/v1/settings/login-history?page=1&limit=20
Authorization: Bearer <token>

# System settings (Admin only)
GET /api/v1/settings/system
Authorization: Bearer <token>

# Update log retention settings (Admin only)
PUT /api/v1/settings/system/log-retention
Authorization: Bearer <token>
{
  "defaultRetentionDays": 90,
  "autoArchiveEnabled": true
}
```

## Configuration

### Environment Variables

See `.env.example` for all available configuration options.

### User Roles and Permissions

- **Admin**: Full system access
- **Security Analyst**: Log analysis and alert management
- **Compliance Officer**: Read-only access to logs and reports
- **Executive**: Dashboard and report access only

## Development

### Project Structure

```
exlog-dashboard/
├── backend/                 # Node.js API server
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   └── Dockerfile
├── frontend/               # React.js application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── store/          # Redux store
│   │   └── styles/         # Styling
│   └── Dockerfile
├── scripts/                # Database initialization
├── nginx/                  # Nginx configuration
└── docker-compose.yml     # Container orchestration
```

### Running Tests

```bash
# Backend tests
npm run test:backend

# Frontend tests
npm run test:frontend

# All tests
npm test
```

### Building for Production

```bash
# Build all components
npm run build

# Build with Docker
docker-compose -f docker-compose.prod.yml build
```

## Security Considerations

- Change default passwords immediately
- Use strong JWT secrets in production
- Enable HTTPS with proper SSL certificates
- Configure proper firewall rules
- Regular security updates
- Monitor access logs

### Network Security

**Development Environment:**

- CORS is configured to allow local network access
- All private network ranges (192.168.x.x, 10.x.x.x, 172.16-31.x.x) are automatically allowed

**Production Environment:**

- **Important:** Update `CORS_ORIGIN` environment variable to specify allowed origins
- Example: `CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com`
- Consider implementing additional security measures for network access
- Review and configure firewall rules appropriately

For detailed networking configuration, see [NETWORKING_CONFIGURATION.md](NETWORKING_CONFIGURATION.md).

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue in the repository
- Check the documentation
- Review the API documentation

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for detailed changes and version history.
