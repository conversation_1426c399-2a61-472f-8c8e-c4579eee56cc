```mermaid
graph TB
    %% External Systems and Users
    subgraph "External Environment"
        WinSys[Windows Systems<br/>Event Sources]
        Users[Security Analysts<br/>Administrators]
        Browser[Web Browser]
    end

    %% Backend Python Agent Project
    subgraph "REPOSITORIES/backend - Python Logging Agent"
        subgraph "Log Collectors"
            EventCol[Event Log Collector<br/>Windows Events]
            SecCol[Security Log Collector<br/>Auth Events]
            AppCol[Application Log Collector<br/>App Events]
            SysCol[System Log Collector<br/>System Events]
            NetCol[Network Log Collector<br/>Network Events]
            PacketCol[Packet Collector<br/>Network Capture]
        end
        
        subgraph "Core Agent Components"
            Agent[Logging Agent<br/>Main Controller]
            Standardizer[Log Standardizer<br/>JSON Formatter]
            Buffer[Timed Buffer<br/>Batch Processing]
            ConfigMgr[Config Manager<br/>YAML Config]
        end
        
        subgraph "Output & Communication"
            APIClient[ExLog API Client<br/>HTTP/REST]
            FileOut[File Output<br/>Local Logs]
            ConsoleOut[Console Output<br/>Debug]
        end
        
        subgraph "Service Management"
            WinService[Windows Service<br/>Background Process]
            ServiceRunner[Service Runner<br/>Process Manager]
        end
        
        subgraph "Utilities"
            Logger[Logger Setup<br/>Audit & Performance]
            UUIDGen[UUID Generator<br/>Log IDs]
        end
    end

    %% Dashboard Project
    subgraph "REPOSITORIES/dashboard - Web Dashboard"
        subgraph "Frontend (React)"
            ReactApp[React Application<br/>Material-UI]
            
            subgraph "Pages & Components"
                LoginPage[Login Page<br/>Authentication]
                Dashboard[Dashboard Page<br/>Overview Stats]
                LogsPage[Logs Page<br/>Log Viewer]
                AlertsPage[Alerts Page<br/>Alert Management]
                AgentsPage[Agents Page<br/>Agent Monitoring]
                UsersPage[Users Page<br/>User Management]
                ReportsPage[Reports Page<br/>Report Generation]
            end
            
            subgraph "State Management"
                Redux[Redux Store<br/>State Management]
                AuthSlice[Auth Slice<br/>User Sessions]
                LogsSlice[Logs Slice<br/>Log Data]
                AlertsSlice[Alerts Slice<br/>Alert Data]
            end
            
            subgraph "Services"
                APIService[API Service<br/>Axios HTTP Client]
            end
        end
        
        subgraph "Backend API (Node.js/Express)"
            ExpressApp[Express.js Server<br/>REST API]
            
            subgraph "API Routes"
                AuthRoutes[Auth Routes<br/>/api/v1/auth]
                LogRoutes[Log Routes<br/>/api/v1/logs]
                UserRoutes[User Routes<br/>/api/v1/users]
                AlertRoutes[Alert Routes<br/>/api/v1/alerts]
                AgentRoutes[Agent Routes<br/>/api/v1/agents]
                ReportRoutes[Report Routes<br/>/api/v1/reports]
            end
            
            subgraph "Middleware"
                AuthMiddleware[JWT Authentication<br/>Token Validation]
                AuthzMiddleware[Authorization<br/>Role-Based Access]
                ErrorHandler[Error Handler<br/>Exception Management]
                Validation[Request Validation<br/>Input Sanitization]
            end
            
            subgraph "Database Models"
                UserModel[User Model<br/>MongoDB Schema]
                LogModel[Log Model<br/>MongoDB Schema]
                AlertModel[Alert Model<br/>MongoDB Schema]
                AgentModel[Agent Model<br/>MongoDB Schema]
            end
        end
        
        subgraph "WebSocket Service"
            WSServer[WebSocket Server<br/>Real-time Updates]
        end
        
        subgraph "Database Layer"
            MongoDB[(MongoDB<br/>Primary Database<br/>Users, Logs, Config)]
            TimescaleDB[(TimescaleDB<br/>Time-series Data<br/>Log Metrics)]
            Elasticsearch[(Elasticsearch<br/>Search Engine<br/>Log Indexing)]
            Redis[(Redis<br/>Cache & Sessions<br/>Real-time Data)]
        end
        
        subgraph "Infrastructure"
            Nginx[Nginx<br/>Reverse Proxy<br/>Load Balancer]
            Docker[Docker Containers<br/>Containerization]
        end
    end

    %% Data Flow Connections
    WinSys --> EventCol
    WinSys --> SecCol
    WinSys --> AppCol
    WinSys --> SysCol
    WinSys --> NetCol
    WinSys --> PacketCol
    
    EventCol --> Agent
    SecCol --> Agent
    AppCol --> Agent
    SysCol --> Agent
    NetCol --> Agent
    PacketCol --> Agent
    
    Agent --> Standardizer
    Standardizer --> Buffer
    Buffer --> APIClient
    APIClient --> FileOut
    APIClient --> ConsoleOut
    
    ConfigMgr --> Agent
    Logger --> Agent
    UUIDGen --> Standardizer
    WinService --> Agent
    ServiceRunner --> WinService
    
    %% API Communication
    APIClient -->|HTTP POST<br/>/api/v1/logs| LogRoutes
    
    %% Frontend to Backend
    Browser --> Nginx
    Nginx --> ReactApp
    Nginx --> ExpressApp
    Nginx --> WSServer
    
    Users --> Browser
    ReactApp --> APIService
    APIService -->|REST API Calls| ExpressApp
    
    %% Redux State Flow
    ReactApp --> Redux
    Redux --> AuthSlice
    Redux --> LogsSlice
    Redux --> AlertsSlice
    
    %% Page Navigation
    ReactApp --> LoginPage
    ReactApp --> Dashboard
    ReactApp --> LogsPage
    ReactApp --> AlertsPage
    ReactApp --> AgentsPage
    ReactApp --> UsersPage
    ReactApp --> ReportsPage
    
    %% API Route Processing
    ExpressApp --> AuthRoutes
    ExpressApp --> LogRoutes
    ExpressApp --> UserRoutes
    ExpressApp --> AlertRoutes
    ExpressApp --> AgentRoutes
    ExpressApp --> ReportRoutes
    
    %% Middleware Chain
    ExpressApp --> AuthMiddleware
    AuthMiddleware --> AuthzMiddleware
    AuthzMiddleware --> Validation
    Validation --> ErrorHandler
    
    %% Database Connections
    LogRoutes --> LogModel
    UserRoutes --> UserModel
    AlertRoutes --> AlertModel
    AgentRoutes --> AgentModel
    
    LogModel --> MongoDB
    UserModel --> MongoDB
    AlertModel --> MongoDB
    AgentModel --> MongoDB
    
    LogRoutes --> TimescaleDB
    LogRoutes --> Elasticsearch
    ExpressApp --> Redis
    WSServer --> Redis
    
    %% Docker Containerization
    ReactApp -.-> Docker
    ExpressApp -.-> Docker
    WSServer -.-> Docker
    MongoDB -.-> Docker
    TimescaleDB -.-> Docker
    Elasticsearch -.-> Docker
    Redis -.-> Docker
    Nginx -.-> Docker

    %% Styling
    classDef agentClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backendClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef databaseClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef infraClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef externalClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class EventCol,SecCol,AppCol,SysCol,NetCol,PacketCol,Agent,Standardizer,Buffer,APIClient,ConfigMgr,WinService,ServiceRunner,Logger,UUIDGen,FileOut,ConsoleOut agentClass
    class ReactApp,LoginPage,Dashboard,LogsPage,AlertsPage,AgentsPage,UsersPage,ReportsPage,Redux,AuthSlice,LogsSlice,AlertsSlice,APIService frontendClass
    class ExpressApp,AuthRoutes,LogRoutes,UserRoutes,AlertRoutes,AgentRoutes,ReportRoutes,AuthMiddleware,AuthzMiddleware,ErrorHandler,Validation,UserModel,LogModel,AlertModel,AgentModel,WSServer backendClass
    class MongoDB,TimescaleDB,Elasticsearch,Redis databaseClass
    class Nginx,Docker infraClass
    class WinSys,Users,Browser externalClass
```