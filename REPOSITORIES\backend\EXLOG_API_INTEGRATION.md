# ExLog Dashboard API Integration

This document describes how to configure and use the ExLog Dashboard API integration in the Python Logging Agent.

## Overview

The ExLog API integration allows the logging agent to send collected logs directly to your ExLog dashboard in real-time. The integration includes:

- **Batch Processing**: Logs are sent in configurable batches for optimal performance
- **Retry Logic**: Failed requests are automatically retried with exponential backoff
- **Offline Buffering**: Logs are buffered locally when the API is unavailable
- **Log Validation**: Automatic validation and fixing of logs missing required fields
- **Error Handling**: Comprehensive error handling and logging

## Quick Start

### 1. Install Dependencies

```bash
pip install aiohttp requests
```

### 2. Configure API Settings

Run the configuration helper:

```bash
python configure_api.py
```

Or manually edit `config/default_config.yaml`:

```yaml
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: "your-api-key-here"
  batch_size: 100
  timeout: 30
```

### 3. Test the Integration

```bash
python test_api_client.py
```

### 4. Start the Agent

```bash
python main.py console
```

## Configuration Reference

### Basic Settings

```yaml
exlog_api:
  enabled: true                                    # Enable/disable API integration
  endpoint: "http://localhost:5000/api/v1/logs"   # ExLog API endpoint
  api_key: "your-api-key-here"                    # API authentication key
```

### Batch Processing

```yaml
exlog_api:
  batch_size: 100                    # Number of logs per batch (recommended: 100)
  max_batch_wait_time: 30           # Max seconds to wait before sending partial batch
```

### Connection Settings

```yaml
exlog_api:
  timeout: 30                       # Request timeout in seconds
  max_retries: 3                    # Maximum retry attempts for failed requests
  retry_delay: 5                    # Initial delay between retries (exponential backoff)
  connection_pool_size: 10          # HTTP connection pool size
```

### Offline Buffer

```yaml
exlog_api:
  offline_buffer:
    enabled: true                   # Enable offline buffering
    max_size: 10000                # Maximum logs to buffer when API unavailable
    buffer_file: "logs/api_buffer.json"  # File to store buffered logs
    retry_interval: 60             # Seconds between retry attempts for buffered logs
```

### Log Validation

```yaml
exlog_api:
  validation:
    fix_missing_fields: true       # Automatically fix logs missing required fields
    default_source: "System"       # Default source for logs missing this field
    default_source_type: "event"   # Default source_type for logs missing this field
    default_log_level: "info"      # Default log_level for logs missing this field
```

## API Schema

The ExLog API expects logs in the following format:

```json
{
  "logs": [
    {
      "log_id": "unique-identifier",
      "timestamp": "2024-01-01T12:00:00Z",
      "source": "System|Application|Security|Network|Custom",
      "source_type": "event|application|security|network|audit|performance",
      "host": "hostname-or-ip",
      "log_level": "critical|error|warning|info|debug",
      "message": "Log message text",
      "raw_data": null,
      "additional_fields": {
        "event_id": 1234,
        "category": "example"
      }
    }
  ]
}
```

### Required Fields

- `log_id`: Unique identifier for the log entry
- `timestamp`: ISO 8601 formatted timestamp
- `source`: Log source category
- `source_type`: Type of log source
- `host`: Hostname or IP address
- `log_level`: Severity level
- `message`: Log message content

### Optional Fields

- `raw_data`: Original raw log data (can be null)
- `additional_fields`: Object containing additional metadata

## Authentication

The API uses header-based authentication:

```
X-API-Key: your-api-key-here
Content-Type: application/json
```

## Error Handling

### Retry Logic

- **Automatic Retries**: Failed requests are automatically retried up to `max_retries` times
- **Exponential Backoff**: Retry delays increase exponentially (5s, 10s, 20s, etc.)
- **Status Code Handling**:
  - `200/201`: Success
  - `400`: Bad request (no retry)
  - `5xx`: Server error (retry)

### Offline Buffering

When the API is unavailable:

1. Logs are stored in a local buffer file
2. Background process periodically retries sending buffered logs
3. Buffer size is limited to prevent disk space issues
4. Oldest logs are removed when buffer is full

### Log Validation

Missing required fields are automatically fixed:

- `log_id`: Generated UUID if missing
- `timestamp`: Current timestamp if missing
- `source`: Default value from configuration
- `source_type`: Default value from configuration
- `host`: System hostname if missing
- `log_level`: Default value from configuration
- `message`: "No message provided" if missing

## Monitoring

### Agent Status

Check API client status:

```bash
python main.py status
```

### Statistics

The agent tracks:

- Logs sent successfully
- Logs failed to send
- Batches sent/failed
- API errors
- Queue size
- Offline buffer size
- Last successful send time
- Last error message

### Log Files

API client activity is logged to:

- `logs/agent.log`: General agent logs including API operations
- `logs/agent_errors.log`: Error logs
- `logs/api_buffer.json`: Offline buffer storage

## Troubleshooting

### Common Issues

1. **"API client not available"**
   - Install dependencies: `pip install aiohttp requests`

2. **"Connection refused"**
   - Check if ExLog dashboard is running
   - Verify endpoint URL in configuration

3. **"Authentication failed"**
   - Verify API key is correct
   - Check API key format and permissions

4. **"Logs not appearing in dashboard"**
   - Check agent logs for errors
   - Verify log format matches API schema
   - Test with `python test_api_client.py`

### Debug Mode

Enable debug logging in `config/default_config.yaml`:

```yaml
general:
  log_level: "DEBUG"
```

### Testing

Test the API integration:

```bash
# Test API client functionality
python test_api_client.py

# Show current configuration
python configure_api.py show

# Test agent in console mode
python main.py console
```

## Performance Tuning

### Batch Size

- **Small batches (10-50)**: Lower latency, higher overhead
- **Large batches (100-500)**: Higher throughput, higher latency
- **Recommended**: 100 logs per batch

### Connection Pool

Increase `connection_pool_size` for high-volume environments:

```yaml
exlog_api:
  connection_pool_size: 20  # Default: 10
```

### Buffer Settings

For high-volume environments:

```yaml
exlog_api:
  offline_buffer:
    max_size: 50000         # Increase buffer size
    retry_interval: 30      # Decrease retry interval
```

## Security Considerations

1. **API Key Storage**: Store API keys securely, avoid committing to version control
2. **Network Security**: Use HTTPS endpoints in production
3. **Access Control**: Limit API key permissions to log writing only
4. **Log Content**: Be mindful of sensitive data in log messages

## Support

For issues with the ExLog API integration:

1. Check the troubleshooting section above
2. Review agent logs for error messages
3. Test with the provided test script
4. Verify configuration settings
