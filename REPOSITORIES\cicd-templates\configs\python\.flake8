[flake8]
# Base flake8 configuration for Python projects

# Maximum line length
max-line-length = 88

# Maximum complexity
max-complexity = 10

# Ignore specific error codes
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501

# Exclude directories and files
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    build,
    dist,
    *.egg-info,
    .tox,
    .coverage,
    .pytest_cache,
    node_modules,
    migrations,
    .mypy_cache

# File patterns to include
filename = *.py

# Enable specific checks
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity

# Per-file ignores
per-file-ignores =
    # Tests can have longer lines and more complex functions
    tests/*:E501,C901
    # __init__.py files can have unused imports
    __init__.py:F401
    # Settings files can have longer lines
    settings.py:E501
    # Configuration files can have longer lines
    config.py:E501,
    config/*.py:E501

# Enable specific plugins (if installed)
# Uncomment as needed:
# enable-extensions = 
#     # flake8-docstrings
#     D,
#     # flake8-import-order
#     I,
#     # flake8-bugbear
#     B,
#     # flake8-comprehensions
#     C4

# Docstring conventions (if using flake8-docstrings)
# docstring-convention = google

# Import order settings (if using flake8-import-order)
# import-order-style = google
# application-import-names = your_app_name

# Show source code for each error
show-source = True

# Show pep8 error codes
show-pep8 = True

# Count errors and warnings
count = True

# Print total number of errors and warnings to stderr
statistics = True

# Format for error messages
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s
