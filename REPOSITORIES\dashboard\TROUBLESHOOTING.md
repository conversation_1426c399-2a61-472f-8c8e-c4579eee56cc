# Log Management Troubleshooting Guide

## Common Issues and Solutions

### 1. **Date Picker Not Working**

**Issue**: Date picker components not rendering or throwing errors

**Solution**:

```bash
# Install the required dependency
docker exec dashboard-frontend-1 npm install @mui/x-date-pickers

# Or rebuild the frontend container
docker-compose build frontend
docker-compose up -d frontend
```

### 2. **No Logs Displayed**

**Issue**: Log management page shows "No logs found"

**Diagnosis**:

```bash
# Check if logs exist in database
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.logs.countDocuments()"

# Check API response
powershell -ExecutionPolicy Bypass -File test-api.ps1
```

**Solutions**:

- If no logs in database, run the sample data insertion script
- If API fails, check backend container logs
- Verify authentication is working

### 3. **Authentication Issues**

**Issue**: "Unauthorized" or "Invalid token" errors

**Solutions**:

```bash
# Check if admin user exists
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.users.findOne({email: '<EMAIL>'})"

# Reset admin password if needed
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.users.updateOne({email: '<EMAIL>'}, {\$set: {password: '\$2a\$12\$cr6szGDjXG7haljTZSxM7uMhahvHz/A6xqjYjSVWhKvEmsMYdws5G'}})"
```

### 4. **Container Issues**

**Issue**: Containers not running or unhealthy

**Diagnosis**:

```bash
# Check container status
docker ps

# Check container logs
docker logs dashboard-backend-1
docker logs dashboard-frontend-1
docker logs dashboard-mongodb-1
```

**Solutions**:

```bash
# Restart containers
docker-compose restart

# Rebuild if needed
docker-compose down
docker-compose build
docker-compose up -d
```

### 5. **Frontend Build Issues**

**Issue**: Frontend not loading or showing errors

**Solutions**:

```bash
# Check frontend logs
docker logs dashboard-frontend-1

# Rebuild frontend
docker-compose build frontend
docker-compose up -d frontend

# Clear browser cache and reload
```

### 6. **API Connection Issues**

**Issue**: Frontend cannot connect to backend API

**Diagnosis**:

- Check if backend is running on port 5000
- Verify API_URL environment variable
- Test API directly with PowerShell script

**Solutions**:

```bash
# Check backend health
curl http://localhost:5000/health

# Verify environment variables
docker exec dashboard-frontend-1 env | grep REACT_APP

# Restart backend
docker-compose restart backend
```

## Quick Fixes

### Add Sample Data

```bash
# Add sample logs to database
docker exec dashboard-mongodb-1 mongosh exlog --eval "
db.logs.insertMany([
  {
    logId: 'sample_log_001',
    timestamp: new Date(),
    source: 'System',
    sourceType: 'event',
    host: 'server-01',
    logLevel: 'info',
    message: 'System startup completed successfully',
    additionalFields: { processId: '1234' },
    metadata: { agentId: 'agent-001' },
    tags: ['startup', 'system'],
    severity: 2
  }
])
"
```

### Reset Admin User

```bash
# Create/update admin user
docker exec dashboard-mongodb-1 mongosh exlog --eval "
db.users.updateOne(
  {email: '<EMAIL>'},
  {
    \$set: {
      username: 'admin',
      password: '\$2a\$12\$cr6szGDjXG7haljTZSxM7uMhahvHz/A6xqjYjSVWhKvEmsMYdws5G',
      role: 'admin',
      status: 'active'
    }
  },
  {upsert: true}
)
"
```

### Install Missing Dependencies

```bash
# Install date picker dependency
docker exec dashboard-frontend-1 npm install @mui/x-date-pickers

# Or add to package.json and rebuild
```

## Verification Steps

### 1. **Check All Services**

```bash
# Verify all containers are running
docker ps | grep dashboard

# Should show:
# - dashboard-frontend-1 (port 3000)
# - dashboard-backend-1 (port 5000)
# - dashboard-mongodb-1 (port 27017)
# - dashboard-websocket-1 (port 5001)
# - dashboard-nginx-1 (ports 80, 443)
```

### 2. **Test API Endpoints**

```bash
# Run the test script
powershell -ExecutionPolicy Bypass -File test-api.ps1
```

### 3. **Verify Database**

```bash
# Check logs count
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.logs.countDocuments()"

# Check users
docker exec dashboard-mongodb-1 mongosh exlog --eval "db.users.countDocuments()"
```

### 4. **Test Frontend**

- Navigate to http://localhost:3000
- <NAME_EMAIL> / Admin123!
- Go to Logs page
- Verify logs are displayed
- Test filtering and search
- Test log detail modal

## Performance Optimization

### Database Indexing

```bash
# Ensure proper indexes exist
docker exec dashboard-mongodb-1 mongosh exlog --eval "
db.logs.createIndex({timestamp: -1});
db.logs.createIndex({source: 1});
db.logs.createIndex({logLevel: 1});
db.logs.createIndex({host: 1});
db.logs.createIndex({message: 'text'});
"
```

### Memory and CPU

- Monitor container resource usage
- Adjust Docker memory limits if needed
- Consider pagination limits for large datasets

## Support

If issues persist:

1. Check container logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure all required ports are available
4. Check firewall and network settings
5. Review the LOG_MANAGEMENT_UPDATE.md for implementation details

## Useful Commands

```bash
# Complete restart
docker-compose down && docker-compose up -d

# View logs
docker-compose logs -f

# Execute commands in containers
docker exec -it dashboard-backend-1 bash
docker exec -it dashboard-frontend-1 bash
docker exec -it dashboard-mongodb-1 mongosh exlog

# Check resource usage
docker stats
```
