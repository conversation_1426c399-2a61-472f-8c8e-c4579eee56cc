# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the Linux Log Collection Agent.

## Quick Diagnostics

### Health Check Commands

Run these commands to quickly assess the agent's status:

```bash
# Service status
sudo systemctl status linux-log-agent

# Recent logs
sudo journalctl -u linux-log-agent -n 20

# Check if logs are being collected
sudo tail -n 5 /var/log/linux-log-agent/standardized_logs.json

# Resource usage
sudo systemctl show linux-log-agent --property=MemoryCurrent,CPUUsageNSec

# Configuration validation
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/python -c "import yaml; print('Config OK') if yaml.safe_load(open('/etc/linux-log-agent/config.yaml')) else print('Config Error')"
```

## Common Issues and Solutions

### 🚫 Service Won't Start

#### Symptoms
- `sudo systemctl start linux-log-agent` fails
- Service shows "failed" or "inactive" status
- Error messages in systemd logs

#### Diagnostic Steps
```bash
# Check detailed service status
sudo systemctl status linux-log-agent -l

# View recent service logs
sudo journalctl -u linux-log-agent -n 50

# Check for configuration errors
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/python main.py console --config /etc/linux-log-agent/config.yaml
```

#### Common Causes and Solutions

**1. Permission Issues**
```bash
# Fix file ownership
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /var/log/linux-log-agent

# Verify group membership
sudo usermod -a -G adm,systemd-journal,syslog linux-log-agent

# Check group membership
groups linux-log-agent
```

**2. Missing Dependencies**
```bash
# Reinstall Python dependencies
cd /opt/linux-log-agent
sudo -u linux-log-agent ./venv/bin/pip install --upgrade pip
sudo -u linux-log-agent ./venv/bin/pip install -r requirements.txt
```

**3. Configuration Errors**
```bash
# Validate YAML syntax
python3 -c "import yaml; yaml.safe_load(open('/etc/linux-log-agent/config.yaml'))"

# Check for required fields
grep -E "(endpoint|api_key)" /etc/linux-log-agent/config.yaml
```

**4. Virtual Environment Issues**
```bash
# Recreate virtual environment
sudo rm -rf /opt/linux-log-agent/venv
cd /opt/linux-log-agent
sudo -u linux-log-agent python3 -m venv venv
sudo -u linux-log-agent ./venv/bin/pip install -r requirements.txt
```

### 🔌 API Connection Issues

#### Symptoms
- Logs in `/var/log/linux-log-agent/errors.log` about API failures
- "Failed to send batch" error messages
- Logs accumulating locally but not reaching dashboard

#### Diagnostic Steps
```bash
# Test API connectivity
curl -I http://your-exlog-server:5000/api/v1/logs

# Test with authentication
curl -H "Authorization: Bearer YOUR_API_KEY" -I http://your-exlog-server:5000/api/v1/logs

# Check network connectivity
ping your-exlog-server
telnet your-exlog-server 5000

# Review API errors
sudo tail -f /var/log/linux-log-agent/errors.log | grep -i api
```

#### Solutions

**1. Network Connectivity**
```bash
# Check firewall rules
sudo iptables -L | grep 5000
sudo ufw status | grep 5000

# Test DNS resolution
nslookup your-exlog-server
```

**2. API Configuration**
```bash
# Verify API settings in config
grep -A 10 "exlog_api:" /etc/linux-log-agent/config.yaml

# Update configuration
sudo nano /etc/linux-log-agent/config.yaml
sudo systemctl restart linux-log-agent
```

**3. Authentication Issues**
```bash
# Test API key
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{"test": "data"}' \
  http://your-exlog-server:5000/api/v1/logs
```

### 📊 No Logs Being Collected

#### Symptoms
- Empty or no updates to `/var/log/linux-log-agent/standardized_logs.json`
- Service running but no log activity
- Missing log categories

#### Diagnostic Steps
```bash
# Check collector status
sudo grep -i "collector" /var/log/linux-log-agent/agent.log

# Verify log file access
sudo -u linux-log-agent ls -la /var/log/auth.log /var/log/syslog

# Test journalctl access
sudo -u linux-log-agent journalctl -n 5 --output=json

# Generate test logs
logger -t test-app "Test log message"
sudo logger -t sudo "Test sudo message"
```

#### Solutions

**1. Permission Issues**
```bash
# Add user to log groups
sudo usermod -a -G adm,systemd-journal,syslog linux-log-agent

# Verify access to log files
sudo -u linux-log-agent cat /var/log/auth.log | tail -n 5
```

**2. Configuration Issues**
```bash
# Check enabled collectors
grep -A 5 "enabled: true" /etc/linux-log-agent/config.yaml

# Enable journalctl collector
sudo sed -i '/journalctl:/,/enabled:/ s/enabled: false/enabled: true/' /etc/linux-log-agent/config.yaml
sudo systemctl restart linux-log-agent
```

**3. Log File Locations**
```bash
# Find actual log file locations
find /var/log -name "*.log" -type f 2>/dev/null | head -10

# Update configuration with correct paths
sudo nano /etc/linux-log-agent/config.yaml
```

### 🏷️ Incorrect Log Categorization

#### Symptoms
- All logs showing as "System" or "Systemd"
- Missing specific categories like "Auth" or "Kernel"
- Poor categorization in dashboard

#### Diagnostic Steps
```bash
# Check recent log categories
sudo tail -n 20 /var/log/linux-log-agent/standardized_logs.json | jq -r '.source + " | " + .source_type'

# Verify categorization logic
sudo grep -A 20 "_categorize_journal_entry" /opt/linux-log-agent/logging_agent/collectors/journalctl_collector.py
```

#### Solutions

**1. Update to Latest Version**
```bash
# Ensure you have the latest categorization improvements
sudo systemctl stop linux-log-agent
# Update files from repository
sudo systemctl start linux-log-agent
```

**2. Generate Test Logs**
```bash
# Test different log types
logger -t apache2 "Test web server log"
logger -t sshd "Test SSH authentication"
logger -t kernel "Test kernel message"
sudo logger "Test sudo authentication"
```

### 🔄 Service Shutdown Issues

#### Symptoms
- `systemctl stop` hangs or times out
- Service requires `SIGKILL` to stop
- "Failed to stop" error messages

#### Diagnostic Steps
```bash
# Check service stop timeout
sudo systemctl show linux-log-agent | grep TimeoutStopUSec

# Monitor shutdown process
sudo journalctl -u linux-log-agent -f &
sudo systemctl stop linux-log-agent
```

#### Solutions

**1. Force Stop**
```bash
# Immediate force stop
sudo systemctl kill linux-log-agent

# Stop with specific signal
sudo systemctl kill -s SIGTERM linux-log-agent
```

**2. Increase Timeout**
```bash
# Edit service file
sudo nano /etc/systemd/system/linux-log-agent.service

# Add or modify:
# TimeoutStopSec=30

sudo systemctl daemon-reload
```

### 💾 High Memory Usage

#### Symptoms
- Memory usage exceeding 256MB limit
- System performance degradation
- Out of memory errors

#### Diagnostic Steps
```bash
# Check current memory usage
sudo systemctl show linux-log-agent --property=MemoryCurrent
ps aux | grep linux-log-agent

# Monitor memory over time
watch -n 5 'sudo systemctl show linux-log-agent --property=MemoryCurrent'
```

#### Solutions

**1. Adjust Configuration**
```bash
# Reduce batch size
sudo nano /etc/linux-log-agent/config.yaml
# Set batch_size: 50 (instead of 100)

# Reduce buffer size
# Set buffer_size: 500 (instead of 1000)

sudo systemctl restart linux-log-agent
```

**2. Check for Memory Leaks**
```bash
# Restart service to clear memory
sudo systemctl restart linux-log-agent

# Monitor for gradual increase
watch -n 30 'sudo systemctl show linux-log-agent --property=MemoryCurrent'
```

### 🔍 Log File Analysis

#### View Recent Logs by Category
```bash
# Authentication logs
sudo grep '"source":"Auth"' /var/log/linux-log-agent/standardized_logs.json | tail -5

# Kernel logs
sudo grep '"source":"Kernel"' /var/log/linux-log-agent/standardized_logs.json | tail -5

# Network logs
sudo grep '"source":"Network"' /var/log/linux-log-agent/standardized_logs.json | tail -5
```

#### Monitor Real-time Collection
```bash
# Watch log collection in real-time
sudo tail -f /var/log/linux-log-agent/standardized_logs.json | jq -r '.timestamp + " | " + .source + " | " + .message[0:80]'

# Monitor errors
sudo tail -f /var/log/linux-log-agent/errors.log

# Monitor service logs
sudo journalctl -u linux-log-agent -f
```

## Performance Optimization

### Resource Monitoring
```bash
# CPU usage
top -p $(pgrep -f linux-log-agent)

# Memory usage details
sudo cat /proc/$(pgrep -f linux-log-agent)/status | grep -E "(VmRSS|VmSize)"

# I/O statistics
sudo iotop -p $(pgrep -f linux-log-agent)
```

### Configuration Tuning
```bash
# For high-volume environments
sudo nano /etc/linux-log-agent/config.yaml

# Adjust these settings:
# batch_size: 200          # Increase for better throughput
# processing_interval: 3   # Decrease for faster processing
# max_memory_mb: 512      # Increase if needed
# worker_threads: 4       # Increase for parallel processing
```

## Getting Help

### Information to Collect
When reporting issues, include:

```bash
# System information
uname -a
cat /etc/os-release

# Service status
sudo systemctl status linux-log-agent -l

# Recent logs
sudo journalctl -u linux-log-agent -n 50

# Configuration (remove sensitive data)
sudo cat /etc/linux-log-agent/config.yaml

# Resource usage
sudo systemctl show linux-log-agent --property=MemoryCurrent,CPUUsageNSec

# Error logs
sudo tail -n 50 /var/log/linux-log-agent/errors.log
```

### Support Channels
- **Documentation**: Check other files in the `docs/` directory
- **GitHub Issues**: Report bugs with detailed information
- **Community Forums**: Ask questions and share solutions
- **Log Analysis**: Always include relevant log excerpts

---

**Remember**: Most issues can be resolved by checking logs, verifying permissions, and ensuring proper configuration. Always start with the quick diagnostics section above.
