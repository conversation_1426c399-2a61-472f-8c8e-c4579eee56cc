collection:
  application_logs:
    enabled: true
    sources:
      - Application
      - Microsoft-Windows-*
  event_logs:
    enabled: true
    max_records: 100
    sources:
      - System
      - Application
  network_logs:
    enabled: true
    include_connections: true
    include_interface_changes: true
  packet_capture:
    enabled: true
    filter: ""
    interface: auto
    max_packets: 50
  security_logs:
    enabled: false
    include_authentication: true
    include_policy_changes: true
    include_privilege_use: true
  system_logs:
    enabled: true
    include_drivers: true
    include_hardware: true
    include_services: true
error_handling:
  error_log_path: logs/agent_errors.log
  log_errors: true
  retry_attempts: 3
  retry_delay: 5
exlog_api:
  api_key: bb93493a1a62ace8d41d25cf233f67b5cb8dbdd709d5617c914c299bc5e4e9a0
  batch_size: 10
  connection_pool_size: 10
  enabled: true
  endpoint: http://localhost:5000/api/v1/logs
  max_batch_wait_time: 5
  max_retries: 3
  offline_buffer:
    buffer_file: logs/api_buffer.json
    enabled: true
    max_size: 10000
    retry_interval: 60
  rate_limit:
    enabled: false
    requests_per_minute: 60
  retry_delay: 5
  timeout: 30
  validation:
    default_log_level: info
    default_source: System
    default_source_type: event
    fix_missing_fields: true
general:
  buffer_size: 1000
  log_level: INFO
  processing_interval: 5
  service_name: PythonLoggingAgent
output:
  console:
    enabled: false
  file:
    enabled: true
    path: logs/standardized_logs.json
    rotation:
      backup_count: 5
      enabled: true
      max_size: 100MB
  syslog:
    enabled: false
    host: localhost
    port: 514
performance:
  max_cpu_percent: 10
  max_memory_mb: 256
  worker_threads: 2
standardization:
  add_hostname: true
  add_source_metadata: true
  generate_log_id: true
  include_raw_data: false
  log_id:
    format: uuid4
    namespace: null
  output_format: json
  timestamp_format: iso8601
