#!/usr/bin/env python3
"""
Simple test script to validate GitLab CI YAML configurations
"""

import yaml
import os
import sys
from pathlib import Path

def validate_yaml_file(file_path):
    """Validate a YAML file for syntax errors"""
    try:
        with open(file_path, 'r') as f:
            yaml.safe_load(f)
        print(f"✓ {file_path} - Valid YAML syntax")
        return True
    except yaml.YAMLError as e:
        print(f"✗ {file_path} - YAML Error: {e}")
        return False
    except Exception as e:
        print(f"✗ {file_path} - Error: {e}")
        return False

def main():
    """Main validation function"""
    print("=== GitLab CI YAML Validation ===\n")
    
    # Files to validate
    files_to_check = [
        "cicd-templates/templates/base.yml",
        "cicd-templates/templates/python.yml", 
        "cicd-templates/templates/nodejs.yml",
        "cicd-templates/templates/docker.yml",
        "cicd-templates/templates/security-simple.yml",
        "backend/.gitlab-ci.yml",
        "dashboard/.gitlab-ci.yml",
        "linux-agent/.gitlab-ci.yml"
    ]
    
    all_valid = True
    
    for file_path in files_to_check:
        full_path = Path(file_path)
        if full_path.exists():
            if not validate_yaml_file(full_path):
                all_valid = False
        else:
            print(f"✗ {file_path} - File not found")
            all_valid = False
    
    print(f"\n=== Summary ===")
    if all_valid:
        print("✓ All YAML files are valid!")
        return 0
    else:
        print("✗ Some YAML files have errors")
        return 1

if __name__ == "__main__":
    sys.exit(main())
