const request = require('supertest');
const mongoose = require('mongoose');
const ExLogServer = require('../index');
const User = require('../models/User');
const Log = require('../models/Log');

describe('Logs API', () => {
  let app;
  let server;
  let authToken;
  let apiKey;
  let testUser;

  beforeAll(async () => {
    // Create server instance
    server = new ExLogServer();
    app = server.app;

    // Setup middleware and routes
    server.setupMiddleware();
    server.setupRoutes();

    // Create test user with API key
    testUser = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      status: 'active',
    });

    // Generate API key for the user
    const generatedApiKey = testUser.generateApiKey('Test Agent');
    apiKey = generatedApiKey.key;
    
    await testUser.save();

    // Generate auth token
    const jwt = require('jsonwebtoken');
    const config = require('../config');
    authToken = jwt.sign({ userId: testUser._id }, config.jwt.secret, {
      expiresIn: '1h',
    });
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Log.deleteMany({});
    
    // Close database connections
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
    }
  });

  beforeEach(async () => {
    // Clear logs before each test
    await Log.deleteMany({});
  });

  describe('POST /api/v1/logs', () => {
    const validLogData = {
      logs: [
        {
          logId: 'test_log_001',
          timestamp: '2024-01-01T12:00:00.000Z',
          source: 'System',
          sourceType: 'event',
          host: 'test-server-01',
          logLevel: 'info',
          message: 'Test log message',
          additionalFields: {
            processId: '1234',
            userId: 'testuser',
          },
        },
      ],
    };

    it('should successfully ingest logs with valid API key', async () => {
      const response = await request(app)
        .post('/api/v1/logs')
        .set('X-API-Key', apiKey)
        .send(validLogData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.processed).toBe(1);
      expect(response.body.data.failed).toBe(0);
      expect(response.body.data.results.successful).toHaveLength(1);
      expect(response.body.data.results.successful[0].logId).toBe('test_log_001');

      // Verify log was saved to database
      const savedLog = await Log.findOne({ logId: 'test_log_001' });
      expect(savedLog).toBeTruthy();
      expect(savedLog.message).toBe('Test log message');
      expect(savedLog.metadata.agentId).toBe(testUser._id.toString());
    });

    it('should reject request without API key', async () => {
      const response = await request(app)
        .post('/api/v1/logs')
        .send(validLogData)
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
      expect(response.body.message).toBe('API key is required');
    });

    it('should reject request with invalid API key', async () => {
      const response = await request(app)
        .post('/api/v1/logs')
        .set('X-API-Key', 'invalid-api-key')
        .send(validLogData)
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
      expect(response.body.message).toBe('Invalid API key');
    });

    it('should validate log data structure', async () => {
      const invalidLogData = {
        logs: [
          {
            logId: 'test_log_002',
            // Missing required fields
            message: 'Test message',
          },
        ],
      };

      const response = await request(app)
        .post('/api/v1/logs')
        .set('X-API-Key', apiKey)
        .send(invalidLogData)
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });

    it('should handle multiple logs in single request', async () => {
      const multipleLogsData = {
        logs: [
          {
            logId: 'test_log_003',
            timestamp: '2024-01-01T12:00:00.000Z',
            source: 'System',
            sourceType: 'event',
            host: 'test-server-01',
            logLevel: 'info',
            message: 'First test log',
          },
          {
            logId: 'test_log_004',
            timestamp: '2024-01-01T12:01:00.000Z',
            source: 'Application',
            sourceType: 'application',
            host: 'test-server-02',
            logLevel: 'warning',
            message: 'Second test log',
          },
        ],
      };

      const response = await request(app)
        .post('/api/v1/logs')
        .set('X-API-Key', apiKey)
        .send(multipleLogsData)
        .expect(201);

      expect(response.body.data.processed).toBe(2);
      expect(response.body.data.failed).toBe(0);

      // Verify both logs were saved
      const savedLogs = await Log.find({ logId: { $in: ['test_log_003', 'test_log_004'] } });
      expect(savedLogs).toHaveLength(2);
    });
  });

  describe('GET /api/v1/logs', () => {
    beforeEach(async () => {
      // Create test logs
      const testLogs = [
        {
          logId: 'get_test_001',
          timestamp: new Date('2024-01-01T12:00:00.000Z'),
          source: 'System',
          sourceType: 'event',
          host: 'server-01',
          logLevel: 'info',
          message: 'System startup',
        },
        {
          logId: 'get_test_002',
          timestamp: new Date('2024-01-01T12:01:00.000Z'),
          source: 'Application',
          sourceType: 'application',
          host: 'server-02',
          logLevel: 'error',
          message: 'Application error occurred',
        },
        {
          logId: 'get_test_003',
          timestamp: new Date('2024-01-01T12:02:00.000Z'),
          source: 'Security',
          sourceType: 'security',
          host: 'server-01',
          logLevel: 'warning',
          message: 'Security alert',
        },
      ];

      await Log.insertMany(testLogs);
    });

    it('should retrieve logs with authentication', async () => {
      const response = await request(app)
        .get('/api/v1/logs')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.logs).toHaveLength(3);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.totalCount).toBe(3);
    });

    it('should reject request without authentication', async () => {
      const response = await request(app)
        .get('/api/v1/logs')
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
    });

    it('should filter logs by source', async () => {
      const response = await request(app)
        .get('/api/v1/logs?source=System')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.logs).toHaveLength(1);
      expect(response.body.data.logs[0].source).toBe('System');
    });

    it('should filter logs by log level', async () => {
      const response = await request(app)
        .get('/api/v1/logs?logLevel=error')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.logs).toHaveLength(1);
      expect(response.body.data.logs[0].logLevel).toBe('error');
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/v1/logs?page=1&limit=2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.logs).toHaveLength(2);
      expect(response.body.data.pagination.currentPage).toBe(1);
      expect(response.body.data.pagination.limit).toBe(2);
      expect(response.body.data.pagination.hasNextPage).toBe(true);
    });

    it('should filter logs by time range', async () => {
      const response = await request(app)
        .get('/api/v1/logs?startTime=2024-01-01T12:01:00.000Z&endTime=2024-01-01T12:02:00.000Z')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.logs).toHaveLength(1);
      expect(response.body.data.logs[0].logId).toBe('get_test_002');
    });
  });

  describe('GET /api/v1/logs/:logId', () => {
    beforeEach(async () => {
      const testLog = new Log({
        logId: 'specific_test_001',
        timestamp: new Date('2024-01-01T12:00:00.000Z'),
        source: 'System',
        sourceType: 'event',
        host: 'server-01',
        logLevel: 'info',
        message: 'Specific log for testing',
      });
      await testLog.save();
    });

    it('should retrieve specific log by ID', async () => {
      const response = await request(app)
        .get('/api/v1/logs/specific_test_001')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.log.logId).toBe('specific_test_001');
      expect(response.body.data.log.message).toBe('Specific log for testing');
    });

    it('should return 404 for non-existent log', async () => {
      const response = await request(app)
        .get('/api/v1/logs/non_existent_log')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toBe('Not Found');
      expect(response.body.message).toBe('Log not found');
    });
  });
});
