[tool:pytest]
# Pytest configuration file
# This file provides common pytest settings for Python projects

# Minimum pytest version required
minversion = 6.0

# Default command line options
addopts = 
    # Show extra test summary info
    -ra
    # Treat unregistered markers as errors
    --strict-markers
    # Treat unknown config options as errors
    --strict-config
    # Enable coverage reporting
    --cov=.
    # Show missing lines in coverage report
    --cov-report=term-missing
    # Generate XML coverage report for CI
    --cov-report=xml:reports/coverage.xml
    # Generate HTML coverage report
    --cov-report=html:reports/htmlcov
    # Fail if coverage is below threshold
    --cov-fail-under=80
    # Show local variables in tracebacks
    --tb=short
    # Capture output (can be overridden with -s)
    --capture=no
    # Verbose output
    -v

# Test discovery patterns
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Custom markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    fast: marks tests as fast
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    smoke: marks tests as smoke tests
    api: marks tests that test API endpoints
    database: marks tests that require database
    network: marks tests that require network access
    windows: marks tests that only run on Windows
    linux: marks tests that only run on Linux
    requires_admin: marks tests that require admin privileges

# Filter warnings
filterwarnings =
    # Treat warnings as errors by default
    error
    # Ignore specific warnings
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    # Ignore warnings from third-party packages
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::ResourceWarning

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test timeout (in seconds)
timeout = 300

# Parallel execution settings (requires pytest-xdist)
# Uncomment to enable parallel test execution
# addopts = -n auto

# JUnit XML output for CI systems
junit_family = xunit2
junit_logging = all
junit_log_passing_tests = true

# Test collection settings
collect_ignore = [
    "setup.py",
    "build",
    "dist",
    ".tox",
    ".venv",
    "venv",
    "env",
    ".env"
]

# Doctest settings
doctest_optionflags = 
    NORMALIZE_WHITESPACE
    IGNORE_EXCEPTION_DETAIL
    ELLIPSIS

# Cache settings
cache_dir = .pytest_cache

# Temporary directory settings
tmp_path_retention_count = 3
tmp_path_retention_policy = failed
