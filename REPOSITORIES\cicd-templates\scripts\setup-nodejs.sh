#!/bin/bash
# Node.js environment setup script for CI/CD pipelines

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
NODE_VERSION=${NODE_VERSION:-"18"}
NPM_CACHE_DIR=${NPM_CACHE_DIR:-"$PWD/.cache/npm"}
PACKAGE_MANAGER=${PACKAGE_MANAGER:-"npm"}
INSTALL_DEV_DEPS=${INSTALL_DEV_DEPS:-"true"}
NODE_ENV=${NODE_ENV:-"test"}

# Main setup function
setup_nodejs_environment() {
    log_info "Setting up Node.js environment..."
    
    # Check Node.js version
    check_nodejs_version
    
    # Setup npm cache
    setup_npm_cache
    
    # Configure package manager
    configure_package_manager
    
    # Install dependencies
    install_dependencies
    
    # Install global tools
    install_global_tools
    
    # Verify installation
    verify_installation
    
    log_success "Node.js environment setup completed successfully!"
}

check_nodejs_version() {
    log_info "Checking Node.js version..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    CURRENT_VERSION=$(node --version)
    log_info "Current Node.js version: $CURRENT_VERSION"
    
    # Check if version meets minimum requirements
    MAJOR_VERSION=$(node --version | cut -d'.' -f1 | sed 's/v//')
    if [[ $MAJOR_VERSION -lt 16 ]]; then
        log_error "Node.js 16 or higher is required"
        exit 1
    fi
    
    log_success "Node.js version check passed"
}

setup_npm_cache() {
    log_info "Setting up npm cache directory..."
    
    mkdir -p "$NPM_CACHE_DIR"
    npm config set cache "$NPM_CACHE_DIR"
    
    log_info "NPM cache directory: $NPM_CACHE_DIR"
    log_success "NPM cache setup completed"
}

configure_package_manager() {
    log_info "Configuring package manager: $PACKAGE_MANAGER"
    
    case $PACKAGE_MANAGER in
        "npm")
            configure_npm
            ;;
        "yarn")
            configure_yarn
            ;;
        "pnpm")
            configure_pnpm
            ;;
        *)
            log_warning "Unknown package manager: $PACKAGE_MANAGER, defaulting to npm"
            PACKAGE_MANAGER="npm"
            configure_npm
            ;;
    esac
    
    log_success "Package manager configuration completed"
}

configure_npm() {
    log_info "Configuring npm..."
    
    # Set npm configurations
    npm config set fund false
    npm config set audit-level moderate
    npm config set progress false
    
    # Show npm version
    NPM_VERSION=$(npm --version)
    log_info "NPM version: $NPM_VERSION"
}

configure_yarn() {
    log_info "Configuring yarn..."
    
    if ! command -v yarn &> /dev/null; then
        log_info "Installing yarn..."
        npm install -g yarn
    fi
    
    # Show yarn version
    YARN_VERSION=$(yarn --version)
    log_info "Yarn version: $YARN_VERSION"
}

configure_pnpm() {
    log_info "Configuring pnpm..."
    
    if ! command -v pnpm &> /dev/null; then
        log_info "Installing pnpm..."
        npm install -g pnpm
    fi
    
    # Show pnpm version
    PNPM_VERSION=$(pnpm --version)
    log_info "PNPM version: $PNPM_VERSION"
}

install_dependencies() {
    log_info "Installing Node.js dependencies..."
    
    if [[ ! -f "package.json" ]]; then
        log_warning "No package.json found, skipping dependency installation"
        return
    fi
    
    case $PACKAGE_MANAGER in
        "npm")
            if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
                npm ci --cache "$NPM_CACHE_DIR"
            else
                npm ci --cache "$NPM_CACHE_DIR" --only=production
            fi
            ;;
        "yarn")
            if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
                yarn install --frozen-lockfile
            else
                yarn install --frozen-lockfile --production
            fi
            ;;
        "pnpm")
            if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
                pnpm install --frozen-lockfile
            else
                pnpm install --frozen-lockfile --prod
            fi
            ;;
    esac
    
    log_success "Dependencies installed successfully"
}

install_global_tools() {
    log_info "Installing global development tools..."
    
    if [[ "$INSTALL_DEV_DEPS" != "true" ]]; then
        log_info "Skipping global tools installation (INSTALL_DEV_DEPS=false)"
        return
    fi
    
    # Essential global tools for CI/CD
    GLOBAL_TOOLS=(
        "eslint"
        "prettier"
        "jest"
        "typescript"
        "@typescript-eslint/parser"
        "@typescript-eslint/eslint-plugin"
        "jest-junit"
    )
    
    for tool in "${GLOBAL_TOOLS[@]}"; do
        log_info "Installing global tool: $tool"
        case $PACKAGE_MANAGER in
            "npm")
                npm install -g "$tool" --cache "$NPM_CACHE_DIR" || log_warning "Failed to install $tool globally"
                ;;
            "yarn")
                yarn global add "$tool" || log_warning "Failed to install $tool globally"
                ;;
            "pnpm")
                pnpm add -g "$tool" || log_warning "Failed to install $tool globally"
                ;;
        esac
    done
    
    log_success "Global tools installation completed"
}

verify_installation() {
    log_info "Verifying installation..."
    
    # Show installed packages
    log_info "Installed packages:"
    case $PACKAGE_MANAGER in
        "npm")
            npm list --depth=0 || true
            ;;
        "yarn")
            yarn list --depth=0 || true
            ;;
        "pnpm")
            pnpm list --depth=0 || true
            ;;
    esac
    
    # Check if essential tools are available
    if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
        TOOLS_TO_CHECK=("eslint" "prettier" "jest" "tsc")
        
        for tool in "${TOOLS_TO_CHECK[@]}"; do
            if command -v "$tool" &> /dev/null || npx "$tool" --version &> /dev/null; then
                log_success "$tool is available"
            else
                log_warning "$tool is not available"
            fi
        done
    fi
    
    # Show Node.js and npm configuration
    log_info "Node.js configuration:"
    node -e "console.log('Node.js version:', process.version)"
    node -e "console.log('Platform:', process.platform)"
    node -e "console.log('Architecture:', process.arch)"
    
    log_success "Installation verification completed"
}

# Error handling
handle_error() {
    log_error "An error occurred during Node.js environment setup"
    log_error "Line $1: Command '$2' failed with exit code $3"
    exit 1
}

# Set up error handling
trap 'handle_error $LINENO "$BASH_COMMAND" $?' ERR

# Main execution
main() {
    log_info "Starting Node.js environment setup script"
    log_info "Configuration:"
    log_info "  Node.js version: $NODE_VERSION"
    log_info "  NPM cache dir: $NPM_CACHE_DIR"
    log_info "  Package manager: $PACKAGE_MANAGER"
    log_info "  Install dev deps: $INSTALL_DEV_DEPS"
    log_info "  Node environment: $NODE_ENV"
    
    # Set NODE_ENV
    export NODE_ENV
    
    setup_nodejs_environment
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
