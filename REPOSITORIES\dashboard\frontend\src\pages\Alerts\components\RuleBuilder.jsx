import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Chip,
  IconButton,
  Stack,
  Divider,
  Switch,
  FormControlLabel,
} from '@mui/material'
import {
  Add,
  Delete,
  Info,
  Security,
  Speed,
  Computer,
  NetworkCheck,
  Assignment,
} from '@mui/icons-material'
import { useDispatch } from 'react-redux'
import { createAlertRule } from '../../../store/slices/alertsSlice'

const RuleBuilder = ({ open, onClose, onRuleCreated }) => {
  const dispatch = useDispatch()
  const [activeStep, setActiveStep] = useState(0)
  const [loading, setLoading] = useState(false)
  
  const [ruleData, setRuleData] = useState({
    name: '',
    description: '',
    severity: 'medium',
    category: 'security',
    ruleType: 'threshold',
    enabled: true,
    priority: 5,
    conditions: {},
    timeWindow: {
      value: 15,
      unit: 'minutes',
    },
    threshold: {
      operator: '>',
      value: 5,
    },
    actions: [
      {
        type: 'create_alert',
        enabled: true,
        config: {},
      },
    ],
    suppressionRules: {
      enabled: false,
      duration: {
        value: 30,
        unit: 'minutes',
      },
    },
    tags: [],
  })

  const steps = ['Basic Info', 'Conditions', 'Actions', 'Review']

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1)
  }

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1)
  }

  const handleReset = () => {
    setActiveStep(0)
    setRuleData({
      name: '',
      description: '',
      severity: 'medium',
      category: 'security',
      ruleType: 'threshold',
      enabled: true,
      priority: 5,
      conditions: {},
      timeWindow: {
        value: 15,
        unit: 'minutes',
      },
      threshold: {
        operator: '>',
        value: 5,
      },
      actions: [
        {
          type: 'create_alert',
          enabled: true,
          config: {},
        },
      ],
      suppressionRules: {
        enabled: false,
        duration: {
          value: 30,
          unit: 'minutes',
        },
      },
      tags: [],
    })
  }

  const handleSubmit = async () => {
    setLoading(true)
    try {
      const result = await dispatch(createAlertRule(ruleData)).unwrap()
      onRuleCreated(result.rule)
      handleReset()
      onClose()
    } catch (error) {
      console.error('Failed to create rule:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateRuleData = (field, value) => {
    setRuleData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const updateNestedField = (path, value) => {
    setRuleData(prev => {
      const newData = { ...prev }
      const keys = path.split('.')
      let current = newData
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]]
      }
      
      current[keys[keys.length - 1]] = value
      return newData
    })
  }

  const addTag = (tag) => {
    if (tag && !ruleData.tags.includes(tag)) {
      updateRuleData('tags', [...ruleData.tags, tag])
    }
  }

  const removeTag = (tagToRemove) => {
    updateRuleData('tags', ruleData.tags.filter(tag => tag !== tagToRemove))
  }

  const renderBasicInfo = () => (
    <Stack spacing={3}>
      <TextField
        label="Rule Name"
        value={ruleData.name}
        onChange={(e) => updateRuleData('name', e.target.value)}
        fullWidth
        required
      />
      
      <TextField
        label="Description"
        value={ruleData.description}
        onChange={(e) => updateRuleData('description', e.target.value)}
        multiline
        rows={3}
        fullWidth
        required
      />
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <FormControl fullWidth>
          <InputLabel>Severity</InputLabel>
          <Select
            value={ruleData.severity}
            label="Severity"
            onChange={(e) => updateRuleData('severity', e.target.value)}
          >
            <MenuItem value="critical">Critical</MenuItem>
            <MenuItem value="high">High</MenuItem>
            <MenuItem value="medium">Medium</MenuItem>
            <MenuItem value="low">Low</MenuItem>
            <MenuItem value="informational">Informational</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl fullWidth>
          <InputLabel>Category</InputLabel>
          <Select
            value={ruleData.category}
            label="Category"
            onChange={(e) => updateRuleData('category', e.target.value)}
          >
            <MenuItem value="security">Security</MenuItem>
            <MenuItem value="performance">Performance</MenuItem>
            <MenuItem value="system">System</MenuItem>
            <MenuItem value="application">Application</MenuItem>
            <MenuItem value="network">Network</MenuItem>
            <MenuItem value="compliance">Compliance</MenuItem>
          </Select>
        </FormControl>
      </Box>
      
      <FormControl fullWidth>
        <InputLabel>Rule Type</InputLabel>
        <Select
          value={ruleData.ruleType}
          label="Rule Type"
          onChange={(e) => updateRuleData('ruleType', e.target.value)}
        >
          <MenuItem value="threshold">Threshold</MenuItem>
          <MenuItem value="pattern">Pattern</MenuItem>
          <MenuItem value="correlation">Correlation</MenuItem>
          <MenuItem value="anomaly">Anomaly</MenuItem>
          <MenuItem value="sequence">Sequence</MenuItem>
        </Select>
      </FormControl>
      
      <TextField
        label="Priority (1-10)"
        type="number"
        value={ruleData.priority}
        onChange={(e) => updateRuleData('priority', parseInt(e.target.value))}
        inputProps={{ min: 1, max: 10 }}
        fullWidth
      />
    </Stack>
  )

  const renderConditions = () => (
    <Stack spacing={3}>
      <Typography variant="h6">Rule Conditions</Typography>
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <TextField
          label="Time Window Value"
          type="number"
          value={ruleData.timeWindow.value}
          onChange={(e) => updateNestedField('timeWindow.value', parseInt(e.target.value))}
          inputProps={{ min: 1 }}
        />
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Unit</InputLabel>
          <Select
            value={ruleData.timeWindow.unit}
            label="Unit"
            onChange={(e) => updateNestedField('timeWindow.unit', e.target.value)}
          >
            <MenuItem value="seconds">Seconds</MenuItem>
            <MenuItem value="minutes">Minutes</MenuItem>
            <MenuItem value="hours">Hours</MenuItem>
            <MenuItem value="days">Days</MenuItem>
          </Select>
        </FormControl>
      </Box>
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Operator</InputLabel>
          <Select
            value={ruleData.threshold.operator}
            label="Operator"
            onChange={(e) => updateNestedField('threshold.operator', e.target.value)}
          >
            <MenuItem value=">">&gt;</MenuItem>
            <MenuItem value=">=">&gt;=</MenuItem>
            <MenuItem value="<">&lt;</MenuItem>
            <MenuItem value="<=">&lt;=</MenuItem>
            <MenuItem value="==">=</MenuItem>
            <MenuItem value="!=">!=</MenuItem>
          </Select>
        </FormControl>
        <TextField
          label="Threshold Value"
          type="number"
          value={ruleData.threshold.value}
          onChange={(e) => updateNestedField('threshold.value', parseInt(e.target.value))}
          inputProps={{ min: 0 }}
        />
      </Box>
      
      {ruleData.ruleType === 'threshold' && (
        <Stack spacing={2}>
          <TextField
            label="Field to Monitor"
            value={ruleData.conditions.field || ''}
            onChange={(e) => updateNestedField('conditions.field', e.target.value)}
            placeholder="e.g., logLevel, source, host"
            fullWidth
          />
          <TextField
            label="Field Value (optional)"
            value={ruleData.conditions.value || ''}
            onChange={(e) => updateNestedField('conditions.value', e.target.value)}
            placeholder="e.g., error, critical"
            fullWidth
          />
        </Stack>
      )}
      
      {ruleData.ruleType === 'pattern' && (
        <Stack spacing={2}>
          <TextField
            label="Pattern to Match"
            value={ruleData.conditions.pattern || ''}
            onChange={(e) => updateNestedField('conditions.pattern', e.target.value)}
            placeholder="e.g., login failed, authentication error"
            fullWidth
          />
          <TextField
            label="Log Level Filter (optional)"
            value={ruleData.conditions.logLevel || ''}
            onChange={(e) => updateNestedField('conditions.logLevel', e.target.value)}
            placeholder="e.g., error, warning"
            fullWidth
          />
        </Stack>
      )}
    </Stack>
  )

  const renderActions = () => (
    <Stack spacing={3}>
      <Typography variant="h6">Actions</Typography>
      
      {ruleData.actions.map((action, index) => (
        <Paper key={index} variant="outlined" sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle2">
              Action {index + 1}: {action.type.replace('_', ' ').toUpperCase()}
            </Typography>
            <Switch
              checked={action.enabled}
              onChange={(e) => {
                const newActions = [...ruleData.actions]
                newActions[index].enabled = e.target.checked
                updateRuleData('actions', newActions)
              }}
            />
          </Box>
        </Paper>
      ))}
      
      <Divider />
      
      <Typography variant="h6">Suppression Rules</Typography>
      <FormControlLabel
        control={
          <Switch
            checked={ruleData.suppressionRules.enabled}
            onChange={(e) => updateNestedField('suppressionRules.enabled', e.target.checked)}
          />
        }
        label="Enable Alert Suppression"
      />
      
      {ruleData.suppressionRules.enabled && (
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            label="Suppression Duration"
            type="number"
            value={ruleData.suppressionRules.duration.value}
            onChange={(e) => updateNestedField('suppressionRules.duration.value', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Unit</InputLabel>
            <Select
              value={ruleData.suppressionRules.duration.unit}
              label="Unit"
              onChange={(e) => updateNestedField('suppressionRules.duration.unit', e.target.value)}
            >
              <MenuItem value="seconds">Seconds</MenuItem>
              <MenuItem value="minutes">Minutes</MenuItem>
              <MenuItem value="hours">Hours</MenuItem>
            </Select>
          </FormControl>
        </Box>
      )}
    </Stack>
  )

  const renderReview = () => (
    <Stack spacing={3}>
      <Typography variant="h6">Review Rule Configuration</Typography>
      
      <Paper variant="outlined" sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Basic Information</Typography>
        <Typography variant="body2">Name: {ruleData.name}</Typography>
        <Typography variant="body2">Description: {ruleData.description}</Typography>
        <Typography variant="body2">Severity: {ruleData.severity}</Typography>
        <Typography variant="body2">Category: {ruleData.category}</Typography>
        <Typography variant="body2">Type: {ruleData.ruleType}</Typography>
      </Paper>
      
      <Paper variant="outlined" sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Conditions</Typography>
        <Typography variant="body2">
          Time Window: {ruleData.timeWindow.value} {ruleData.timeWindow.unit}
        </Typography>
        <Typography variant="body2">
          Threshold: {ruleData.threshold.operator} {ruleData.threshold.value}
        </Typography>
        <pre style={{ fontSize: '0.875rem', margin: '8px 0' }}>
          {JSON.stringify(ruleData.conditions, null, 2)}
        </pre>
      </Paper>
      
      <Paper variant="outlined" sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Actions</Typography>
        {ruleData.actions.map((action, index) => (
          <Chip
            key={index}
            label={`${action.type} (${action.enabled ? 'enabled' : 'disabled'})`}
            size="small"
            sx={{ mr: 1, mb: 1 }}
            color={action.enabled ? 'primary' : 'default'}
          />
        ))}
      </Paper>
    </Stack>
  )

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return renderBasicInfo()
      case 1:
        return renderConditions()
      case 2:
        return renderActions()
      case 3:
        return renderReview()
      default:
        return 'Unknown step'
    }
  }

  const isStepValid = (step) => {
    switch (step) {
      case 0:
        return ruleData.name && ruleData.description
      case 1:
        return ruleData.timeWindow.value > 0 && ruleData.threshold.value >= 0
      case 2:
        return ruleData.actions.length > 0
      case 3:
        return true
      default:
        return false
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create Alert Rule</DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          
          {renderStepContent(activeStep)}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Box sx={{ flex: '1 1 auto' }} />
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
        >
          Back
        </Button>
        {activeStep === steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading || !isStepValid(activeStep)}
          >
            {loading ? 'Creating...' : 'Create Rule'}
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={!isStepValid(activeStep)}
          >
            Next
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default RuleBuilder
