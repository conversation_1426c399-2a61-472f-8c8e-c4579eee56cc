```mermaid
sequenceDiagram
    participant LS as 🐧 Linux Systems
    participant WS as 🪟 Windows Systems
    participant LA as Linux Agent
    participant WA as Windows Agent
    participant API as 📥 Log Ingestion API
    participant AE as 🚨 Alert Engine
    participant DB as 🍃 MongoDB
    participant WSS as 🔌 WebSocket Server
    participant FE as ⚛️ React Frontend
    participant User as 👥 Security Analyst

    %% Log Collection Phase
    Note over LS,WS: Log Generation Phase
    LS->>LA: Generate syslog, auth, journal logs
    WS->>WA: Generate Windows Event Logs
    
    %% Agent Processing
    Note over LA,WA: Agent Processing Phase
    LA->>LA: Collect & Parse Logs
    WA->>WA: Collect & Parse Logs
    LA->>LA: Standardize to JSON
    WA->>WA: Standardize to JSON
    LA->>LA: Buffer & Batch
    WA->>WA: Buffer & Batch

    %% API Ingestion
    Note over LA,API: Log Transmission Phase
    LA->>API: POST /api/v1/logs (X-API-Key)
    WA->>API: POST /api/v1/logs (X-API-Key)
    
    API->>API: Validate API Key
    API->>API: Validate Log Schema
    API->>DB: Store Logs
    
    %% Alert Processing
    Note over API,AE: Alert Processing Phase
    API->>AE: Trigger Alert Analysis
    AE->>DB: Query Alert Rules
    AE->>AE: Pattern Matching
    AE->>DB: Store Generated Alerts
    AE->>WSS: Send Real-time Alert
    
    %% Real-time Updates
    Note over WSS,User: Real-time Notification Phase
    WSS->>FE: WebSocket Alert Notification
    FE->>User: Display Alert Popup
    
    %% User Interaction
    Note over User,DB: User Query Phase
    User->>FE: Search Logs
    FE->>API: GET /api/v1/logs?search=...
    API->>API: Authenticate JWT Token
    API->>DB: Query Logs Collection
    DB->>API: Return Log Results
    API->>FE: JSON Response
    FE->>User: Display Log Results
    
    %% Alert Management
    Note over User,DB: Alert Management Phase
    User->>FE: Acknowledge Alert
    FE->>API: PUT /api/v1/alerts/:id
    API->>DB: Update Alert Status
    API->>WSS: Broadcast Alert Update
    WSS->>FE: Real-time Status Update
    
    %% Agent Health Monitoring
    Note over LA,User: Health Monitoring Phase
    LA->>API: POST /api/v1/agents/heartbeat
    WA->>API: POST /api/v1/agents/heartbeat
    API->>DB: Update Agent Status
    User->>FE: View Agent Dashboard
    FE->>API: GET /api/v1/agents
    API->>DB: Query Agent Status
    DB->>API: Return Agent Data
    API->>FE: Agent Status Response
    FE->>User: Display Agent Health
```