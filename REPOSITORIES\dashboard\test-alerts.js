const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

// Test data for triggering different alert rules
const testLogs = [
  // Test 1: Multiple Failed Login Attempts (should trigger after 5 attempts)
  {
    logId: `test-${Date.now()}-001`,
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'web-server-01',
    logLevel: 'error',
    message: '<PERSON><PERSON> failed for user admin from IP ************* - invalid credentials',
    rawData: 'AUTH_FAILED: user=admin, ip=*************, reason=invalid_password',
    additionalFields: {
      eventId: 4625,
      eventCategory: 'Authentication',
      eventType: 'Login Failure'
    }
  },
  {
    logId: 'test-002',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'web-server-01',
    logLevel: 'error',
    message: '<PERSON><PERSON> failed for user admin from IP ************* - authentication failed',
    rawData: 'AUTH_FAILED: user=admin, ip=*************, reason=invalid_password',
    additionalFields: {
      eventId: 4625,
      eventCategory: 'Authentication',
      eventType: 'Login Failure'
    }
  },
  {
    logId: 'test-003',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'web-server-01',
    logLevel: 'error',
    message: 'Login failed for user admin from IP ************* - invalid credentials',
    rawData: 'AUTH_FAILED: user=admin, ip=*************, reason=invalid_password',
    additionalFields: {
      eventId: 4625,
      eventCategory: 'Authentication',
      eventType: 'Login Failure'
    }
  },
  {
    logId: 'test-004',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'web-server-01',
    logLevel: 'error',
    message: 'Authentication failed for user admin from IP *************',
    rawData: 'AUTH_FAILED: user=admin, ip=*************, reason=invalid_password',
    additionalFields: {
      eventId: 4625,
      eventCategory: 'Authentication',
      eventType: 'Login Failure'
    }
  },
  {
    logId: 'test-005',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'web-server-01',
    logLevel: 'error',
    message: 'Login failed for user admin from IP ************* - invalid credentials',
    rawData: 'AUTH_FAILED: user=admin, ip=*************, reason=invalid_password',
    additionalFields: {
      eventId: 4625,
      eventCategory: 'Authentication',
      eventType: 'Login Failure'
    }
  },
  
  // Test 2: Privilege Escalation Attempt
  {
    logId: 'test-006',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'app-server-01',
    logLevel: 'warning',
    message: 'User john attempted privilege escalation using sudo command',
    rawData: 'PRIV_ESC: user=john, command=sudo, target=root',
    additionalFields: {
      eventId: 4672,
      eventCategory: 'Privilege Use',
      eventType: 'Privilege Escalation'
    }
  },
  
  // Test 3: System Error Spike (need 21+ errors in 10 minutes)
  ...Array.from({ length: 25 }, (_, i) => ({
    logId: `test-error-${i + 1}`,
    timestamp: new Date().toISOString(),
    source: 'System',
    sourceType: 'application',
    host: 'app-server-02',
    logLevel: 'error',
    message: `System error occurred: Database connection timeout (error ${i + 1})`,
    rawData: `ERROR: db_timeout, connection_id=${i + 1}, duration=30s`,
    additionalFields: {
      eventId: 1001,
      eventCategory: 'System Error',
      eventType: 'Database Error'
    }
  })),
  
  // Test 4: Critical Service Failure
  {
    logId: 'test-007',
    timestamp: new Date().toISOString(),
    source: 'System',
    sourceType: 'application',
    host: 'db-server-01',
    logLevel: 'critical',
    message: 'Critical error: Database service stopped unexpectedly',
    rawData: 'SERVICE_STOP: service=mongodb, reason=fatal_error, exit_code=1',
    additionalFields: {
      eventId: 7034,
      eventCategory: 'Service Control',
      eventType: 'Service Failure'
    }
  },
  
  // Test 5: Data Access Violation
  {
    logId: 'test-008',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'file-server-01',
    logLevel: 'warning',
    message: 'Access denied: User jane attempted to access restricted file /etc/shadow',
    rawData: 'ACCESS_DENIED: user=jane, file=/etc/shadow, permission=read',
    additionalFields: {
      eventId: 4656,
      eventCategory: 'Object Access',
      eventType: 'Access Violation'
    }
  },
  {
    logId: 'test-009',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'file-server-01',
    logLevel: 'warning',
    message: 'Unauthorized access attempt to sensitive data by user jane',
    rawData: 'ACCESS_DENIED: user=jane, resource=sensitive_data, action=read',
    additionalFields: {
      eventId: 4656,
      eventCategory: 'Object Access',
      eventType: 'Access Violation'
    }
  },
  {
    logId: 'test-010',
    timestamp: new Date().toISOString(),
    source: 'Security',
    sourceType: 'security',
    host: 'file-server-01',
    logLevel: 'warning',
    message: 'Permission denied for user jane accessing confidential files',
    rawData: 'ACCESS_DENIED: user=jane, resource=confidential, action=write',
    additionalFields: {
      eventId: 4656,
      eventCategory: 'Object Access',
      eventType: 'Access Violation'
    }
  }
];

async function sendTestLogs() {
  console.log('Starting alert system test...');
  console.log(`Sending ${testLogs.length} test logs to trigger alerts`);
  
  try {
    // Send logs in batches to simulate real-world scenarios
    const batchSize = 5;
    for (let i = 0; i < testLogs.length; i += batchSize) {
      const batch = testLogs.slice(i, i + batchSize);
      
      console.log(`\nSending batch ${Math.floor(i / batchSize) + 1} (${batch.length} logs)...`);
      
      const response = await axios.post(`${API_BASE}/logs`, {
        logs: batch
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'test-api-key-12345' // Using API key authentication
        }
      });
      
      console.log(`✓ Batch sent successfully. Processed: ${response.data.data.processed}/${response.data.data.total}`);
      
      // Wait a bit between batches to allow correlation engine to process
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('\n✓ All test logs sent successfully!');
    console.log('\nWaiting 10 seconds for correlation engine to process...');
    
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Check if any alerts were created
    console.log('\nChecking for triggered alerts...');
    
    // We can't easily check via API without authentication, so let's check the database directly
    console.log('Check the database for alerts using:');
    console.log('docker exec dashboard-mongodb-1 mongosh exlog --eval "db.alerts.find({}, {name: 1, severity: 1, status: 1, triggeredAt: 1}).pretty()"');
    
  } catch (error) {
    console.error('Error sending test logs:', error.response?.data || error.message);
  }
}

// Run the test
sendTestLogs();
