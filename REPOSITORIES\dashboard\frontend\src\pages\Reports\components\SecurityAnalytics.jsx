import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Alert,
  CircularProgress,
} from '@mui/material'
import {
  Security,
  Warning,
  Error,
  Info,
  TrendingUp,
  TrendingDown,
  Public,
  Shield,
} from '@mui/icons-material'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import { useTheme } from '@mui/material/styles'
import {
  fetchSecurityAnalytics,
  fetchIncidentAnalytics,
  fetchThreatIntelligence,
} from '../../../store/slices/reportingSlice'

const SecurityAnalytics = ({ timeRange = '30d' }) => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const {
    securityAnalytics,
    incidentAnalytics,
    threatIntelligence,
    loading,
    error,
  } = useSelector(state => state.reporting)

  useEffect(() => {
    dispatch(fetchSecurityAnalytics(timeRange))
    dispatch(fetchIncidentAnalytics(timeRange))
    dispatch(fetchThreatIntelligence(timeRange))
  }, [dispatch, timeRange])

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return theme.palette.error.main
      case 'high': return theme.palette.warning.main
      case 'medium': return theme.palette.info.main
      case 'low': return theme.palette.success.main
      default: return theme.palette.grey[500]
    }
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <Error color="error" />
      case 'high': return <Warning color="warning" />
      case 'medium': return <Info color="info" />
      case 'low': return <Shield color="success" />
      default: return <Info />
    }
  }

  const renderSecurityPosture = () => {
    if (loading.securityAnalytics) {
      return (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    if (error.securityAnalytics) {
      return (
        <Card>
          <CardContent>
            <Alert severity="error">{error.securityAnalytics}</Alert>
          </CardContent>
        </Card>
      )
    }

    if (!securityAnalytics) return null

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Security Posture Overview
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h3" color="primary" gutterBottom>
                  {securityAnalytics.securityScore}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Security Score
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={securityAnalytics.securityScore}
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={8}>
              <Typography variant="subtitle1" gutterBottom>
                Recommendations
              </Typography>
              <List dense>
                {securityAnalytics.recommendations?.map((rec, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {getSeverityIcon(rec.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={rec.title}
                      secondary={rec.description}
                    />
                  </ListItem>
                ))}
                {(!securityAnalytics.recommendations || securityAnalytics.recommendations.length === 0) && (
                  <ListItem>
                    <ListItemIcon>
                      <Shield color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="No immediate recommendations"
                      secondary="Your security posture looks good!"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderIncidentAnalytics = () => {
    if (loading.incidentAnalytics || !incidentAnalytics) {
      return (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Incident Analytics</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    const severityData = incidentAnalytics.incidentsBySeverity?.map(item => ({
      name: item._id,
      value: item.count,
      color: getSeverityColor(item._id),
    })) || []

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Incident Analytics
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Incidents by Severity
              </Typography>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={severityData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {severityData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Response Metrics
              </Typography>
              <Box sx={{ py: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Mean Time to Detect</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round((incidentAnalytics.responseMetrics?.avgMTTD || 0) / (1000 * 60))}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Mean Time to Respond</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round((incidentAnalytics.responseMetrics?.avgMTTR || 0) / (1000 * 60))}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Resolution Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round(
                      ((incidentAnalytics.responseMetrics?.resolvedIncidents || 0) /
                      (incidentAnalytics.responseMetrics?.totalIncidents || 1)) * 100
                    )}%
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderThreatIntelligence = () => {
    if (loading.threatIntelligence || !threatIntelligence) {
      return (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Threat Intelligence</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Threat Intelligence
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Top Attack Vectors
              </Typography>
              <List dense>
                {threatIntelligence.attackVectors?.slice(0, 5).map((vector, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={vector._id || 'Unknown'}
                      secondary={`${vector.count} attempts`}
                    />
                    <Chip
                      label={vector.count}
                      size="small"
                      color="error"
                      variant="outlined"
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="No attack vectors detected"
                      secondary="System appears secure"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Geographic Attack Origins
              </Typography>
              <List dense>
                {threatIntelligence.geoAttacks?.slice(0, 5).map((geo, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Public />
                    </ListItemIcon>
                    <ListItemText
                      primary={geo.country || 'Unknown'}
                      secondary={`${geo.attackCount} attacks from ${geo.uniqueIPCount} IPs`}
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="No geographic data available"
                      secondary="Insufficient attack data"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
          </Grid>
          
          {threatIntelligence.emergingThreats?.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Emerging Threats (Last 7 Days)
              </Typography>
              <List dense>
                {threatIntelligence.emergingThreats.map((threat, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <TrendingUp color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={threat._id?.pattern || 'Unknown Pattern'}
                      secondary={`${threat.count} occurrences from ${threat._id?.source}`}
                    />
                    <Chip
                      label="New"
                      size="small"
                      color="warning"
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {renderSecurityPosture()}
        </Grid>
        
        <Grid item xs={12} lg={6}>
          {renderIncidentAnalytics()}
        </Grid>
        
        <Grid item xs={12} lg={6}>
          {renderThreatIntelligence()}
        </Grid>
      </Grid>
    </Box>
  )
}

export default SecurityAnalytics
