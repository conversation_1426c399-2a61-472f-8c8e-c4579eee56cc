import React from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  But<PERSON>,
  <PERSON><PERSON>,
} from '@mui/material'
import { Add, Computer } from '@mui/icons-material'

const Agents = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Agent Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage log collection agents across your infrastructure.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          disabled
        >
          Register Agent
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Computer sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Agent Management Coming Soon
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              This feature will allow you to register, configure, and monitor log collection agents
              deployed across your infrastructure.
            </Typography>
            <Alert severity="info">
              Agent management functionality will be implemented in the next development phase.
            </Alert>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default Agents
