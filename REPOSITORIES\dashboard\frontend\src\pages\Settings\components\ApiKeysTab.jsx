import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Typography,
  Alert,
  Tooltip,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { settingsService } from '../../../services/settingsService'

const ApiKeysTab = ({ onSuccess }) => {
  const [apiKeys, setApiKeys] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [createDialog, setCreateDialog] = useState(false)
  const [editDialog, setEditDialog] = useState(false)
  const [deleteDialog, setDeleteDialog] = useState(false)
  const [selectedKey, setSelectedKey] = useState(null)
  const [newApiKey, setNewApiKey] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    expiresAt: '',
    permissions: [],
    ipWhitelist: [],
  })
  const [newIp, setNewIp] = useState('')
  const [errors, setErrors] = useState({})

  useEffect(() => {
    loadApiKeys()
  }, [])

  const loadApiKeys = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.getApiKeys()
      setApiKeys(response.data.apiKeys || [])
    } catch (error) {
      onSuccess('Failed to load API keys', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateKey = async () => {
    const validationErrors = validateForm()
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsLoading(true)
    try {
      const response = await settingsService.createApiKey(formData)
      
      if (response.status === 'success') {
        setNewApiKey(response.data.apiKey.key)
        await loadApiKeys()
        onSuccess('API key created successfully')
        resetForm()
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to create API key', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateKey = async () => {
    const validationErrors = validateForm()
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsLoading(true)
    try {
      const response = await settingsService.updateApiKey(selectedKey._id, formData)
      
      if (response.status === 'success') {
        await loadApiKeys()
        setEditDialog(false)
        onSuccess('API key updated successfully')
        resetForm()
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update API key', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteKey = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.deleteApiKey(selectedKey._id)
      
      if (response.status === 'success') {
        await loadApiKeys()
        setDeleteDialog(false)
        onSuccess('API key deleted successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to delete API key', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    const errors = {}
    
    const nameError = settingsService.validateApiKeyName(formData.name)
    if (nameError) errors.name = nameError
    
    if (formData.expiresAt && new Date(formData.expiresAt) <= new Date()) {
      errors.expiresAt = 'Expiration date must be in the future'
    }
    
    // Validate IP addresses
    formData.ipWhitelist.forEach((ip, index) => {
      if (!settingsService.validateIpAddress(ip)) {
        errors[`ip_${index}`] = 'Invalid IP address format'
      }
    })
    
    return errors
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      expiresAt: '',
      permissions: [],
      ipWhitelist: [],
    })
    setErrors({})
    setSelectedKey(null)
  }

  const openCreateDialog = () => {
    resetForm()
    setCreateDialog(true)
  }

  const openEditDialog = (apiKey) => {
    setSelectedKey(apiKey)
    setFormData({
      name: apiKey.name,
      description: apiKey.description || '',
      expiresAt: apiKey.expiresAt ? new Date(apiKey.expiresAt).toISOString().split('T')[0] : '',
      permissions: apiKey.permissions || [],
      ipWhitelist: apiKey.ipWhitelist || [],
    })
    setEditDialog(true)
  }

  const openDeleteDialog = (apiKey) => {
    setSelectedKey(apiKey)
    setDeleteDialog(true)
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    onSuccess('Copied to clipboard')
  }

  const addIpToWhitelist = () => {
    if (newIp && settingsService.validateIpAddress(newIp)) {
      setFormData(prev => ({
        ...prev,
        ipWhitelist: [...prev.ipWhitelist, newIp],
      }))
      setNewIp('')
    } else {
      onSuccess('Invalid IP address format', 'error')
    }
  }

  const removeIpFromWhitelist = (index) => {
    setFormData(prev => ({
      ...prev,
      ipWhitelist: prev.ipWhitelist.filter((_, i) => i !== index),
    }))
  }

  const getStatusChip = (apiKey) => {
    if (!apiKey.isActive) {
      return <Chip label="Inactive" color="default" size="small" />
    }
    
    if (apiKey.expiresAt && new Date(apiKey.expiresAt) <= new Date()) {
      return <Chip label="Expired" color="error" size="small" />
    }
    
    return <Chip label="Active" color="success" size="small" />
  }

  return (
    <Box>
      <Card>
        <CardHeader
          title="API Keys"
          subheader="Manage API keys for programmatic access to ExLog"
          action={
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={openCreateDialog}
            >
              Create API Key
            </Button>
          }
        />
        <CardContent>
          {apiKeys.length === 0 ? (
            <Alert severity="info">
              No API keys found. Create your first API key to get started with programmatic access.
            </Alert>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Key</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Last Used</TableCell>
                    <TableCell>Usage Count</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {apiKeys.map((apiKey) => (
                    <TableRow key={apiKey._id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {apiKey.name}
                        </Typography>
                        {apiKey.description && (
                          <Typography variant="caption" color="text.secondary">
                            {apiKey.description}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontFamily="monospace">
                            {apiKey.key}
                          </Typography>
                          <Tooltip title="Copy to clipboard">
                            <IconButton
                              size="small"
                              onClick={() => copyToClipboard(apiKey.key)}
                            >
                              <CopyIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                      <TableCell>{getStatusChip(apiKey)}</TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {settingsService.formatTimeAgo(apiKey.lastUsed)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {apiKey.usageCount || 0}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {settingsService.formatDate(apiKey.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => openEditDialog(apiKey)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              onClick={() => openDeleteDialog(apiKey)}
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Create API Key Dialog */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New API Key</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Expiration Date"
                type="date"
                value={formData.expiresAt}
                onChange={(e) => setFormData(prev => ({ ...prev, expiresAt: e.target.value }))}
                error={!!errors.expiresAt}
                helperText={errors.expiresAt || 'Leave empty for no expiration'}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Permissions
              </Typography>
              <Grid container>
                {settingsService.getAvailablePermissions().map((permission) => (
                  <Grid item xs={12} sm={6} key={permission.value}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.permissions.includes(permission.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                permissions: [...prev.permissions, permission.value],
                              }))
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                permissions: prev.permissions.filter(p => p !== permission.value),
                              }))
                            }
                          }}
                        />
                      }
                      label={permission.label}
                    />
                  </Grid>
                ))}
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                IP Whitelist (Optional)
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <TextField
                  label="IP Address"
                  value={newIp}
                  onChange={(e) => setNewIp(e.target.value)}
                  placeholder="***********"
                  size="small"
                />
                <Button onClick={addIpToWhitelist} variant="outlined">
                  Add
                </Button>
              </Box>
              {formData.ipWhitelist.length > 0 && (
                <List dense>
                  {formData.ipWhitelist.map((ip, index) => (
                    <ListItem key={index}>
                      <ListItemText primary={ip} />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => removeIpFromWhitelist(index)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateKey} variant="contained" disabled={isLoading}>
            Create API Key
          </Button>
        </DialogActions>
      </Dialog>

      {/* Show New API Key Dialog */}
      <Dialog open={!!newApiKey} onClose={() => setNewApiKey('')} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            API Key Created
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This is the only time you will see this API key. Please copy it and store it securely.
          </Alert>
          <TextField
            fullWidth
            label="Your New API Key"
            value={newApiKey}
            InputProps={{
              readOnly: true,
              endAdornment: (
                <IconButton onClick={() => copyToClipboard(newApiKey)}>
                  <CopyIcon />
                </IconButton>
              ),
            }}
            sx={{ fontFamily: 'monospace' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewApiKey('')} variant="contained">
            I've Copied the Key
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit API Key Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit API Key</DialogTitle>
        <DialogContent>
          {/* Similar form as create, but without showing the key */}
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.isActive !== false}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label="Active"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button onClick={handleUpdateKey} variant="contained" disabled={isLoading}>
            Update API Key
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Delete API Key</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the API key "{selectedKey?.name}"? 
            This action cannot be undone and will immediately revoke access for any applications using this key.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDeleteKey} color="error" variant="contained" disabled={isLoading}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ApiKeysTab
