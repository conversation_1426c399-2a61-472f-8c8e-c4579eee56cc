import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks
export const fetchLogs = createAsyncThunk(
  'logs/fetchLogs',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/logs', { params })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch logs')
    }
  }
)

export const searchLogs = createAsyncThunk(
  'logs/searchLogs',
  async (searchParams, { rejectWithValue }) => {
    try {
      const response = await api.post('/logs/search', searchParams)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Search failed')
    }
  }
)

export const fetchLogStatistics = createAsyncThunk(
  'logs/fetchStatistics',
  async (timeRange = '24h', { rejectWithValue }) => {
    try {
      const response = await api.get(`/logs/statistics/summary?timeRange=${timeRange}`)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch statistics')
    }
  }
)

const initialState = {
  logs: [],
  statistics: null,
  pagination: {
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    limit: 100,
    hasNextPage: false,
    hasPrevPage: false,
  },
  filters: {
    startTime: null,
    endTime: null,
    source: '',
    logLevel: '',
    host: '',
    search: '',
  },
  isLoading: false,
  error: null,
}

const logsSlice = createSlice({
  name: 'logs',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = initialState.filters
    },
    clearLogs: (state) => {
      state.logs = []
      state.pagination = initialState.pagination
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch logs
      .addCase(fetchLogs.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchLogs.fulfilled, (state, action) => {
        state.isLoading = false
        state.logs = action.payload.data.logs
        state.pagination = action.payload.data.pagination
      })
      .addCase(fetchLogs.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Search logs
      .addCase(searchLogs.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(searchLogs.fulfilled, (state, action) => {
        state.isLoading = false
        state.logs = action.payload.data.logs
      })
      .addCase(searchLogs.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Fetch statistics
      .addCase(fetchLogStatistics.pending, (state) => {
        state.error = null
      })
      .addCase(fetchLogStatistics.fulfilled, (state, action) => {
        state.statistics = action.payload.data
      })
      .addCase(fetchLogStatistics.rejected, (state, action) => {
        state.error = action.payload
      })
  },
})

export const { clearError, setFilters, clearFilters, clearLogs } = logsSlice.actions

export default logsSlice.reducer
