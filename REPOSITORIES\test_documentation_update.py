#!/usr/bin/env python3
"""
Test script to validate documentation updates for v2.0.0
"""

import os
import sys
from pathlib import Path

def test_main_readme():
    """Test main README.md updates"""
    print("=== Testing Main README.md ===\n")
    
    readme_path = Path("cicd-templates/README.md")
    if not readme_path.exists():
        print("❌ Main README.md not found")
        return False
    
    with open(readme_path, encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Version badge", "Version-2.0.0-blue.svg"),
        ("Recent update notice", "Recent Major Update (v2.0.0)"),
        ("Simplified templates", "simplified, robust"),
        ("Dependency handling", "dependency installation issues"),
        ("Platform compatibility", "Windows/Linux dependency differences"),
        ("Quick start Python", "templates/python.yml"),
        ("Quick start Node.js", "templates/nodejs.yml"),
        ("Requirements-ci.txt", "requirements-ci.txt"),
        ("Changelog reference", "CHANGELOG.md"),
        ("Migration guide reference", "MIGRATION_GUIDE.md")
    ]
    
    all_passed = True
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def test_changelog():
    """Test CHANGELOG.md"""
    print("\n=== Testing CHANGELOG.md ===\n")
    
    changelog_path = Path("cicd-templates/CHANGELOG.md")
    if not changelog_path.exists():
        print("❌ CHANGELOG.md not found")
        return False
    
    with open(changelog_path, encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Version 2.0.0", "[2.0.0] - 2024-12-06"),
        ("Breaking changes", "BREAKING CHANGES"),
        ("Template simplification", "Complete template simplification"),
        ("Dependency handling", "Robust dependency handling"),
        ("Platform compatibility", "Platform compatibility"),
        ("Files modified", "Files Modified"),
        ("Migration notes", "Migration Notes"),
        ("Version comparison", "Version Comparison")
    ]
    
    all_passed = True
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def test_migration_guide():
    """Test MIGRATION_GUIDE.md"""
    print("\n=== Testing MIGRATION_GUIDE.md ===\n")
    
    migration_path = Path("cicd-templates/MIGRATION_GUIDE.md")
    if not migration_path.exists():
        print("❌ MIGRATION_GUIDE.md not found")
        return False
    
    with open(migration_path, encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Migration title", "Migration Guide: v1.x to v2.0.0"),
        ("Why migrate", "Why Migrate?"),
        ("Migration steps", "Migration Steps"),
        ("Requirements-ci.txt", "requirements-ci.txt"),
        ("Feature mapping", "Feature Mapping"),
        ("Enhancement phases", "Incremental Enhancement"),
        ("Troubleshooting", "Troubleshooting"),
        ("Success checklist", "Migration Checklist")
    ]
    
    all_passed = True
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def test_docs_readme():
    """Test docs/README.md updates"""
    print("\n=== Testing docs/README.md ===\n")
    
    docs_readme_path = Path("cicd-templates/docs/README.md")
    if not docs_readme_path.exists():
        print("❌ docs/README.md not found")
        return False
    
    with open(docs_readme_path, encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Version in title", "v2.0.0"),
        ("Major update notice", "Major Update Notice"),
        ("Simplified architecture", "Simplified architecture"),
        ("Repository structure", "Repository Structure (v2.0.0)"),
        ("Key features", "Key Features (v2.0.0)"),
        ("Python template v2", "Python Template (v2.0.0)"),
        ("Node.js template v2", "Node.js Template (v2.0.0)"),
        ("Enhancement path", "Enhancement Path (v2.0.0)"),
        ("Success stories", "Success Stories")
    ]
    
    all_passed = True
    for check_name, check_text in checks:
        if check_text in content:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def test_ci_requirements_files():
    """Test CI requirements files"""
    print("\n=== Testing CI Requirements Files ===\n")
    
    files_to_check = [
        ("backend/requirements-ci.txt", ["PyYAML", "requests", "psutil", "pytest"]),
        ("linux-agent/requirements-ci.txt", ["PyYAML", "requests", "python-systemd>=0.0.9", "pytest"])
    ]
    
    all_passed = True
    for file_path, expected_deps in files_to_check:
        path = Path(file_path)
        if path.exists():
            with open(path, encoding='utf-8') as f:
                content = f.read()
            
            print(f"✅ {file_path} exists")
            for dep in expected_deps:
                if dep in content:
                    print(f"  ✅ Contains {dep}")
                else:
                    print(f"  ❌ Missing {dep}")
                    all_passed = False
        else:
            print(f"❌ {file_path} not found")
            all_passed = False
    
    return all_passed

def main():
    """Main validation function"""
    print("=== Documentation Update Validation ===\n")
    
    tests = [
        ("Main README.md", test_main_readme),
        ("CHANGELOG.md", test_changelog),
        ("MIGRATION_GUIDE.md", test_migration_guide),
        ("docs/README.md", test_docs_readme),
        ("CI Requirements Files", test_ci_requirements_files)
    ]
    
    all_passed = True
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
            all_passed = False
    
    print("\n=== Final Summary ===")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    if all_passed:
        print("\n🎉 All documentation updates validated successfully!")
        print("\n📋 Documentation is ready for:")
        print("  • README.md - Updated with v2.0.0 features")
        print("  • CHANGELOG.md - Complete version history")
        print("  • MIGRATION_GUIDE.md - v1.x to v2.0.0 upgrade guide")
        print("  • docs/README.md - Updated technical documentation")
        print("  • CI requirements files - Platform-specific dependencies")
        return 0
    else:
        print("\n❌ Some documentation updates need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
