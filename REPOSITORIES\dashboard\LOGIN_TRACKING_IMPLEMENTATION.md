# Login Tracking and Activity Monitoring Implementation

## Overview

This document describes the comprehensive implementation of login tracking, failed login attempt monitoring, and user activity management in the ExLog dashboard system.

## Features Implemented

### 1. **Failed Login Attempt Tracking**
- **Automatic Increment**: Failed login attempts are automatically tracked and incremented
- **Account Locking**: Accounts are temporarily locked after too many failed attempts
- **IP Address Logging**: Failed attempts include IP address and device information
- **Failure Reason Tracking**: Specific reasons for failure (invalid password, account locked, etc.)

### 2. **Login History Tracking**
- **Comprehensive Logging**: Every login attempt (successful and failed) is recorded
- **Device Information**: Browser, OS, device type detection from User-Agent
- **IP Address Tracking**: Client IP address recording for security monitoring
- **Timestamp Recording**: Precise timestamp for each login attempt
- **Location Tracking**: Ready for geolocation integration

### 3. **Session Management**
- **Session ID Generation**: Unique session identifiers for each login
- **Device Tracking**: Session tied to specific device/browser information
- **Activity Monitoring**: Last activity timestamp updates on each request
- **Session Termination**: Individual and bulk session termination capabilities
- **Remember Me Support**: Extended session duration for persistent logins

### 4. **Activity Dashboard**
- **Dual-Tab Interface**: Separate views for login history and active sessions
- **Real-time Statistics**: Login success rates, failed attempts, active sessions
- **Advanced Filtering**: Filter by success/failure, date range, device type
- **Session Management**: Terminate individual sessions or all other sessions
- **Security Alerts**: Visual indicators for suspicious activity

## Backend Implementation

### Authentication Route Updates (`backend/src/routes/auth.js`)

#### Enhanced Login Process:
```javascript
// Device information parsing
const deviceInfo = {
  userAgent,
  ip: clientIP,
  device: 'Desktop/Mobile/Tablet',
  browser: 'Chrome/Firefox/Safari/Edge',
  os: 'Windows/macOS/Linux/Android/iOS'
}

// Failed login tracking
user.loginHistory.push({
  timestamp: new Date(),
  ip: clientIP,
  userAgent,
  success: false,
  failureReason: 'Invalid password/Account locked/Account inactive',
  deviceInfo
})

// Successful login tracking
user.loginHistory.push({
  timestamp: new Date(),
  ip: clientIP,
  userAgent,
  success: true,
  deviceInfo
})

// Session creation
user.sessions.push({
  sessionId,
  deviceInfo,
  createdAt: new Date(),
  lastActivity: new Date(),
  isActive: true,
  rememberMe
})
```

#### Token Generation with Session ID:
- JWT tokens now include session ID for tracking
- Session activity updates on each authenticated request
- Session termination on logout

### User Activity API Endpoints (`backend/src/routes/users.js`)

#### New Endpoints:
1. **GET `/api/v1/users/:id/activity`**
   - Returns login history, sessions, and statistics
   - Accessible by user themselves or admins
   - Sorted by timestamp (newest first)

2. **POST `/api/v1/users/:id/sessions/:sessionId/terminate`**
   - Terminates a specific user session
   - Logs termination activity

3. **POST `/api/v1/users/:id/sessions/terminate-all`**
   - Terminates all other sessions except current
   - Bulk session management

### Authentication Middleware Updates (`backend/src/middleware/auth.js`)

#### Session Activity Tracking:
- Updates `lastActivity` timestamp on each request
- Validates session is still active
- Extracts session ID from JWT tokens

### User Model Updates (`backend/src/models/User.js`)

#### Schema Enhancements:
- `loginHistory` array with detailed login attempt records
- `sessions` array with active session tracking
- Automatic cleanup (last 100 login entries, last 10 sessions)

## Frontend Implementation

### Settings Activity Tab (`frontend/src/pages/Settings/components/ActivityTab.jsx`)

#### Features:
1. **Tabbed Interface**:
   - Login History tab with filtering and pagination
   - Active Sessions tab with management controls

2. **Activity Statistics**:
   - Total logins, successful logins, failed logins
   - Success rate percentage
   - Active sessions count

3. **Login History Management**:
   - Advanced filtering (success/failure, date range, device type)
   - Pagination support
   - Device and browser information display
   - IP address tracking

4. **Session Management**:
   - View all active sessions
   - Terminate individual sessions
   - Terminate all other sessions
   - Session details (device, browser, IP, creation time, last activity)

5. **Security Features**:
   - Visual status indicators (success/failed chips)
   - Device type icons
   - Security tips and warnings
   - Confirmation dialogs for session termination

### Settings Service Updates (`frontend/src/services/settingsService.js`)

#### New Methods:
- `getUserActivity()`: Fetch complete user activity data
- `terminateSession(sessionId)`: Terminate specific session
- `terminateAllOtherSessions()`: Bulk session termination
- Enhanced `getLoginHistory()` with pagination simulation

## Security Features

### 1. **Failed Login Protection**
- Automatic account locking after multiple failed attempts
- IP address logging for security monitoring
- Detailed failure reason tracking

### 2. **Session Security**
- Unique session IDs prevent session hijacking
- Session activity tracking detects inactive sessions
- Bulk session termination for security incidents

### 3. **Device Tracking**
- Browser and OS detection from User-Agent
- Device type classification (Desktop/Mobile/Tablet)
- IP address recording for location tracking

### 4. **Activity Monitoring**
- Comprehensive login history with timestamps
- Real-time session status tracking
- Security statistics and success rate monitoring

## Data Structure

### Login History Entry:
```javascript
{
  timestamp: Date,
  ip: String,
  userAgent: String,
  success: Boolean,
  failureReason: String, // Only for failed attempts
  deviceInfo: {
    userAgent: String,
    ip: String,
    device: String,
    browser: String,
    os: String
  }
}
```

### Session Entry:
```javascript
{
  sessionId: String,
  deviceInfo: Object,
  createdAt: Date,
  lastActivity: Date,
  isActive: Boolean,
  rememberMe: Boolean
}
```

## Usage Instructions

### For Users:
1. **View Activity**: Go to Settings → Activity tab
2. **Monitor Logins**: Check login history for suspicious activity
3. **Manage Sessions**: View and terminate active sessions
4. **Security Review**: Monitor failed login attempts and success rates

### For Administrators:
1. **User Monitoring**: Access any user's activity via Users → Edit → Activity
2. **Security Investigation**: Review failed login patterns
3. **Session Management**: Terminate suspicious sessions
4. **Bulk Operations**: Terminate all sessions for compromised accounts

## Testing

### Login Tracking Tests:
- ✅ Failed login attempts increment correctly
- ✅ Account locking after multiple failures
- ✅ Successful login resets attempt counter
- ✅ Login history records all attempts
- ✅ Device information parsing works correctly

### Session Management Tests:
- ✅ Session creation on login
- ✅ Session activity updates on requests
- ✅ Session termination on logout
- ✅ Bulk session termination
- ✅ Session status tracking

### Frontend Tests:
- ✅ Activity tab displays login history
- ✅ Session management interface works
- ✅ Filtering and pagination function
- ✅ Statistics display correctly
- ✅ Session termination dialogs work

## Security Considerations

1. **Data Retention**: Login history limited to 100 entries per user
2. **Session Limits**: Maximum 10 active sessions per user
3. **IP Privacy**: IP addresses stored for security but should comply with privacy laws
4. **Session Security**: Session IDs are cryptographically secure
5. **Activity Logging**: All session terminations are logged for audit trails

## Future Enhancements

1. **Geolocation**: Add location detection based on IP addresses
2. **Anomaly Detection**: Implement ML-based suspicious activity detection
3. **Email Notifications**: Send alerts for suspicious login attempts
4. **Two-Factor Authentication**: Integrate with 2FA systems
5. **Advanced Analytics**: Detailed security analytics and reporting
