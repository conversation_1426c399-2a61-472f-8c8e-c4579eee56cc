#!/usr/bin/env python3
"""
GitLab CI/CD Pipeline Functionality Tester
This script tests the actual functionality of the pipeline components.
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import List, Tuple, Optional

class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def log_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")

def log_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")

def log_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")

def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.END} {message}")

def run_command(command: List[str], cwd: Optional[Path] = None, capture_output: bool = True) -> Tuple[bool, str, str]:
    """Run a command and return success status, stdout, stderr"""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=capture_output,
            text=True,
            timeout=60
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def test_python_tools():
    """Test Python development tools"""
    log_info("Testing Python tools...")
    
    tools_to_test = [
        (['python', '--version'], "Python interpreter"),
        (['pip', '--version'], "pip package manager"),
    ]
    
    # Test if we can install Python tools
    python_tools = ['flake8', 'black', 'isort', 'pytest', 'bandit', 'safety']
    
    all_good = True
    
    for command, description in tools_to_test:
        success, stdout, stderr = run_command(command)
        if success:
            log_success(f"✓ {description}: {stdout.strip()}")
        else:
            log_error(f"✗ {description}: {stderr}")
            all_good = False
    
    # Test Python tool installation
    log_info("Testing Python tool installation...")
    for tool in python_tools:
        success, stdout, stderr = run_command(['pip', 'show', tool])
        if success:
            log_success(f"✓ {tool} is available")
        else:
            log_info(f"Installing {tool}...")
            success, stdout, stderr = run_command(['pip', 'install', tool])
            if success:
                log_success(f"✓ {tool} installed successfully")
            else:
                log_warning(f"⚠ Failed to install {tool}: {stderr}")
    
    return all_good

def test_nodejs_tools():
    """Test Node.js development tools"""
    log_info("Testing Node.js tools...")
    
    tools_to_test = [
        (['node', '--version'], "Node.js runtime"),
        (['npm', '--version'], "npm package manager"),
    ]
    
    all_good = True
    
    for command, description in tools_to_test:
        success, stdout, stderr = run_command(command)
        if success:
            log_success(f"✓ {description}: {stdout.strip()}")
        else:
            log_error(f"✗ {description}: {stderr}")
            all_good = False
    
    return all_good

def test_python_linting():
    """Test Python linting with actual code"""
    log_info("Testing Python linting functionality...")
    
    # Create a temporary Python file with some issues
    test_code = '''
import os,sys
import json

def bad_function( x,y ):
    password = "hardcoded_password"
    if x==y:
        return True
    else:
        return False

class TestClass:
    def __init__(self):
        pass
    
    def method_with_issues(self):
        unused_var = "this is not used"
        return "test"
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        test_file = Path(f.name)
    
    try:
        # Test flake8
        log_info("Testing flake8...")
        success, stdout, stderr = run_command(['flake8', str(test_file)])
        if not success:  # We expect flake8 to find issues
            log_success("✓ flake8 detected code issues (as expected)")
        else:
            log_warning("⚠ flake8 didn't detect any issues")
        
        # Test black (check mode)
        log_info("Testing black...")
        success, stdout, stderr = run_command(['black', '--check', '--diff', str(test_file)])
        if not success:  # We expect black to find formatting issues
            log_success("✓ black detected formatting issues (as expected)")
        else:
            log_warning("⚠ black didn't detect any formatting issues")
        
        # Test bandit
        log_info("Testing bandit...")
        success, stdout, stderr = run_command(['bandit', str(test_file)])
        if not success:  # We expect bandit to find security issues
            log_success("✓ bandit detected security issues (as expected)")
        else:
            log_warning("⚠ bandit didn't detect any security issues")
        
        return True
        
    except Exception as e:
        log_error(f"Error testing Python linting: {e}")
        return False
    finally:
        # Clean up
        if test_file.exists():
            test_file.unlink()

def test_configuration_files():
    """Test that configuration files are valid and usable"""
    log_info("Testing configuration files...")
    
    # Test flake8 config
    flake8_config = Path('cicd-templates/configs/python/.flake8')
    if flake8_config.exists():
        # Create a simple Python file to test with
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('print("hello world")\n')
            test_file = Path(f.name)
        
        try:
            success, stdout, stderr = run_command([
                'flake8', 
                f'--config={flake8_config}', 
                str(test_file)
            ])
            if success:
                log_success("✓ flake8 configuration is valid")
            else:
                log_warning(f"⚠ flake8 configuration issue: {stderr}")
        finally:
            test_file.unlink()
    
    # Test pytest config
    pytest_config = Path('cicd-templates/configs/python/pytest.ini')
    if pytest_config.exists():
        log_success("✓ pytest configuration file exists")
    
    # Test ESLint config
    eslint_config = Path('cicd-templates/configs/eslint/.eslintrc.js')
    if eslint_config.exists():
        log_success("✓ ESLint configuration file exists")
    
    # Test Prettier config
    prettier_config = Path('cicd-templates/configs/eslint/.prettierrc')
    if prettier_config.exists():
        log_success("✓ Prettier configuration file exists")
    
    return True

def test_setup_scripts():
    """Test setup scripts functionality"""
    log_info("Testing setup scripts...")
    
    # Test Python setup script
    python_script = Path('cicd-templates/scripts/setup-python.sh')
    if python_script.exists():
        log_success("✓ Python setup script exists")
        
        # Check if script has proper shebang and functions
        with open(python_script, 'r') as f:
            content = f.read()
            if content.startswith('#!/bin/bash'):
                log_success("✓ Python setup script has proper shebang")
            else:
                log_warning("⚠ Python setup script missing shebang")
    
    # Test Node.js setup script
    nodejs_script = Path('cicd-templates/scripts/setup-nodejs.sh')
    if nodejs_script.exists():
        log_success("✓ Node.js setup script exists")
        
        # Check if script has proper shebang and functions
        with open(nodejs_script, 'r') as f:
            content = f.read()
            if content.startswith('#!/bin/bash'):
                log_success("✓ Node.js setup script has proper shebang")
            else:
                log_warning("⚠ Node.js setup script missing shebang")
    
    # Test security script
    security_script = Path('cicd-templates/scripts/security-scan.sh')
    if security_script.exists():
        log_success("✓ Security scan script exists")
    
    return True

def test_project_configurations():
    """Test individual project GitLab CI configurations"""
    log_info("Testing project configurations...")
    
    projects = [
        ('backend', 'Python backend agent'),
        ('dashboard', 'Full-stack dashboard'),
        ('linux-agent', 'Linux agent')
    ]
    
    all_good = True
    
    for project_dir, description in projects:
        gitlab_ci = Path(f'{project_dir}/.gitlab-ci.yml')
        if gitlab_ci.exists():
            log_success(f"✓ {description} GitLab CI configuration exists")
            
            # Check if it includes the templates
            with open(gitlab_ci, 'r') as f:
                content = f.read()
                if 'cicd-templates' in content:
                    log_success(f"✓ {description} references cicd-templates")
                else:
                    log_warning(f"⚠ {description} doesn't reference cicd-templates")
                    all_good = False
        else:
            log_error(f"✗ {description} GitLab CI configuration missing")
            all_good = False
    
    return all_good

def main():
    """Main test function"""
    log_info("Starting GitLab CI/CD Pipeline Functionality Testing")
    log_info("=" * 60)
    
    test_results = []
    
    # Test Python tools
    test_results.append(("Python Tools", test_python_tools()))
    
    # Test Node.js tools
    test_results.append(("Node.js Tools", test_nodejs_tools()))
    
    # Test Python linting functionality
    test_results.append(("Python Linting", test_python_linting()))
    
    # Test configuration files
    test_results.append(("Configuration Files", test_configuration_files()))
    
    # Test setup scripts
    test_results.append(("Setup Scripts", test_setup_scripts()))
    
    # Test project configurations
    test_results.append(("Project Configurations", test_project_configurations()))
    
    # Summary
    log_info("\n" + "=" * 60)
    log_info("FUNCTIONALITY TEST SUMMARY")
    log_info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        if result:
            log_success(f"✓ {test_name}")
            passed_tests += 1
        else:
            log_error(f"✗ {test_name}")
    
    log_info(f"\nTests passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        log_success("\n🎉 All functionality tests passed! Pipeline is ready to use.")
        return 0
    else:
        log_error(f"\n❌ {total_tests - passed_tests} test(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        log_info("\nTesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)
