import React from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Box,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  Error,
  Warning,
  Info,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  TrendingFlat,
  Refresh,
} from '@mui/icons-material'

const AlertsStatistics = ({ statistics = {}, loading = false }) => {
  const {
    total = 0,
    critical = 0,
    high = 0,
    medium = 0,
    low = 0,
    new: newAlerts = 0,
    acknowledged = 0,
    resolved = 0,
    avgResolutionTime = 0,
  } = statistics

  const severityStats = [
    {
      label: 'Critical',
      value: critical,
      color: 'error',
      icon: <Error />,
      percentage: total > 0 ? (critical / total) * 100 : 0,
    },
    {
      label: 'High',
      value: high,
      color: 'warning',
      icon: <Warning />,
      percentage: total > 0 ? (high / total) * 100 : 0,
    },
    {
      label: 'Medium',
      value: medium,
      color: 'info',
      icon: <Info />,
      percentage: total > 0 ? (medium / total) * 100 : 0,
    },
    {
      label: 'Low',
      value: low,
      color: 'success',
      icon: <CheckCircle />,
      percentage: total > 0 ? (low / total) * 100 : 0,
    },
  ]

  const statusStats = [
    {
      label: 'New',
      value: newAlerts,
      color: 'error',
      percentage: total > 0 ? (newAlerts / total) * 100 : 0,
    },
    {
      label: 'Acknowledged',
      value: acknowledged,
      color: 'warning',
      percentage: total > 0 ? (acknowledged / total) * 100 : 0,
    },
    {
      label: 'Resolved',
      value: resolved,
      color: 'success',
      percentage: total > 0 ? (resolved / total) * 100 : 0,
    },
  ]

  const formatResolutionTime = (minutes) => {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`
    } else if (minutes < 1440) {
      return `${Math.round(minutes / 60)}h`
    } else {
      return `${Math.round(minutes / 1440)}d`
    }
  }

  const getTrendIcon = (trend) => {
    if (trend > 0) return <TrendingUp color="error" />
    if (trend < 0) return <TrendingDown color="success" />
    return <TrendingFlat color="disabled" />
  }

  return (
    <Grid container spacing={3}>
      {/* Total Alerts */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom variant="overline">
                  Total Alerts
                </Typography>
                <Typography variant="h4" component="div">
                  {loading ? '-' : total.toLocaleString()}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  {getTrendIcon(0)}
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 0.5 }}>
                    Last 24h
                  </Typography>
                </Box>
              </Box>
              <Error color="primary" sx={{ fontSize: 40, opacity: 0.3 }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Active Alerts */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom variant="overline">
                  Active Alerts
                </Typography>
                <Typography variant="h4" component="div">
                  {loading ? '-' : (newAlerts + acknowledged).toLocaleString()}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip 
                    label={`${newAlerts} new`} 
                    size="small" 
                    color="error" 
                    variant="outlined"
                  />
                </Box>
              </Box>
              <Warning color="warning" sx={{ fontSize: 40, opacity: 0.3 }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Resolution Rate */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box sx={{ width: '100%' }}>
                <Typography color="textSecondary" gutterBottom variant="overline">
                  Resolution Rate
                </Typography>
                <Typography variant="h4" component="div">
                  {loading ? '-' : total > 0 ? `${Math.round((resolved / total) * 100)}%` : '0%'}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={total > 0 ? (resolved / total) * 100 : 0}
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Box>
              <CheckCircle color="success" sx={{ fontSize: 40, opacity: 0.3 }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Avg Resolution Time */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="textSecondary" gutterBottom variant="overline">
                  Avg Resolution Time
                </Typography>
                <Typography variant="h4" component="div">
                  {loading ? '-' : formatResolutionTime(avgResolutionTime)}
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                  {avgResolutionTime > 0 ? `${Math.round(avgResolutionTime)} minutes` : 'No data'}
                </Typography>
              </Box>
              <TrendingUp color="info" sx={{ fontSize: 40, opacity: 0.3 }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Severity Breakdown */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Alerts by Severity
            </Typography>
            <Box sx={{ mt: 2 }}>
              {severityStats.map((stat) => (
                <Box key={stat.label} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {React.cloneElement(stat.icon, { 
                        color: stat.color, 
                        sx: { fontSize: 20, mr: 1 } 
                      })}
                      <Typography variant="body2">
                        {stat.label}
                      </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight="bold">
                      {stat.value}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stat.percentage}
                    color={stat.color}
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Status Breakdown */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Alerts by Status
            </Typography>
            <Box sx={{ mt: 2 }}>
              {statusStats.map((stat) => (
                <Box key={stat.label} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2">
                      {stat.label}
                    </Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {stat.value}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stat.percentage}
                    color={stat.color}
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default AlertsStatistics
