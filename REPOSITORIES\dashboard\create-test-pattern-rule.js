// Script to create a simple pattern rule for testing
db = db.getSiblingDB('exlog');

const testRule = {
  name: 'Authentication Failure Pattern Test',
  description: 'Simple pattern rule to test authentication failure detection',
  enabled: true,
  severity: 'medium',
  category: 'security',
  ruleType: 'pattern',
  conditions: {
    field: 'logLevel',
    value: 'error',
    pattern: 'authentication failed|login failed|invalid credentials'
  },
  actions: [
    {
      type: 'create_alert',
      enabled: true
    }
  ],
  suppressionRules: {
    enabled: false
  },
  escalationRules: {
    enabled: false,
    levels: []
  },
  tags: ['test', 'authentication', 'pattern'],
  createdBy: ObjectId('68479e8c88170afc02956f2e'),
  lastModifiedBy: ObjectId('68479e8c88170afc02956f2e'),
  isDefault: false,
  priority: 5,
  statistics: {
    totalTriggers: 0,
    lastTriggered: null,
    falsePositives: 0,
    avgResolutionTime: 0
  },
  metadata: {
    version: '1.0.0',
    source: 'test',
    lastTested: null,
    performance: {
      avgExecutionTime: 0,
      lastExecutionTime: 0
    }
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

const result = db.alertrules.insertOne(testRule);
print('Test pattern rule created:', JSON.stringify(result));

// Verify the rule was created
const createdRule = db.alertrules.findOne({name: 'Authentication Failure Pattern Test'});
print('Created rule:', JSON.stringify(createdRule, null, 2));
