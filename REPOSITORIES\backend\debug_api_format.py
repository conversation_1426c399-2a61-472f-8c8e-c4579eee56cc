#!/usr/bin/env python3
"""
Debug script to test ExLog API format requirements

This script sends different log formats to identify what causes validation errors.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    import requests
    from config.config_manager import ConfigManager
except ImportError as e:
    print(f"Error importing: {e}")
    sys.exit(1)


def test_log_format(test_name, log_data):
    """Test a specific log format."""
    print(f"\n=== Testing: {test_name} ===")
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        endpoint = api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        api_key = api_config.get('api_key', '')
        
        # Prepare payload
        payload = {"logs": [log_data]}
        
        # Send request
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
        
        print(f"Sending log: {json.dumps(log_data, indent=2)}")
        
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ SUCCESS!")
            return True
        else:
            print("❌ FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def main():
    """Test different log formats to identify validation issues."""
    print("ExLog API Format Debug Tool")
    print("=" * 50)
    
    # Test 1: Minimal valid log
    test_log_format("Minimal Valid Log", {
        "log_id": "test-001",
        "timestamp": "2025-06-02T14:30:00Z",
        "source": "System",
        "source_type": "event",
        "host": "test-host",
        "log_level": "info",
        "message": "Test message",
        "raw_data": None,
        "additional_fields": {}
    })
    
    # Test 2: Log without timezone in timestamp
    test_log_format("Timestamp without timezone", {
        "log_id": "test-002",
        "timestamp": "2025-06-02T14:30:00",
        "source": "System",
        "source_type": "event",
        "host": "test-host",
        "log_level": "info",
        "message": "Test message",
        "raw_data": None,
        "additional_fields": {}
    })
    
    # Test 3: Log with different source values
    test_log_format("Different source value", {
        "log_id": "test-003",
        "timestamp": "2025-06-02T14:30:00Z",
        "source": "Application",
        "source_type": "application",
        "host": "test-host",
        "log_level": "warning",
        "message": "Test message",
        "raw_data": None,
        "additional_fields": {}
    })
    
    # Test 4: Log with complex additional_fields (like your actual logs)
    test_log_format("Complex additional_fields", {
        "log_id": "test-004",
        "timestamp": "2025-06-02T14:30:00Z",
        "source": "System",
        "source_type": "event",
        "host": "DESKTOP-PLUAU4C",
        "log_level": "warning",
        "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.",
        "raw_data": None,
        "additional_fields": {
            "record_number": 71841,
            "computer_name": "DESKTOP-PLUAU4C",
            "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"],
            "data": None,
            "event_id": 1014,
            "event_category": 1014,
            "event_type": 2,
            "metadata": {
                "collection_time": "2025-05-30T00:43:47.522277",
                "agent_version": "1.0.0",
                "standardizer_version": "1.0.0",
                "windows_event_log": True,
                "event_log_source": "System"
            }
        }
    })
    
    # Test 5: Log with missing optional fields
    test_log_format("Missing optional fields", {
        "log_id": "test-005",
        "timestamp": "2025-06-02T14:30:00Z",
        "source": "System",
        "source_type": "event",
        "host": "test-host",
        "log_level": "info",
        "message": "Test message"
        # Missing raw_data and additional_fields
    })
    
    # Test 6: Log with null values
    test_log_format("Null values", {
        "log_id": "test-006",
        "timestamp": "2025-06-02T14:30:00Z",
        "source": "System",
        "source_type": "event",
        "host": "test-host",
        "log_level": "info",
        "message": "Test message",
        "raw_data": None,
        "additional_fields": None
    })
    
    # Test 7: Your actual log format (from standardized_logs.json)
    test_log_format("Your actual log format", {
        "log_id": "94feaade-5de3-44b7-958a-13ea30ad8c41",
        "timestamp": "2025-05-30T00:41:24",
        "source": "System",
        "source_type": "event",
        "host": "DESKTOP-PLUAU4C",
        "log_level": "warning",
        "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.",
        "raw_data": None,
        "additional_fields": {
            "record_number": 71841,
            "computer_name": "DESKTOP-PLUAU4C",
            "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"],
            "data": None,
            "event_id": 1014,
            "event_category": 1014,
            "event_type": 2,
            "metadata": {
                "collection_time": "2025-05-30T00:43:47.522277",
                "agent_version": "1.0.0",
                "standardizer_version": "1.0.0",
                "windows_event_log": True,
                "event_log_source": "System"
            }
        }
    })
    
    # Test 8: Your actual log format with fixed timestamp
    test_log_format("Your log format with Z timezone", {
        "log_id": "94feaade-5de3-44b7-958a-13ea30ad8c41",
        "timestamp": "2025-05-30T00:41:24Z",  # Added Z
        "source": "System",
        "source_type": "event",
        "host": "DESKTOP-PLUAU4C",
        "log_level": "warning",
        "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.",
        "raw_data": None,
        "additional_fields": {
            "record_number": 71841,
            "computer_name": "DESKTOP-PLUAU4C",
            "string_inserts": ["**************.in-addr.arpa.", "128"],  # Shortened for testing
            "data": None,
            "event_id": 1014,
            "event_category": 1014,
            "event_type": 2,
            "metadata": {
                "collection_time": "2025-05-30T00:43:47.522277",
                "agent_version": "1.0.0",
                "standardizer_version": "1.0.0",
                "windows_event_log": True,
                "event_log_source": "System"
            }
        }
    })


if __name__ == "__main__":
    main()
