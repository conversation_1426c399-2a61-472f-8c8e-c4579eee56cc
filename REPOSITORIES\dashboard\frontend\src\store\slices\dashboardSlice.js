import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks
export const fetchDashboardOverview = createAsyncThunk(
  'dashboard/fetchOverview',
  async (timeRange = '24h', { rejectWithValue }) => {
    try {
      const response = await api.get(`/dashboards/overview?timeRange=${timeRange}`)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard overview')
    }
  }
)

export const fetchSystemHealth = createAsyncThunk(
  'dashboard/fetchSystemHealth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboards/system-health')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch system health')
    }
  }
)

const initialState = {
  overview: null,
  systemHealth: null,
  isLoading: false,
  isLoadingHealth: false,
  error: null,
  lastUpdated: null,
}

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setLastUpdated: (state) => {
      state.lastUpdated = new Date().toISOString()
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch dashboard overview
      .addCase(fetchDashboardOverview.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardOverview.fulfilled, (state, action) => {
        state.isLoading = false
        state.overview = action.payload.data
        state.lastUpdated = new Date().toISOString()
      })
      .addCase(fetchDashboardOverview.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Fetch system health
      .addCase(fetchSystemHealth.pending, (state) => {
        state.isLoadingHealth = true
      })
      .addCase(fetchSystemHealth.fulfilled, (state, action) => {
        state.isLoadingHealth = false
        state.systemHealth = action.payload.data
      })
      .addCase(fetchSystemHealth.rejected, (state, action) => {
        state.isLoadingHealth = false
        state.error = action.payload
      })
  },
})

export const { clearError, setLastUpdated } = dashboardSlice.actions
export default dashboardSlice.reducer
