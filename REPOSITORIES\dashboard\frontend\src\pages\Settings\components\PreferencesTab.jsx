import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Divider,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { useSelector } from 'react-redux'
import { settingsService } from '../../../services/settingsService'

const PreferencesTab = ({ onSuccess }) => {
  const { user } = useSelector((state) => state.auth)
  const [isLoading, setIsLoading] = useState(false)
  const [preferences, setPreferences] = useState({
    theme: 'light',
    timezone: 'UTC',
    language: 'en',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    dashboardRefreshInterval: 30,
    logsPerPage: 50,
    notifications: {
      email: true,
      inApp: true,
      alerts: {
        critical: true,
        high: true,
        medium: false,
        low: false,
      },
      digest: {
        enabled: false,
        frequency: 'weekly',
        time: '09:00',
      },
    },
    dashboard: {
      defaultView: 'overview',
      autoRefresh: true,
      compactMode: false,
    },
    security: {
      sessionTimeout: 60,
      requireMfaForSensitiveActions: false,
      loginNotifications: true,
      allowMultipleSessions: true,
    },
  })
  const [originalPreferences, setOriginalPreferences] = useState({})
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    if (user?.preferences) {
      setPreferences(user.preferences)
      setOriginalPreferences(user.preferences)
    }
  }, [user])

  useEffect(() => {
    const changed = JSON.stringify(preferences) !== JSON.stringify(originalPreferences)
    setHasChanges(changed)
  }, [preferences, originalPreferences])

  const handleChange = (section, field, value) => {
    if (section) {
      setPreferences(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value,
        },
      }))
    } else {
      setPreferences(prev => ({
        ...prev,
        [field]: value,
      }))
    }
  }

  const handleNestedChange = (section, subsection, field, value) => {
    setPreferences(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value,
        },
      },
    }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.updatePreferences(preferences)
      
      if (response.status === 'success') {
        setOriginalPreferences(preferences)
        onSuccess('Preferences updated successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update preferences', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setPreferences(originalPreferences)
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Appearance Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Appearance" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Theme</InputLabel>
                    <Select
                      value={preferences.theme}
                      label="Theme"
                      onChange={(e) => handleChange(null, 'theme', e.target.value)}
                    >
                      {settingsService.getAvailableThemes().map(theme => (
                        <MenuItem key={theme.value} value={theme.value}>
                          {theme.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Language</InputLabel>
                    <Select
                      value={preferences.language}
                      label="Language"
                      onChange={(e) => handleChange(null, 'language', e.target.value)}
                    >
                      {settingsService.getAvailableLanguages().map(lang => (
                        <MenuItem key={lang.value} value={lang.value}>
                          {lang.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Date Format</InputLabel>
                    <Select
                      value={preferences.dateFormat}
                      label="Date Format"
                      onChange={(e) => handleChange(null, 'dateFormat', e.target.value)}
                    >
                      {settingsService.getDateFormats().map(format => (
                        <MenuItem key={format.value} value={format.value}>
                          {format.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Time Format</InputLabel>
                    <Select
                      value={preferences.timeFormat}
                      label="Time Format"
                      onChange={(e) => handleChange(null, 'timeFormat', e.target.value)}
                    >
                      {settingsService.getTimeFormats().map(format => (
                        <MenuItem key={format.value} value={format.value}>
                          {format.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={preferences.timezone}
                      label="Timezone"
                      onChange={(e) => handleChange(null, 'timezone', e.target.value)}
                    >
                      {settingsService.getAvailableTimezones().map(tz => (
                        <MenuItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Dashboard Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Dashboard" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Default View</InputLabel>
                    <Select
                      value={preferences.dashboard?.defaultView || 'overview'}
                      label="Default View"
                      onChange={(e) => handleChange('dashboard', 'defaultView', e.target.value)}
                    >
                      <MenuItem value="overview">Overview</MenuItem>
                      <MenuItem value="logs">Logs</MenuItem>
                      <MenuItem value="alerts">Alerts</MenuItem>
                      <MenuItem value="agents">Agents</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Typography gutterBottom>
                    Refresh Interval: {preferences.dashboardRefreshInterval} seconds
                  </Typography>
                  <Slider
                    value={preferences.dashboardRefreshInterval}
                    onChange={(e, value) => handleChange(null, 'dashboardRefreshInterval', value)}
                    min={10}
                    max={300}
                    step={10}
                    marks={[
                      { value: 10, label: '10s' },
                      { value: 60, label: '1m' },
                      { value: 180, label: '3m' },
                      { value: 300, label: '5m' },
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography gutterBottom>
                    Logs per Page: {preferences.logsPerPage}
                  </Typography>
                  <Slider
                    value={preferences.logsPerPage}
                    onChange={(e, value) => handleChange(null, 'logsPerPage', value)}
                    min={10}
                    max={200}
                    step={10}
                    marks={[
                      { value: 10, label: '10' },
                      { value: 50, label: '50' },
                      { value: 100, label: '100' },
                      { value: 200, label: '200' },
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.dashboard?.autoRefresh || false}
                        onChange={(e) => handleChange('dashboard', 'autoRefresh', e.target.checked)}
                      />
                    }
                    label="Auto-refresh dashboard"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.dashboard?.compactMode || false}
                        onChange={(e) => handleChange('dashboard', 'compactMode', e.target.checked)}
                      />
                    }
                    label="Compact mode"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Notifications" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="h6" gutterBottom>
                    General Notifications
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.email || false}
                        onChange={(e) => handleChange('notifications', 'email', e.target.checked)}
                      />
                    }
                    label="Email notifications"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.inApp || false}
                        onChange={(e) => handleChange('notifications', 'inApp', e.target.checked)}
                      />
                    }
                    label="In-app notifications"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="h6" gutterBottom>
                    Alert Notifications
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.alerts?.critical || false}
                        onChange={(e) => handleNestedChange('notifications', 'alerts', 'critical', e.target.checked)}
                      />
                    }
                    label="Critical alerts"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.alerts?.high || false}
                        onChange={(e) => handleNestedChange('notifications', 'alerts', 'high', e.target.checked)}
                      />
                    }
                    label="High priority alerts"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.alerts?.medium || false}
                        onChange={(e) => handleNestedChange('notifications', 'alerts', 'medium', e.target.checked)}
                      />
                    }
                    label="Medium priority alerts"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications?.alerts?.low || false}
                        onChange={(e) => handleNestedChange('notifications', 'alerts', 'low', e.target.checked)}
                      />
                    }
                    label="Low priority alerts"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Digest Notifications
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={preferences.notifications?.digest?.enabled || false}
                            onChange={(e) => handleNestedChange('notifications', 'digest', 'enabled', e.target.checked)}
                          />
                        }
                        label="Enable digest emails"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth disabled={!preferences.notifications?.digest?.enabled}>
                        <InputLabel>Frequency</InputLabel>
                        <Select
                          value={preferences.notifications?.digest?.frequency || 'weekly'}
                          label="Frequency"
                          onChange={(e) => handleNestedChange('notifications', 'digest', 'frequency', e.target.value)}
                        >
                          <MenuItem value="daily">Daily</MenuItem>
                          <MenuItem value="weekly">Weekly</MenuItem>
                          <MenuItem value="monthly">Monthly</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Time"
                        type="time"
                        value={preferences.notifications?.digest?.time || '09:00'}
                        onChange={(e) => handleNestedChange('notifications', 'digest', 'time', e.target.value)}
                        disabled={!preferences.notifications?.digest?.enabled}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          {hasChanges && (
            <Alert severity="info" sx={{ mb: 2 }}>
              You have unsaved changes. Don't forget to save your preferences.
            </Alert>
          )}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={!hasChanges || isLoading}
            >
              Save Preferences
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleReset}
              disabled={!hasChanges || isLoading}
            >
              Reset Changes
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}

export default PreferencesTab
