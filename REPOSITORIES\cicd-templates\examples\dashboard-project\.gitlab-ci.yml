# Example GitLab CI/CD Configuration for Dashboard Projects
# This example shows how to use the centralized templates for a full-stack dashboard

# Include centralized templates
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'
      - 'templates/docker.yml'
      - 'templates/security-simple.yml'

# Project-specific variables
variables:
  # Node.js configuration
  NODE_VERSION: "18"
  NODE_ENV: "test"
  
  # Project identification
  PROJECT_NAME: "example-dashboard"
  PROJECT_TYPE: "fullstack-dashboard"
  
  # Quality gates
  COVERAGE_THRESHOLD: "75"
  QUALITY_GATE_ENABLED: "true"
  
  # Security scanning
  SECURITY_SCAN_ENABLED: "true"
  ZAP_TARGET_URL: "http://localhost:3000"
  
  # Docker configuration
  DOCKER_BUILDKIT: 1
  
  # Development tools
  INSTALL_DEV_DEPS: "true"

# Override cache for this project
cache:
  key: 
    files:
      - package-lock.json
      - frontend/package-lock.json
      - backend/package-lock.json
  paths:
    - .cache/npm/
    - node_modules/
    - frontend/node_modules/
    - backend/node_modules/

# Custom validation for dashboard projects
validate_dashboard_structure:
  stage: validate
  extends: 
    - .base_job
    - .retry_policy
  image: node:18
  script:
    - echo "Validating dashboard project structure..."
    - |
      # Check for dashboard-specific files
      dashboard_files=(
        "package.json"
        "docker-compose.yml"
        "frontend/package.json"
        "backend/package.json"
        "frontend/Dockerfile"
        "backend/Dockerfile"
        "nginx/nginx.conf"
      )
      
      for file in "${dashboard_files[@]}"; do
        if [[ ! -f "$file" ]]; then
          echo "❌ Missing required file: $file"
          exit 1
        else
          echo "✅ Found: $file"
        fi
      done
    - |
      # Check for dashboard-specific directories
      dashboard_dirs=(
        "frontend"
        "backend"
        "nginx"
        "scripts"
      )
      
      for dir in "${dashboard_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
          echo "❌ Missing required directory: $dir"
          exit 1
        else
          echo "✅ Found directory: $dir"
        fi
      done
    - echo "✅ Dashboard structure validation passed"

# Setup workspace for monorepo
setup_dashboard_workspace:
  stage: validate
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  script:
    - echo "Setting up dashboard workspace..."
    - npm ci --cache $NPM_CACHE_DIR
    - cd frontend && npm ci --cache ../$NPM_CACHE_DIR
    - cd ../backend && npm ci --cache ../$NPM_CACHE_DIR
    - echo "✅ Workspace setup completed"
  artifacts:
    paths:
      - node_modules/
      - frontend/node_modules/
      - backend/node_modules/
      - .cache/npm/
    expire_in: 2 hours

# Frontend-specific jobs
frontend_lint:
  stage: quality
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  script:
    - echo "Running frontend linting..."
    - cd frontend
    - mkdir -p ../reports
    - npx eslint src/ --config ../../cicd-templates/configs/eslint/.eslintrc.js --format junit --output-file ../reports/frontend-eslint.xml
    - npx prettier --config ../../cicd-templates/configs/eslint/.prettierrc --check src/
    - echo "✅ Frontend linting passed"
  artifacts:
    reports:
      junit: reports/frontend-eslint.xml
    paths:
      - reports/
    expire_in: 1 week

frontend_test:
  stage: test
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  script:
    - echo "Running frontend tests..."
    - cd frontend
    - mkdir -p ../reports
    - npm test -- --coverage --coverageReporters=cobertura --coverageReporters=text --coverageDirectory=../reports/frontend-coverage --testResultsProcessor=jest-junit
    - echo "✅ Frontend tests passed"
  artifacts:
    reports:
      junit: reports/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: reports/frontend-coverage/cobertura-coverage.xml
    paths:
      - reports/
    expire_in: 1 week
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'

frontend_build:
  stage: build
  extends:
    - .nodejs_base
    - .build_template
  needs: 
    - job: frontend_test
      artifacts: false
    - job: frontend_lint
      artifacts: false
  script:
    - echo "Building frontend application..."
    - cd frontend
    - npm run build
    - echo "✅ Frontend build completed"
    - ls -la dist/
  artifacts:
    paths:
      - frontend/dist/
    expire_in: 1 week

# Backend-specific jobs
backend_lint:
  stage: quality
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  script:
    - echo "Running backend linting..."
    - cd backend
    - mkdir -p ../reports
    - npx eslint src/ --config ../../cicd-templates/configs/eslint/.eslintrc.js --format junit --output-file ../reports/backend-eslint.xml
    - npx prettier --config ../../cicd-templates/configs/eslint/.prettierrc --check src/
    - echo "✅ Backend linting passed"
  artifacts:
    reports:
      junit: reports/backend-eslint.xml
    paths:
      - reports/
    expire_in: 1 week

backend_test:
  stage: test
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  services:
    - mongo:4.4
    - redis:6-alpine
  variables:
    MONGODB_URI: "mongodb://mongo:27017/exlog_test"
    REDIS_URL: "redis://redis:6379"
    NODE_ENV: "test"
  script:
    - echo "Running backend tests..."
    - cd backend
    - mkdir -p ../reports
    - npm test -- --coverage --coverageReporters=cobertura --coverageReporters=text --coverageDirectory=../reports/backend-coverage --testResultsProcessor=jest-junit
    - echo "✅ Backend tests passed"
  artifacts:
    reports:
      junit: reports/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: reports/backend-coverage/cobertura-coverage.xml
    paths:
      - reports/
    expire_in: 1 week
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'

# Integration tests
test_api_integration:
  stage: test
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  services:
    - mongo:4.4
    - redis:6-alpine
  variables:
    MONGODB_URI: "mongodb://mongo:27017/exlog_integration"
    REDIS_URL: "redis://redis:6379"
    NODE_ENV: "test"
  script:
    - echo "Running API integration tests..."
    - cd backend
    - npm run test:integration || echo "No integration tests defined"
    - echo "✅ API integration tests completed"
  artifacts:
    paths:
      - reports/
    expire_in: 1 week
  allow_failure: true

# End-to-end tests
test_e2e:
  stage: test
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: 
    - job: frontend_build
      artifacts: true
    - job: setup_dashboard_workspace
      artifacts: true
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - echo "Running end-to-end tests..."
    - docker-compose up -d
    - sleep 60  # Wait for all services to start
    - |
      # Wait for services to be healthy
      timeout=300
      while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3000/health && curl -f http://localhost:5000/health; then
          echo "✅ Services are healthy"
          break
        fi
        echo "Waiting for services to be ready..."
        sleep 10
        timeout=$((timeout-10))
      done
    - cd frontend
    - npm run test:e2e || echo "No E2E tests defined"
    - docker-compose down
    - echo "✅ E2E tests completed"
  artifacts:
    paths:
      - reports/
    expire_in: 1 week
  allow_failure: true

# Performance tests
test_performance:
  stage: test
  extends:
    - .nodejs_base
    - .base_job
    - .retry_policy
  needs: ["setup_dashboard_workspace"]
  services:
    - mongo:4.4
    - redis:6-alpine
  script:
    - echo "Running performance tests..."
    - cd backend
    - npm install -g artillery
    - artillery quick --count 10 --num 5 http://localhost:5000/api/v1/health || echo "Performance tests completed"
    - echo "✅ Performance tests completed"
  allow_failure: true

# Deploy to staging
deploy_staging:
  stage: deploy
  extends: .deploy_template
  variables:
    ENVIRONMENT_NAME: "staging"
    ENVIRONMENT_URL: "https://staging-dashboard.example.com"
  script:
    - echo "Deploying dashboard to staging environment..."
    - echo "This would deploy using docker-compose to staging servers"
    - echo "✅ Staging deployment completed"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

# Deploy to production
deploy_production:
  stage: deploy
  extends: .deploy_template
  variables:
    ENVIRONMENT_NAME: "production"
    ENVIRONMENT_URL: "https://dashboard.example.com"
  script:
    - echo "Deploying dashboard to production environment..."
    - echo "This would deploy using docker-compose to production servers"
    - echo "✅ Production deployment completed"
  rules:
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
      when: manual
  environment:
    name: production
    url: https://dashboard.example.com
