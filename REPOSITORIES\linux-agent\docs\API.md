# ExLog API Integration

This document describes how the Linux Log Collection Agent integrates with the ExLog dashboard API.

## API Overview

The agent sends collected and standardized logs to the ExLog dashboard via HTTP POST requests to the `/api/v1/logs` endpoint.

### Base Configuration

```yaml
exlog_api:
  enabled: true
  endpoint: "http://your-exlog-server:5000/api/v1/logs"
  api_key: "your-secure-api-key"
  batch_size: 100
  timeout: 30
  max_retries: 3
```

## Authentication

The agent uses Bearer token authentication with the API key:

```http
Authorization: Bearer your-secure-api-key
```

## Request Format

### HTTP Request
```http
POST /api/v1/logs HTTP/1.1
Host: your-exlog-server:5000
Content-Type: application/json
Authorization: Bearer your-secure-api-key

{
  "logs": [
    {
      "log_id": "uuid-string",
      "timestamp": "2025-06-19T20:30:45",
      "source": "Auth",
      "source_type": "auth",
      "host": "hostname",
      "log_level": "info",
      "message": "User login successful",
      "raw_data": "original log line",
      "additional_fields": {
        "program": "sshd",
        "pid": 1234,
        "facility": "auth",
        "priority": 6
      }
    }
  ]
}
```

### Log Entry Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `log_id` | string | Yes | Unique identifier (UUID4) |
| `timestamp` | string | Yes | ISO 8601 timestamp |
| `source` | string | Yes | Log category (Auth, Kernel, Network, etc.) |
| `source_type` | string | Yes | Source type identifier |
| `host` | string | Yes | Hostname where log originated |
| `log_level` | string | Yes | Log severity (critical, error, warning, info, debug) |
| `message` | string | Yes | Processed log message |
| `raw_data` | string | No | Original log line (if enabled) |
| `additional_fields` | object | No | Additional metadata and parsed fields |

## Log Categories

The agent categorizes logs into the following sources:

### Auth Logs
```json
{
  "source": "Auth",
  "source_type": "auth",
  "additional_fields": {
    "auth_method": "pam",
    "auth_service": "sudo",
    "auth_event_type": "session_opened",
    "username": "user",
    "uid": 1000
  }
}
```

### Kernel Logs
```json
{
  "source": "Kernel",
  "source_type": "kernel",
  "additional_fields": {
    "kernel_event_type": "hardware",
    "kernel_timestamp": "[12345.678901]"
  }
}
```

### Network Logs
```json
{
  "source": "Network",
  "source_type": "network",
  "additional_fields": {
    "network_event_type": "interface_change",
    "interface": "eth0"
  }
}
```

### Application Logs
```json
{
  "source": "Application",
  "source_type": "application",
  "additional_fields": {
    "application_name": "apache2",
    "application_type": "web_server"
  }
}
```

### Security Logs
```json
{
  "source": "Security",
  "source_type": "security",
  "additional_fields": {
    "security_event_type": "firewall",
    "action": "blocked"
  }
}
```

### Service Logs
```json
{
  "source": "Service",
  "source_type": "service",
  "additional_fields": {
    "systemd_unit": "nginx.service",
    "service_action": "started"
  }
}
```

## Response Format

### Success Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": "success",
  "message": "Logs processed successfully",
  "processed": 100,
  "timestamp": "2025-06-19T20:30:45Z"
}
```

### Error Response
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "status": "error",
  "message": "Invalid log format",
  "error_code": "INVALID_FORMAT",
  "details": {
    "field": "timestamp",
    "issue": "Invalid ISO 8601 format"
  }
}
```

## Error Handling

The agent implements comprehensive error handling:

### Retry Logic
- **Max Retries**: 3 attempts by default
- **Retry Delay**: Exponential backoff (5s, 10s, 20s)
- **Timeout**: 30 seconds per request

### Offline Buffering
When the API is unavailable:
- Logs are buffered locally in `/var/log/linux-log-agent/api_buffer.json`
- Maximum buffer size: 10,000 logs (configurable)
- Automatic retry every 60 seconds
- Buffer persistence across service restarts

### Rate Limiting
```yaml
exlog_api:
  rate_limit:
    enabled: false
    requests_per_minute: 60
```

## Batch Processing

### Configuration
```yaml
exlog_api:
  batch_size: 100              # Logs per batch
  max_batch_wait_time: 5       # Seconds to wait for batch completion
```

### Behavior
- Logs are collected into batches for efficient transmission
- Batches are sent when `batch_size` is reached or `max_batch_wait_time` expires
- Partial batches are sent during service shutdown

## Security Considerations

### API Key Management
- Store API keys securely in configuration files
- Use environment variables for sensitive deployments
- Rotate API keys regularly
- Monitor for unauthorized API usage

### Network Security
- Use HTTPS in production environments
- Implement proper firewall rules
- Consider VPN or private networks for sensitive data
- Monitor network traffic for anomalies

### Data Privacy
- Configure `include_raw_data: false` to exclude original log content
- Implement log filtering for sensitive information
- Consider data retention policies
- Ensure compliance with privacy regulations

## Monitoring and Debugging

### API Client Logs
```bash
# Monitor API communication
sudo tail -f /var/log/linux-log-agent/errors.log | grep -i api

# Check successful transmissions
sudo grep "Sent.*logs to API" /var/log/linux-log-agent/agent.log

# Monitor buffer usage
sudo ls -la /var/log/linux-log-agent/api_buffer.json
```

### Performance Metrics
```bash
# Check API response times
sudo grep "API response time" /var/log/linux-log-agent/agent.log

# Monitor batch sizes
sudo grep "batch.*logs" /var/log/linux-log-agent/agent.log
```

### Testing API Connectivity
```bash
# Test basic connectivity
curl -I http://your-exlog-server:5000/api/v1/logs

# Test with authentication
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"logs":[{"log_id":"test","timestamp":"2025-06-19T20:30:45","source":"Test","source_type":"test","host":"test","log_level":"info","message":"Test message"}]}' \
     http://your-exlog-server:5000/api/v1/logs
```

## Configuration Examples

### High-Volume Environment
```yaml
exlog_api:
  enabled: true
  endpoint: "https://exlog.company.com/api/v1/logs"
  api_key: "${EXLOG_API_KEY}"
  batch_size: 500
  max_batch_wait_time: 2
  timeout: 60
  max_retries: 5
  retry_delay: 3
  connection_pool_size: 20
  offline_buffer:
    enabled: true
    max_size: 50000
```

### Low-Bandwidth Environment
```yaml
exlog_api:
  enabled: true
  endpoint: "http://exlog.local:5000/api/v1/logs"
  api_key: "local-api-key"
  batch_size: 25
  max_batch_wait_time: 10
  timeout: 120
  max_retries: 2
  rate_limit:
    enabled: true
    requests_per_minute: 30
```

### Development Environment
```yaml
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: "dev-api-key"
  batch_size: 10
  max_batch_wait_time: 1
  timeout: 10
  max_retries: 1
  offline_buffer:
    enabled: false
```

## Troubleshooting

### Common API Issues

**401 Unauthorized**
- Verify API key is correct
- Check API key format and encoding
- Ensure API key has proper permissions

**Connection Timeout**
- Check network connectivity
- Verify firewall rules
- Increase timeout value
- Check ExLog server status

**Rate Limiting (429)**
- Reduce batch size
- Increase batch wait time
- Enable rate limiting in configuration
- Spread load across multiple agents

**Large Payload (413)**
- Reduce batch size
- Check for unusually large log messages
- Implement message truncation if needed

---

For more information about the ExLog dashboard API, consult the ExLog documentation or contact your system administrator.
