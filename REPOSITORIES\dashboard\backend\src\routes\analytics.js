const express = require('express');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');
const analyticsService = require('../services/analyticsService');

const router = express.Router();

/**
 * @route   GET /api/v1/analytics/security
 * @desc    Get security posture analytics
 * @access  Private
 */
router.get('/security', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '30d' } = req.query;
  
  const data = await analyticsService.getSecurityPosture(timeRange);
  
  res.json({
    status: 'success',
    data,
  });
}));

/**
 * @route   GET /api/v1/analytics/incidents
 * @desc    Get incident analytics
 * @access  Private
 */
router.get('/incidents', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '30d' } = req.query;
  
  const data = await analyticsService.getIncidentAnalytics(timeRange);
  
  res.json({
    status: 'success',
    data,
  });
}));

/**
 * @route   GET /api/v1/analytics/threats
 * @desc    Get threat intelligence data
 * @access  Private
 */
router.get('/threats', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '30d' } = req.query;
  
  const endDate = new Date();
  const startDate = getStartDate(timeRange, endDate);
  
  const data = await analyticsService.getThreatIntelligence(startDate, endDate);
  
  res.json({
    status: 'success',
    data: {
      timeRange: { start: startDate, end: endDate },
      ...data,
    },
  });
}));

/**
 * @route   GET /api/v1/analytics/operations
 * @desc    Get operational metrics
 * @access  Private
 */
router.get('/operations', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '24h' } = req.query;
  
  const data = await analyticsService.getOperationalMetrics(timeRange);
  
  res.json({
    status: 'success',
    data,
  });
}));

/**
 * @route   GET /api/v1/analytics/compliance
 * @desc    Get compliance posture metrics
 * @access  Private
 */
router.get('/compliance', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { framework } = req.query;
  
  // TODO: Implement compliance analytics
  const data = {
    frameworks: [
      {
        name: 'PCI DSS',
        version: '4.0',
        complianceScore: 85,
        controlsTotal: 12,
        controlsPassed: 10,
        controlsFailed: 2,
        lastAssessment: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        nextAssessment: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'HIPAA',
        version: '2013',
        complianceScore: 92,
        controlsTotal: 18,
        controlsPassed: 17,
        controlsFailed: 1,
        lastAssessment: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        nextAssessment: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'SOC2',
        version: '2017',
        complianceScore: 78,
        controlsTotal: 15,
        controlsPassed: 12,
        controlsFailed: 3,
        lastAssessment: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
        nextAssessment: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      },
    ],
    overallScore: 85,
    auditReadiness: {
      daysUntilNextAudit: 30,
      evidenceCollectionProgress: 75,
      outstandingItems: 5,
    },
  };
  
  if (framework) {
    const selectedFramework = data.frameworks.find(f => 
      f.name.toLowerCase() === framework.toLowerCase()
    );
    
    if (!selectedFramework) {
      throw new AppError('Compliance framework not found', 404);
    }
    
    res.json({
      status: 'success',
      data: selectedFramework,
    });
  } else {
    res.json({
      status: 'success',
      data,
    });
  }
}));

/**
 * @route   GET /api/v1/analytics/dashboard-overview
 * @desc    Get overview data for analytics dashboard
 * @access  Private
 */
router.get('/dashboard-overview', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '24h' } = req.query;
  
  // Get all analytics data for dashboard
  const [securityPosture, incidentAnalytics, operationalMetrics] = await Promise.all([
    analyticsService.getSecurityPosture(timeRange),
    analyticsService.getIncidentAnalytics(timeRange),
    analyticsService.getOperationalMetrics(timeRange),
  ]);
  
  // Combine into dashboard overview
  const overview = {
    securityScore: securityPosture.securityScore,
    totalIncidents: incidentAnalytics.responseMetrics?.totalIncidents || 0,
    criticalIncidents: incidentAnalytics.incidentsBySeverity?.find(s => s._id === 'critical')?.count || 0,
    avgMTTR: incidentAnalytics.responseMetrics?.avgMTTR || 0,
    activeLogSources: operationalMetrics.sourceHealth?.length || 0,
    logVolume: operationalMetrics.sourceHealth?.reduce((sum, source) => sum + source.totalLogs, 0) || 0,
    timeRange: {
      start: securityPosture.timeRange.start,
      end: securityPosture.timeRange.end,
    },
  };
  
  res.json({
    status: 'success',
    data: overview,
  });
}));

/**
 * @route   GET /api/v1/analytics/trends
 * @desc    Get trending data for charts
 * @access  Private
 */
router.get('/trends', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { timeRange = '7d', metric = 'logs' } = req.query;
  
  // TODO: Implement trending analytics
  const data = {
    metric,
    timeRange,
    data: generateMockTrendData(timeRange, metric),
  };
  
  res.json({
    status: 'success',
    data,
  });
}));

// Helper functions
function getStartDate(timeRange, endDate) {
  const end = new Date(endDate);
  
  switch (timeRange) {
    case '1h':
      return new Date(end.getTime() - 60 * 60 * 1000);
    case '24h':
      return new Date(end.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    case '90d':
      return new Date(end.getTime() - 90 * 24 * 60 * 60 * 1000);
    default:
      return new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
}

function generateMockTrendData(timeRange, metric) {
  const points = timeRange === '1h' ? 12 : timeRange === '24h' ? 24 : 30;
  const data = [];
  
  for (let i = 0; i < points; i++) {
    const timestamp = new Date(Date.now() - (points - i) * (timeRange === '1h' ? 5 * 60 * 1000 : 60 * 60 * 1000));
    const value = Math.floor(Math.random() * 100) + 50;
    
    data.push({
      timestamp,
      value,
      label: timestamp.toISOString(),
    });
  }
  
  return data;
}

module.exports = router;
