# GitLab CI/CD Configuration for Dashboard Project - Simplified
# This configuration uses centralized templates from the cicd-templates project


# Include centralized templates
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'

# Project-specific variables
variables:
  NODE_VERSION: "18"
  PROJECT_NAME: "exlog-dashboard"

# Simple project validation
validate_dashboard_structure:
  stage: validate
  extends: .base_job
  image: node:18
  script:
    - echo "Validating dashboard project structure..."
    - ls -la
    - echo "✓ Found package.json" && test -f package.json
    - echo "✓ Found frontend directory" && test -d frontend
    - echo "✓ Found backend directory" && test -d backend
    - echo "Dashboard structure validation passed"
