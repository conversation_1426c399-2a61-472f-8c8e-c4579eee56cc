// Script to add API key to system user for testing
db = db.getSiblingDB('exlog');

const result = db.users.updateOne(
  { username: 'system' },
  {
    $push: {
      apiKeys: {
        key: 'test-api-key-12345',
        name: 'Test Key for Alert Testing',
        permissions: ['ingest_logs'],
        isActive: true,
        createdAt: new Date(),
        lastUsed: null
      }
    }
  }
);

print('API key added:', JSON.stringify(result));

// Verify the key was added
const user = db.users.findOne({ username: 'system' }, { apiKeys: 1 });
print('System user API keys:', JSON.stringify(user.apiKeys, null, 2));
