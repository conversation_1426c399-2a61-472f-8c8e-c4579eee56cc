# ExLog High-Level Architecture

```mermaid
graph TB
    %% External Systems
    Users[Security Analysts & Administrators]
    LogSources[Log Sources\nWindows & Linux Systems]
    
    %% Main Components
    subgraph "ExLog System"
        subgraph "Linux Agent"
            LCollectors[Log Collectors]
            LProcessor[Log Processor]
            LTransport[API Client]
        end
        
        subgraph "Windows Agent"
            WCollectors[Log Collectors]
            WProcessor[Log Processor]
            WTransport[API Client]
        end
        
        subgraph "Infrastructure"
            Nginx[Nginx Reverse Proxy]
        end
        
        subgraph "Dashboard Frontend"
            UI[React UI]
            StateManager[Redux Store]
            APIClient[API Client]
        end
        
        subgraph "Backend Services"
            APIServer[Express API Server]
            WSServer[WebSocket Server]
            LogEngine[Log Processing Engine]
            AlertEngine[Alert Engine]
        end
        
        subgraph "Data Storage"
            MongoDB[(MongoDB)]
        end
    end
    
    %% Connections
    LogSources -->|Raw Logs| LCollectors
    LogSources -->|Raw Logs| WCollectors
    
    LCollectors --> LProcessor
    LProcessor --> LTransport
    
    WCollectors --> WProcessor
    WProcessor --> WTransport
    
    LTransport -->|HTTP/JSON| Nginx
    WTransport -->|HTTP/JSON| Nginx
    
    Users -->|Web Browser| Nginx
    
    Nginx -->|Proxy| UI
    UI <--> StateManager
    StateManager <--> APIClient
    
    APIClient -->|HTTP/REST| Nginx
    Nginx -->|Proxy| APIServer
    
    APIClient <-->|WebSocket| Nginx
    Nginx <-->|Proxy| WSServer
    
    APIServer <--> LogEngine
    APIServer <--> AlertEngine
    WSServer <--> AlertEngine
    
    LogEngine <--> MongoDB
    AlertEngine <--> MongoDB
    APIServer <--> MongoDB
    WSServer <--> MongoDB
    
    %% Styling
    classDef agent fill:#d0e0ff,stroke:#0066cc,stroke-width:2px
    classDef frontend fill:#ffe6cc,stroke:#ff9933,stroke-width:2px
    classDef backend fill:#d5e8d4,stroke:#82b366,stroke-width:2px
    classDef database fill:#e1d5e7,stroke:#9673a6,stroke-width:2px
    classDef infrastructure fill:#fff2cc,stroke:#d6b656,stroke-width:2px
    classDef external fill:#f5f5f5,stroke:#666666,stroke-width:1px
    
    class LCollectors,LProcessor,LTransport,WCollectors,WProcessor,WTransport agent
    class UI,StateManager,APIClient frontend
    class APIServer,WSServer,LogEngine,AlertEngine backend
    class MongoDB database
    class Nginx infrastructure
    class Users,LogSources external
```

## Component Descriptions

### Log Sources
- **Windows & Linux Systems**: The origin points of log data, including servers, workstations, and network devices.

### Agent Components
- **Log Collectors**: Specialized modules that gather logs from various sources (event logs, syslog, application logs, etc.)
- **Log Processor**: Handles parsing, normalization, and standardization of raw logs into a consistent JSON format
- **API Client**: Manages secure transmission of processed logs to the backend services

### Infrastructure
- **Nginx Reverse Proxy**: Routes traffic between clients and services, handles load balancing, SSL termination, and serves as the entry point for all web traffic

### Dashboard Frontend
- **React UI**: User interface built with React and Material-UI, providing visualizations and interactive controls
- **Redux Store**: State management system that maintains application data and UI state
- **API Client**: Handles communication with backend services via REST API and WebSocket connections

### Backend Services
- **Express API Server**: RESTful API providing endpoints for log ingestion, queries, user management, and configuration
- **WebSocket Server**: Enables real-time updates and notifications to the frontend
- **Log Processing Engine**: Handles incoming logs, performs additional processing, and manages storage
- **Alert Engine**: Evaluates logs against alert rules and generates notifications for security events

### Data Storage
- **MongoDB**: Central database storing logs, user data, configuration, and system metadata

## Communication Flows

1. **Log Collection**: Log sources generate raw logs that are collected by agent components
2. **Log Processing**: Agents standardize and batch logs before secure transmission
3. **Log Ingestion**: Backend receives logs via HTTP/JSON API calls through Nginx
4. **User Interaction**: Analysts access the system through Nginx to the React frontend
5. **Data Retrieval**: Frontend requests data from backend via REST API through Nginx
6. **Real-time Updates**: WebSocket connections through Nginx provide live updates to the dashboard
7. **Alert Generation**: Backend evaluates logs against rules and generates alerts
8. **Persistent Storage**: All system data is stored in and retrieved from MongoDB
