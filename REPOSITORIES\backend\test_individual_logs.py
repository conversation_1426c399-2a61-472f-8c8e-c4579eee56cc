#!/usr/bin/env python3
"""
Test individual logs from standardized_logs.json to identify validation issues.
"""

import json
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    import requests
    from config.config_manager import ConfigManager
    from utils.simple_api_client import SimpleExLogAPIClient
except ImportError as e:
    print(f"Error importing: {e}")
    sys.exit(1)


def test_individual_log(log_data, log_number):
    """Test a single log entry."""
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        endpoint = api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        api_key = api_config.get('api_key', '')
        
        # Create API client for validation
        client = SimpleExLogAPIClient(api_config)
        
        # Validate the log
        validated_log = client._validate_and_fix_log(log_data)
        
        if not validated_log:
            print(f"❌ Log {log_number}: Failed validation")
            return False
        
        # Prepare payload
        payload = {"logs": [validated_log]}
        
        # Send request
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
        
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        
        if response.status_code in [200, 201]:
            print(f"✅ Log {log_number}: SUCCESS")
            return True
        else:
            print(f"❌ Log {log_number}: FAILED - {response.status_code}")
            print(f"   Response: {response.text}")
            print(f"   Log ID: {validated_log.get('log_id', 'unknown')}")
            print(f"   Source: {validated_log.get('source', 'unknown')}")
            print(f"   Message: {validated_log.get('message', 'unknown')[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ Log {log_number}: ERROR - {e}")
        return False


def main():
    """Test individual logs from standardized_logs.json."""
    print("Testing Individual Logs from standardized_logs.json")
    print("=" * 60)
    
    # Read logs from file
    logs_file = Path("logs/standardized_logs.json")
    if not logs_file.exists():
        print("❌ logs/standardized_logs.json not found")
        return
    
    logs = []
    with open(logs_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line:
                try:
                    log_data = json.loads(line)
                    logs.append((line_num, log_data))
                except json.JSONDecodeError as e:
                    print(f"❌ Line {line_num}: Invalid JSON - {e}")
    
    print(f"Found {len(logs)} logs to test")
    print()
    
    # Test first 50 logs to identify patterns
    success_count = 0
    failure_count = 0
    
    for i, (line_num, log_data) in enumerate(logs[:50]):
        if test_individual_log(log_data, i + 1):
            success_count += 1
        else:
            failure_count += 1
        
        # Add a small delay to avoid overwhelming the API
        import time
        time.sleep(0.1)
    
    print()
    print("=" * 60)
    print(f"Results: {success_count} successful, {failure_count} failed")
    
    if failure_count > 0:
        print("\n🔍 Some logs failed validation. Check the error messages above to identify patterns.")
        print("Common issues might be:")
        print("- Invalid timestamp format")
        print("- Invalid log_level values")
        print("- Invalid source_type values")
        print("- Very long message content")
        print("- Invalid characters in log_id")
    else:
        print("\n🎉 All tested logs passed validation!")


if __name__ == "__main__":
    main()
