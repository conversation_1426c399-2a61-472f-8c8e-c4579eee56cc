const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * Authentication middleware to verify JWT tokens
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Access token is required',
      });
    }

    // Verify the token
    const decoded = jwt.verify(token, config.jwt.secret);

    // Get user from database
    const user = await User.findById(decoded.userId).select('-password');

    if (!user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found',
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is not active',
      });
    }

    // Validate session if session ID is present
    if (decoded.sessionId) {
      const session = user.sessions.find(s => s.sessionId === decoded.sessionId);

      // Check if session exists and is active
      if (!session) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Session not found',
        });
      }

      if (!session.isActive) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Session has been terminated',
        });
      }

      // Update session activity
      session.lastActivity = new Date();
      await user.save();
    }

    // Add user to request object
    req.user = user;
    req.userId = user._id;
    req.sessionId = decoded.sessionId;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid token',
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token expired',
      });
    }

    logger.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed',
    });
  }
};

/**
 * Authorization middleware to check user permissions
 */
const authorize = (requiredPermissions = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    // Get user's effective permissions
    const userPermissions = req.user.getEffectivePermissions();
    
    // Check if user has required permissions
    const hasPermission = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions',
        required: requiredPermissions,
        userPermissions,
      });
    }

    next();
  };
};

/**
 * Role-based authorization middleware
 */
const authorizeRoles = (allowedRoles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied for your role',
        userRole: req.user.role,
        allowedRoles,
      });
    }

    next();
  };
};

/**
 * API Key authentication middleware for agents
 */
const authenticateApiKey = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];

    if (!apiKey) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'API key is required',
      });
    }

    // Find user with this API key
    const user = await User.findOne({
      'apiKeys.key': apiKey,
      'apiKeys.isActive': true,
    });

    if (!user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid API key',
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is not active',
      });
    }

    // Update last used timestamp for the API key
    const apiKeyObj = user.apiKeys.find(key => key.key === apiKey);
    if (apiKeyObj) {
      apiKeyObj.lastUsed = new Date();
      await user.save();
    }

    // Add user to request object
    req.user = user;
    req.userId = user._id;
    req.apiKey = apiKey;
    
    next();
  } catch (error) {
    logger.error('API key authentication error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed',
    });
  }
};

/**
 * Optional authentication middleware (allows both authenticated and unauthenticated requests)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && user.status === 'active') {
        req.user = user;
        req.userId = user._id;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

/**
 * Combined authentication middleware (supports both JWT and API key)
 */
const authenticate = async (req, res, next) => {
  try {
    // Try JWT authentication first
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const decoded = jwt.verify(token, config.jwt.secret);
        const user = await User.findById(decoded.userId).select('-password');

        if (user && user.status === 'active') {
          req.user = user;
          req.userId = user._id;
          return next();
        }
      } catch (jwtError) {
        // JWT failed, try API key
      }
    }

    // Try API key authentication
    const apiKey = req.headers['x-api-key'];
    if (apiKey) {
      const user = await User.findOne({
        'apiKeys.key': apiKey,
        'apiKeys.isActive': true,
      });

      if (user && user.status === 'active') {
        // Update last used timestamp for the API key
        const apiKeyObj = user.apiKeys.find(key => key.key === apiKey);
        if (apiKeyObj) {
          apiKeyObj.lastUsed = new Date();
          await user.save();
        }

        req.user = user;
        req.userId = user._id;
        req.apiKey = apiKey;
        return next();
      }
    }

    // No valid authentication found
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Access token or API key is required',
    });

  } catch (error) {
    logger.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed',
    });
  }
};

/**
 * Middleware to check if user owns the resource or has admin privileges
 */
const authorizeOwnerOrAdmin = (resourceUserIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    const isOwner = resourceUserId && resourceUserId.toString() === req.userId.toString();
    const isAdmin = req.user.role === 'admin';

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied. You can only access your own resources or need admin privileges.',
      });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  authenticate,
  authorize,
  authorizeRoles,
  authenticateApiKey,
  optionalAuth,
  authorizeOwnerOrAdmin,
};
