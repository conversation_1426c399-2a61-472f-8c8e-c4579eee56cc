# Example GitLab CI/CD Configuration for Python Agent Projects
# This example shows how to use the centralized templates for a Python logging agent

# Include centralized templates
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/python.yml'
      - 'templates/security-simple.yml'

# Project-specific variables
variables:
  # Python configuration
  PYTHON_VERSION: "3.9"
  PYTHONPATH: "$CI_PROJECT_DIR"
  
  # Project identification
  PROJECT_NAME: "example-python-agent"
  PROJECT_TYPE: "python-agent"
  
  # Quality gates
  COVERAGE_THRESHOLD: "80"
  QUALITY_GATE_ENABLED: "true"
  
  # Security scanning
  SECURITY_SCAN_ENABLED: "true"
  
  # Development tools
  INSTALL_DEV_DEPS: "true"

# Override cache for this project
cache:
  key: 
    files:
      - requirements.txt
      - requirements-dev.txt
  paths:
    - .cache/pip/
    - .pytest_cache/
    - .mypy_cache/

# Custom validation specific to agent projects
validate_agent_structure:
  stage: validate
  extends: 
    - .base_job
    - .retry_policy
  image: python:3.9
  script:
    - echo "Validating Python agent project structure..."
    - |
      # Check for agent-specific files
      agent_files=(
        "main.py"
        "requirements.txt"
        "setup.py"
        "config/default_config.yaml"
        "logging_agent/__init__.py"
        "log_standardizer/__init__.py"
      )
      
      for file in "${agent_files[@]}"; do
        if [[ ! -f "$file" ]]; then
          echo "❌ Missing required file: $file"
          exit 1
        else
          echo "✅ Found: $file"
        fi
      done
    - |
      # Check for agent-specific directories
      agent_dirs=(
        "config"
        "logging_agent"
        "log_standardizer"
        "utils"
        "tests"
      )
      
      for dir in "${agent_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
          echo "❌ Missing required directory: $dir"
          exit 1
        else
          echo "✅ Found directory: $dir"
        fi
      done
    - echo "✅ Agent structure validation passed"

# Configuration validation
validate_agent_config:
  stage: validate
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["python_setup"]
  script:
    - echo "Validating agent configuration..."
    - python main.py validate-config
    - echo "✅ Configuration validation passed"

# Agent functionality tests
test_agent_core:
  stage: test
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["python_setup"]
  script:
    - echo "Testing core agent functionality..."
    - python main.py test --dry-run
    - echo "✅ Core functionality tests passed"
  artifacts:
    paths:
      - logs/test_*.log
    expire_in: 1 day
    when: always

# Log standardizer tests
test_log_standardizer:
  stage: test
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["python_setup"]
  script:
    - echo "Testing log standardizer..."
    - python -m pytest tests/test_standardizer.py -v --junitxml=reports/standardizer.xml
  artifacts:
    reports:
      junit: reports/standardizer.xml
    paths:
      - reports/
    expire_in: 1 week

# Performance benchmarks
test_performance:
  stage: test
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["python_setup"]
  script:
    - echo "Running performance benchmarks..."
    - pip install pytest-benchmark
    - python -m pytest tests/performance/ --benchmark-only --benchmark-json=reports/benchmark.json
  artifacts:
    paths:
      - reports/benchmark.json
    expire_in: 1 week
  allow_failure: true

# Agent-specific security checks
security_agent_config:
  stage: security
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["python_setup"]
  script:
    - echo "Checking agent configuration security..."
    - |
      # Check for hardcoded credentials
      if grep -r "password\|secret\|key" config/ --include="*.yaml" --include="*.yml"; then
        echo "⚠️  Warning: Potential hardcoded credentials found in config files"
      fi
    - |
      # Check file permissions in config
      python -c "
      import yaml
      import os
      
      config_files = ['config/default_config.yaml']
      for config_file in config_files:
          if os.path.exists(config_file):
              with open(config_file, 'r') as f:
                  config = yaml.safe_load(f)
                  print(f'✅ Configuration file {config_file} is valid YAML')
          else:
              print(f'❌ Configuration file {config_file} not found')
      "
    - echo "✅ Agent configuration security check completed"

# Build agent package
build_agent_package:
  stage: build
  extends:
    - .python_base
    - .build_template
  needs: 
    - job: python_test_unit
      artifacts: false
    - job: test_agent_core
      artifacts: false
    - job: python_lint_flake8
      artifacts: false
  script:
    - echo "Building agent package..."
    - python setup.py sdist bdist_wheel
    - echo "✅ Agent package built successfully"
    - ls -la dist/
  artifacts:
    paths:
      - dist/
      - build/
    expire_in: 2 weeks

# Create agent installer
create_agent_installer:
  stage: build
  extends:
    - .python_base
    - .base_job
    - .retry_policy
  needs: ["build_agent_package"]
  script:
    - echo "Creating agent installer package..."
    - mkdir -p installer
    - cp -r dist/ installer/
    - cp -r config/ installer/
    - cp README.md installer/
    - |
      # Create installation script
      cat > installer/install.sh << 'EOF'
      #!/bin/bash
      echo "Installing Python Logging Agent..."
      pip install dist/*.whl
      echo "Agent installation completed"
      EOF
    - chmod +x installer/install.sh
    - echo "✅ Agent installer created"
  artifacts:
    paths:
      - installer/
    expire_in: 2 weeks

# Deploy to test environment
deploy_agent_test:
  stage: deploy
  extends: .deploy_template
  variables:
    ENVIRONMENT_NAME: "test"
    ENVIRONMENT_URL: "https://test-agent.example.com"
  script:
    - echo "Deploying agent to test environment..."
    - echo "This would deploy the agent to test servers"
    - echo "✅ Test deployment completed"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.*$/
      when: manual

# Deploy to production environment
deploy_agent_production:
  stage: deploy
  extends: .deploy_template
  variables:
    ENVIRONMENT_NAME: "production"
    ENVIRONMENT_URL: "https://agent.example.com"
  script:
    - echo "Deploying agent to production environment..."
    - echo "This would deploy the agent to production servers"
    - echo "✅ Production deployment completed"
  rules:
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
      when: manual
  environment:
    name: production
    url: https://agent.example.com

# Custom cleanup job
cleanup_test_data:
  stage: .post
  extends:
    - .base_job
  script:
    - echo "Cleaning up test data..."
    - rm -rf logs/test_*.log
    - echo "✅ Cleanup completed"
  when: always
  allow_failure: true
