"""
Configuration Manager for Linux Log Collection Agent

This module handles loading, validation, and management of configuration
settings for the Linux log collection agent.
"""

import logging
import os
import platform
from pathlib import Path
from typing import Dict, Any, Optional, List

try:
    import yaml
except ImportError:
    yaml = None


class ConfigManager:
    """Manages configuration loading and validation for the Linux logging agent."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file. If None, uses default.
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        
    def _get_default_config_path(self) -> str:
        """Get the default configuration file path for Linux."""
        # Check for system-wide config first
        system_config = "/etc/linux-log-agent/config.yaml"
        if os.path.exists(system_config):
            return system_config
        
        # Fall back to local config
        current_dir = Path(__file__).parent
        return str(current_dir / "default_config.yaml")
        
    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the YAML file.
        
        Returns:
            Dictionary containing the configuration settings.
            
        Raises:
            FileNotFoundError: If the configuration file doesn't exist.
            yaml.YAMLError: If the YAML file is malformed.
        """
        if not yaml:
            raise ImportError("PyYAML is required. Install with: pip3 install PyYAML")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
                
            self._validate_config()
            self._adapt_for_distribution()
            self.logger.info(f"Configuration loaded from {self.config_path}")
            return self.config
            
        except FileNotFoundError:
            self.logger.error(f"Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            self.logger.error(f"Error parsing YAML configuration: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error loading configuration: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Validate the loaded configuration."""
        if not isinstance(self.config, dict):
            raise ValueError("Configuration must be a dictionary")
        
        # Validate required sections
        required_sections = ['collection', 'exlog_api', 'general']
        for section in required_sections:
            if section not in self.config:
                self.logger.warning(f"Missing configuration section: {section}")
                self.config[section] = {}
        
        # Validate ExLog API configuration
        api_config = self.config.get('exlog_api', {})
        if api_config.get('enabled', False):
            required_api_fields = ['endpoint', 'api_key']
            for field in required_api_fields:
                if not api_config.get(field):
                    raise ValueError(f"ExLog API is enabled but missing required field: {field}")
    
    def _adapt_for_distribution(self) -> None:
        """Adapt configuration for the current Linux distribution."""
        distro = self._detect_distribution()
        self.logger.info(f"Detected Linux distribution: {distro}")
        
        # Adapt log paths based on distribution
        collection_config = self.config.get('collection', {})
        
        # Syslog paths
        syslog_config = collection_config.get('syslog', {})
        if not syslog_config.get('paths'):
            syslog_config['paths'] = self._get_syslog_paths(distro)
        
        # Auth log paths
        auth_config = collection_config.get('auth_logs', {})
        if not auth_config.get('paths'):
            auth_config['paths'] = self._get_auth_log_paths(distro)
        
        # Kernel log paths
        kernel_config = collection_config.get('kernel_logs', {})
        if not kernel_config.get('paths'):
            kernel_config['paths'] = self._get_kernel_log_paths(distro)
    
    def _detect_distribution(self) -> str:
        """Detect the Linux distribution."""
        try:
            # Try to read /etc/os-release
            if os.path.exists('/etc/os-release'):
                with open('/etc/os-release', 'r') as f:
                    for line in f:
                        if line.startswith('ID='):
                            return line.split('=')[1].strip().strip('"')
            
            # Fall back to platform detection
            return platform.system().lower()
            
        except Exception:
            return 'unknown'
    
    def _get_syslog_paths(self, distro: str) -> List[str]:
        """Get syslog paths for the distribution."""
        paths = []
        
        # Common paths to check
        common_paths = [
            '/var/log/syslog',      # Ubuntu, Debian
            '/var/log/messages',    # CentOS, RHEL, Fedora
            '/var/log/system.log'   # Some distributions
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                paths.append(path)
        
        return paths
    
    def _get_auth_log_paths(self, distro: str) -> List[str]:
        """Get authentication log paths for the distribution."""
        paths = []
        
        # Common auth log paths
        common_paths = [
            '/var/log/auth.log',    # Ubuntu, Debian
            '/var/log/secure',      # CentOS, RHEL, Fedora
            '/var/log/authlog'      # Some distributions
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                paths.append(path)
        
        return paths
    
    def _get_kernel_log_paths(self, distro: str) -> List[str]:
        """Get kernel log paths for the distribution."""
        paths = []
        
        # Common kernel log paths
        common_paths = [
            '/var/log/kern.log',    # Ubuntu, Debian
            '/var/log/kernel',      # Some distributions
            '/var/log/dmesg'        # Boot messages
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                paths.append(path)
        
        return paths
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'exlog_api.endpoint')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
        """
        keys = key.split('.')
        config_ref = self.config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config_ref:
                config_ref[k] = {}
            config_ref = config_ref[k]
            
        # Set the value
        config_ref[keys[-1]] = value
    
    def save_config(self, path: Optional[str] = None) -> None:
        """
        Save the current configuration to a file.
        
        Args:
            path: Path to save the configuration. If None, uses current config_path.
        """
        if not yaml:
            raise ImportError("PyYAML is required for saving configuration")
        
        save_path = path or self.config_path
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, indent=2)
            self.logger.info(f"Configuration saved to {save_path}")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            raise
    
    def reload_config(self) -> Dict[str, Any]:
        """
        Reload configuration from the file.
        
        Returns:
            Updated configuration dictionary
        """
        return self.load_config()
    
    def get_log_paths(self) -> Dict[str, List[str]]:
        """
        Get all configured log paths organized by type.
        
        Returns:
            Dictionary mapping log types to their file paths
        """
        collection_config = self.config.get('collection', {})
        
        return {
            'syslog': collection_config.get('syslog', {}).get('paths', []),
            'auth': collection_config.get('auth_logs', {}).get('paths', []),
            'kernel': collection_config.get('kernel_logs', {}).get('paths', []),
            'application': collection_config.get('application_logs', {}).get('paths', [])
        }
