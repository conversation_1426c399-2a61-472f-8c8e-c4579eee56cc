const axios = require('axios');
const os = require('os');

// Get local IP addresses
function getLocalIPs() {
  const interfaces = os.networkInterfaces();
  const ips = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        ips.push(interface.address);
      }
    }
  }
  
  return ips;
}

async function testEndpoint(url, description, options = {}) {
  console.log(`\n🔍 Testing: ${description}`);
  console.log(`   URL: ${url}`);
  
  try {
    const response = await axios.get(url, {
      timeout: 5000,
      ...options
    });
    
    console.log(`   ✅ Success - Status: ${response.status}`);
    if (response.data && typeof response.data === 'object') {
      if (response.data.status) {
        console.log(`   📊 Response: ${response.data.status}`);
      }
      if (response.data.message) {
        console.log(`   💬 Message: ${response.data.message}`);
      }
    }
    return true;
  } catch (error) {
    console.log(`   ❌ Failed`);
    if (error.response) {
      console.log(`   📊 Status: ${error.response.status}`);
      console.log(`   💬 Error: ${error.response.data?.message || error.response.statusText}`);
    } else if (error.code) {
      console.log(`   🔌 Network Error: ${error.code} - ${error.message}`);
    } else {
      console.log(`   ⚠️  Error: ${error.message}`);
    }
    return false;
  }
}

async function testLogin(baseUrl, description) {
  console.log(`\n🔐 Testing Login: ${description}`);
  console.log(`   URL: ${baseUrl}/api/v1/auth/login`);
  
  try {
    const response = await axios.post(`${baseUrl}/api/v1/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log(`   ✅ Login Success - Status: ${response.status}`);
    console.log(`   👤 User: ${response.data.data.user.email}`);
    console.log(`   🎭 Role: ${response.data.data.user.role}`);
    console.log(`   🎫 Token: ${response.data.data.token.substring(0, 20)}...`);
    return response.data.data.token;
  } catch (error) {
    console.log(`   ❌ Login Failed`);
    if (error.response) {
      console.log(`   📊 Status: ${error.response.status}`);
      console.log(`   💬 Error: ${error.response.data?.message || error.response.statusText}`);
      if (error.response.data) {
        console.log(`   📋 Details:`, JSON.stringify(error.response.data, null, 2));
      }
    } else if (error.code) {
      console.log(`   🔌 Network Error: ${error.code} - ${error.message}`);
    } else {
      console.log(`   ⚠️  Error: ${error.message}`);
    }
    return null;
  }
}

async function debugExternalAccess() {
  console.log('=== ExLog External Access Debug ===\n');
  
  // Get local IP addresses
  const localIPs = getLocalIPs();
  console.log('🌐 Local IP Addresses:');
  localIPs.forEach(ip => console.log(`   - ${ip}`));
  
  if (localIPs.length === 0) {
    console.log('❌ No external IP addresses found!');
    return;
  }
  
  // Test localhost first
  console.log('\n📍 Testing Localhost Access:');
  const localhostTests = [
    { url: 'http://localhost/health', desc: 'Nginx Health (Port 80)' },
    { url: 'http://localhost:5000/health', desc: 'Backend Health (Direct)' },
    { url: 'http://localhost/api/v1/auth/login', desc: 'API Login Endpoint (via Nginx)', method: 'OPTIONS' },
    { url: 'http://localhost:5000/api/v1/auth/login', desc: 'API Login Endpoint (Direct)', method: 'OPTIONS' },
  ];
  
  for (const test of localhostTests) {
    if (test.method === 'OPTIONS') {
      await testEndpoint(test.url, test.desc, { method: 'OPTIONS' });
    } else {
      await testEndpoint(test.url, test.desc);
    }
  }
  
  // Test login on localhost
  await testLogin('http://localhost', 'Localhost via Nginx');
  await testLogin('http://localhost:5000', 'Localhost Direct');
  
  // Test each IP address
  for (const ip of localIPs) {
    console.log(`\n📍 Testing IP Address: ${ip}`);
    
    const ipTests = [
      { url: `http://${ip}/health`, desc: 'Nginx Health (Port 80)' },
      { url: `http://${ip}:5000/health`, desc: 'Backend Health (Direct)' },
      { url: `http://${ip}/api/docs`, desc: 'API Documentation (via Nginx)' },
      { url: `http://${ip}:5000/api/docs`, desc: 'API Documentation (Direct)' },
      { url: `http://${ip}/api/v1/auth/login`, desc: 'API Login Endpoint (via Nginx)', method: 'OPTIONS' },
      { url: `http://${ip}:5000/api/v1/auth/login`, desc: 'API Login Endpoint (Direct)', method: 'OPTIONS' },
    ];
    
    for (const test of ipTests) {
      if (test.method === 'OPTIONS') {
        await testEndpoint(test.url, test.desc, { method: 'OPTIONS' });
      } else {
        await testEndpoint(test.url, test.desc);
      }
    }
    
    // Test login on this IP
    await testLogin(`http://${ip}`, `${ip} via Nginx`);
    await testLogin(`http://${ip}:5000`, `${ip} Direct`);
  }
  
  console.log('\n=== Summary ===');
  console.log('If external access is failing:');
  console.log('1. Check Windows Firewall settings');
  console.log('2. Verify Docker containers are running: docker-compose ps');
  console.log('3. Check if ports 80, 5000 are accessible from external devices');
  console.log('4. Try accessing from another device on the same network');
  console.log('5. Check router/network configuration');
  
  console.log('\n🔧 Troubleshooting Commands:');
  console.log('- Check firewall: Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*Docker*"}');
  console.log('- Check port binding: netstat -an | findstr ":80 :5000"');
  console.log('- Restart containers: docker-compose restart');
  console.log('- View logs: docker-compose logs backend nginx');
}

debugExternalAccess().catch(console.error);
